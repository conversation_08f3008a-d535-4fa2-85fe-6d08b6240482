
/* Avoid breaking parameter names, etc. in table cells. */
.doc-contents td code {
  word-break: normal !important;
}

/* No line break before first paragraph of descriptions. */
.doc-md-description,
.doc-md-description>p:first-child {
  display: inline;
}

/* No text transformation from Material for MkDocs for H5 headings. */
.md-typeset h5 .doc-object-name {
  text-transform: none;
}

/* Max width for docstring sections tables. */
.doc .md-typeset__table,
.doc .md-typeset__table table {
  display: table !important;
  width: 100%;
}

.doc .md-typeset__table tr {
  display: table-row;
}

/* Defaults in Spacy table style. */
.doc-param-default {
  float: right;
}

/* Parameter headings must be inline, not blocks. */
.doc-heading-parameter {
  display: inline;
}

/* Default font size for parameter headings. */
.md-typeset .doc-heading-parameter {
  font-size: inherit;
}

/* Prefer space on the right, not the left of parameter permalinks. */
.doc-heading-parameter .headerlink {
  margin-left: 0 !important;
  margin-right: 0.2rem;
}

/* Backward-compatibility: docstring section titles in bold. */
.doc-section-title {
  font-weight: bold;
}

/* Backlinks crumb separator. */
.doc-backlink-crumb {
  display: inline-flex;
  gap: .2rem;
  white-space: nowrap;
  align-items: center;
  vertical-align: middle;
}
.doc-backlink-crumb:not(:first-child)::before {
  background-color: var(--md-default-fg-color--lighter);
  content: "";
  display: inline;
  height: 1rem;
  --md-path-icon: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M8.59 16.58 13.17 12 8.59 7.41 10 6l6 6-6 6z"/></svg>');
  -webkit-mask-image: var(--md-path-icon);
  mask-image: var(--md-path-icon);
  width: 1rem;
}
.doc-backlink-crumb.last {
  font-weight: bold;
}

/* Symbols in Navigation and ToC. */
:root, :host,
[data-md-color-scheme="default"] {
  --doc-symbol-parameter-fg-color: #df50af;
  --doc-symbol-attribute-fg-color: #953800;
  --doc-symbol-function-fg-color: #8250df;
  --doc-symbol-method-fg-color: #8250df;
  --doc-symbol-class-fg-color: #0550ae;
  --doc-symbol-module-fg-color: #5cad0f;

  --doc-symbol-parameter-bg-color: #df50af1a;
  --doc-symbol-attribute-bg-color: #9538001a;
  --doc-symbol-function-bg-color: #8250df1a;
  --doc-symbol-method-bg-color: #8250df1a;
  --doc-symbol-class-bg-color: #0550ae1a;
  --doc-symbol-module-bg-color: #5cad0f1a;
}

[data-md-color-scheme="slate"] {
  --doc-symbol-parameter-fg-color: #ffa8cc;
  --doc-symbol-attribute-fg-color: #ffa657;
  --doc-symbol-function-fg-color: #d2a8ff;
  --doc-symbol-method-fg-color: #d2a8ff;
  --doc-symbol-class-fg-color: #79c0ff;
  --doc-symbol-module-fg-color: #baff79;

  --doc-symbol-parameter-bg-color: #ffa8cc1a;
  --doc-symbol-attribute-bg-color: #ffa6571a;
  --doc-symbol-function-bg-color: #d2a8ff1a;
  --doc-symbol-method-bg-color: #d2a8ff1a;
  --doc-symbol-class-bg-color: #79c0ff1a;
  --doc-symbol-module-bg-color: #baff791a;
}

code.doc-symbol {
  border-radius: .1rem;
  font-size: .85em;
  padding: 0 .3em;
  font-weight: bold;
}

code.doc-symbol-parameter,
a code.doc-symbol-parameter {
  color: var(--doc-symbol-parameter-fg-color);
  background-color: var(--doc-symbol-parameter-bg-color);
}

code.doc-symbol-parameter::after {
  content: "param";
}

code.doc-symbol-attribute,
a code.doc-symbol-attribute {
  color: var(--doc-symbol-attribute-fg-color);
  background-color: var(--doc-symbol-attribute-bg-color);
}

code.doc-symbol-attribute::after {
  content: "attr";
}

code.doc-symbol-function,
a code.doc-symbol-function {
  color: var(--doc-symbol-function-fg-color);
  background-color: var(--doc-symbol-function-bg-color);
}

code.doc-symbol-function::after {
  content: "func";
}

code.doc-symbol-method,
a code.doc-symbol-method {
  color: var(--doc-symbol-method-fg-color);
  background-color: var(--doc-symbol-method-bg-color);
}

code.doc-symbol-method::after {
  content: "meth";
}

code.doc-symbol-class,
a code.doc-symbol-class {
  color: var(--doc-symbol-class-fg-color);
  background-color: var(--doc-symbol-class-bg-color);
}

code.doc-symbol-class::after {
  content: "class";
}

code.doc-symbol-module,
a code.doc-symbol-module {
  color: var(--doc-symbol-module-fg-color);
  background-color: var(--doc-symbol-module-bg-color);
}

code.doc-symbol-module::after {
  content: "mod";
}

.doc-signature .autorefs {
  color: inherit;
  border-bottom: 1px dotted currentcolor;
}

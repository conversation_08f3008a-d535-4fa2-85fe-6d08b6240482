
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="A powerful, extensible, and minimally opinionated ASGI web framework for Python">
      
      
      
        <link rel="canonical" href="https://getserving.dev/reference/serv/cli/commands/">
      
      
        <link rel="prev" href="../">
      
      
        <link rel="next" href="../main/">
      
      
      <link rel="icon" href="../../../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>commands - Serv Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
      <link rel="stylesheet" href="../../../../assets/_mkdocstrings.css">
    
    <script>__md_scope=new URL("../../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#serv.cli.commands" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../../../.." title="Serv Documentation" class="md-header__button md-logo" aria-label="Serv Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            Serv Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              commands
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/your-org/serv" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81"/></svg>
  </div>
  <div class="md-source__repository">
    your-org/serv
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../../../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../../getting-started/installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../../guides/routing/" class="md-tabs__link">
          
  
  
  Guides

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../../" class="md-tabs__link">
          
  
  
  API Reference

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../../examples/basic-app.md" class="md-tabs__link">
          
  
  
  Examples

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../../../.." title="Serv Documentation" class="md-nav__button md-logo" aria-label="Serv Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    Serv Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/your-org/serv" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81"/></svg>
  </div>
  <div class="md-source__repository">
    your-org/serv
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../getting-started/installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../getting-started/quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../getting-started/first-app/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Your First App
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../getting-started/configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Guides
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Guides
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/routing/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Routing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/dependency-injection/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Dependency Injection
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/plugins/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugins
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/middleware/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Middleware
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/requests.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Request Handling
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/responses.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Response Building
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/error-handling.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Error Handling
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/templates.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Templates
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/forms.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Forms and File Uploads
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/events.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Events
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/testing.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Testing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../guides/deployment.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    API Reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            API Reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    
    
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4_1" checked>
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../../" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    serv
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1" id="__nav_4_1_label" tabindex="">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_4_1_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4_1">
            <span class="md-nav__icon md-icon"></span>
            serv
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../additional_context/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    additional_context
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../app/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    app
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1_3" >
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../../bundled/" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    bundled
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1_3" id="__nav_4_1_3_label" tabindex="0">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_4_1_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1_3">
            <span class="md-nav__icon md-icon"></span>
            bundled
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1_3_1" >
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../../bundled/plugins/" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    plugins
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1_3_1" id="__nav_4_1_3_1_label" tabindex="0">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="4" aria-labelledby="__nav_4_1_3_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1_3_1">
            <span class="md-nav__icon md-icon"></span>
            plugins
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1_3_1_1" >
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../../bundled/plugins/welcome/" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    welcome
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1_3_1_1" id="__nav_4_1_3_1_1_label" tabindex="0">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="5" aria-labelledby="__nav_4_1_3_1_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1_3_1_1">
            <span class="md-nav__icon md-icon"></span>
            welcome
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bundled/plugins/welcome/welcome/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    welcome
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
    
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4_1_4" checked>
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    cli
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1_4" id="__nav_4_1_4_label" tabindex="0">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_4_1_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4_1_4">
            <span class="md-nav__icon md-icon"></span>
            cli
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    commands
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    commands
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#serv.cli.commands" class="md-nav__link">
    <span class="md-ellipsis">
      commands
    </span>
  </a>
  
    <nav class="md-nav" aria-label="commands">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_get_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_get_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_set_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_set_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_show_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_show_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_validate_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_validate_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_entrypoint_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_entrypoint_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_middleware_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_middleware_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_route_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_route_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_dev_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_dev_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_disable_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_disable_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_enable_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_enable_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_init_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_init_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_launch_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_launch_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_list_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_list_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_shell_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_shell_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_test_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_test_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_validate_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_validate_plugin_command
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../main/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    main
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../parser/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    parser
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../utils/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    utils
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../config/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    config
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../exceptions/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    exceptions
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../injectors/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    injectors
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../multipart_parser/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    multipart_parser
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1_9" >
        
          
          <div class="md-nav__link md-nav__container">
            <a href="../../plugins/" class="md-nav__link ">
              
  
  
  <span class="md-ellipsis">
    plugins
    
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_4_1_9" id="__nav_4_1_9_label" tabindex="0">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_4_1_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1_9">
            <span class="md-nav__icon md-icon"></span>
            plugins
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../plugins/importer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    importer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../plugins/loader/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    loader
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../plugins/middleware/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    middleware
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../plugins/plugins/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    plugins
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../plugins/router_plugin/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    router_plugin
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../requests/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    requests
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../responses/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    responses
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../routes/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    routes
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../routing/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    routing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../serv/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    serv
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../examples/basic-app.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic App
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../examples/plugin-development.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Development
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../examples/advanced-routing.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Advanced Routing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../../examples/authentication.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Authentication
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#serv.cli.commands" class="md-nav__link">
    <span class="md-ellipsis">
      commands
    </span>
  </a>
  
    <nav class="md-nav" aria-label="commands">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_get_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_get_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_set_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_set_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_show_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_show_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_config_validate_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_config_validate_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_entrypoint_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_entrypoint_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_middleware_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_middleware_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_create_route_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_create_route_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_dev_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_dev_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_disable_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_disable_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_enable_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_enable_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_init_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_init_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_launch_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_launch_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_list_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_list_plugin_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_shell_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_shell_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_test_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_test_command
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#serv.cli.commands.handle_validate_plugin_command" class="md-nav__link">
    <span class="md-ellipsis">
      handle_validate_plugin_command
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


  <h1>commands</h1>

<div class="doc doc-object doc-module">



<h2 id="serv.cli.commands" class="doc doc-heading">
            <span class="doc doc-object-name doc-module-name">serv.cli.commands</span>


</h2>

    <div class="doc doc-contents first">

        <p>CLI command handlers.</p>
<p>This module contains all the command handlers for the Serv CLI.</p>









  <div class="doc doc-children">









<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_config_get_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_config_get_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_config_get_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'config get' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1477">1477</a></span>
<span class="normal"><a href="#__codelineno-0-1478">1478</a></span>
<span class="normal"><a href="#__codelineno-0-1479">1479</a></span>
<span class="normal"><a href="#__codelineno-0-1480">1480</a></span>
<span class="normal"><a href="#__codelineno-0-1481">1481</a></span>
<span class="normal"><a href="#__codelineno-0-1482">1482</a></span>
<span class="normal"><a href="#__codelineno-0-1483">1483</a></span>
<span class="normal"><a href="#__codelineno-0-1484">1484</a></span>
<span class="normal"><a href="#__codelineno-0-1485">1485</a></span>
<span class="normal"><a href="#__codelineno-0-1486">1486</a></span>
<span class="normal"><a href="#__codelineno-0-1487">1487</a></span>
<span class="normal"><a href="#__codelineno-0-1488">1488</a></span>
<span class="normal"><a href="#__codelineno-0-1489">1489</a></span>
<span class="normal"><a href="#__codelineno-0-1490">1490</a></span>
<span class="normal"><a href="#__codelineno-0-1491">1491</a></span>
<span class="normal"><a href="#__codelineno-0-1492">1492</a></span>
<span class="normal"><a href="#__codelineno-0-1493">1493</a></span>
<span class="normal"><a href="#__codelineno-0-1494">1494</a></span>
<span class="normal"><a href="#__codelineno-0-1495">1495</a></span>
<span class="normal"><a href="#__codelineno-0-1496">1496</a></span>
<span class="normal"><a href="#__codelineno-0-1497">1497</a></span>
<span class="normal"><a href="#__codelineno-0-1498">1498</a></span>
<span class="normal"><a href="#__codelineno-0-1499">1499</a></span>
<span class="normal"><a href="#__codelineno-0-1500">1500</a></span>
<span class="normal"><a href="#__codelineno-0-1501">1501</a></span>
<span class="normal"><a href="#__codelineno-0-1502">1502</a></span>
<span class="normal"><a href="#__codelineno-0-1503">1503</a></span>
<span class="normal"><a href="#__codelineno-0-1504">1504</a></span>
<span class="normal"><a href="#__codelineno-0-1505">1505</a></span>
<span class="normal"><a href="#__codelineno-0-1506">1506</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1477"><a id="__codelineno-0-1477" name="__codelineno-0-1477"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_config_get_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1478"><a id="__codelineno-0-1478" name="__codelineno-0-1478"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;config get&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1479"><a id="__codelineno-0-1479" name="__codelineno-0-1479"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Config get command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1480"><a id="__codelineno-0-1480" name="__codelineno-0-1480"></a>
</span><span id="__span-0-1481"><a id="__codelineno-0-1481" name="__codelineno-0-1481"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-1482"><a id="__codelineno-0-1482" name="__codelineno-0-1482"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1483"><a id="__codelineno-0-1483" name="__codelineno-0-1483"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1484"><a id="__codelineno-0-1484" name="__codelineno-0-1484"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1485"><a id="__codelineno-0-1485" name="__codelineno-0-1485"></a>
</span><span id="__span-0-1486"><a id="__codelineno-0-1486" name="__codelineno-0-1486"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1487"><a id="__codelineno-0-1487" name="__codelineno-0-1487"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1488"><a id="__codelineno-0-1488" name="__codelineno-0-1488"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-1489"><a id="__codelineno-0-1489" name="__codelineno-0-1489"></a>
</span><span id="__span-0-1490"><a id="__codelineno-0-1490" name="__codelineno-0-1490"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1491"><a id="__codelineno-0-1491" name="__codelineno-0-1491"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ Configuration file is empty&quot;</span><span class="p">)</span>
</span><span id="__span-0-1492"><a id="__codelineno-0-1492" name="__codelineno-0-1492"></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1493"><a id="__codelineno-0-1493" name="__codelineno-0-1493"></a>
</span><span id="__span-0-1494"><a id="__codelineno-0-1494" name="__codelineno-0-1494"></a>        <span class="n">value</span> <span class="o">=</span> <span class="n">_get_config_value</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">key</span><span class="p">)</span>
</span><span id="__span-0-1495"><a id="__codelineno-0-1495" name="__codelineno-0-1495"></a>
</span><span id="__span-0-1496"><a id="__codelineno-0-1496" name="__codelineno-0-1496"></a>        <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="__span-0-1497"><a id="__codelineno-0-1497" name="__codelineno-0-1497"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Key &#39;</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">key</span><span class="si">}</span><span class="s2">&#39; not found in configuration&quot;</span><span class="p">)</span>
</span><span id="__span-0-1498"><a id="__codelineno-0-1498" name="__codelineno-0-1498"></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1499"><a id="__codelineno-0-1499" name="__codelineno-0-1499"></a>
</span><span id="__span-0-1500"><a id="__codelineno-0-1500" name="__codelineno-0-1500"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;🔑 </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">key</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1501"><a id="__codelineno-0-1501" name="__codelineno-0-1501"></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1502"><a id="__codelineno-0-1502" name="__codelineno-0-1502"></a>
</span><span id="__span-0-1503"><a id="__codelineno-0-1503" name="__codelineno-0-1503"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1504"><a id="__codelineno-0-1504" name="__codelineno-0-1504"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error reading configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1505"><a id="__codelineno-0-1505" name="__codelineno-0-1505"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Error reading configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1506"><a id="__codelineno-0-1506" name="__codelineno-0-1506"></a>        <span class="k">return</span> <span class="kc">False</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_config_set_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_config_set_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_config_set_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'config set' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1509">1509</a></span>
<span class="normal"><a href="#__codelineno-0-1510">1510</a></span>
<span class="normal"><a href="#__codelineno-0-1511">1511</a></span>
<span class="normal"><a href="#__codelineno-0-1512">1512</a></span>
<span class="normal"><a href="#__codelineno-0-1513">1513</a></span>
<span class="normal"><a href="#__codelineno-0-1514">1514</a></span>
<span class="normal"><a href="#__codelineno-0-1515">1515</a></span>
<span class="normal"><a href="#__codelineno-0-1516">1516</a></span>
<span class="normal"><a href="#__codelineno-0-1517">1517</a></span>
<span class="normal"><a href="#__codelineno-0-1518">1518</a></span>
<span class="normal"><a href="#__codelineno-0-1519">1519</a></span>
<span class="normal"><a href="#__codelineno-0-1520">1520</a></span>
<span class="normal"><a href="#__codelineno-0-1521">1521</a></span>
<span class="normal"><a href="#__codelineno-0-1522">1522</a></span>
<span class="normal"><a href="#__codelineno-0-1523">1523</a></span>
<span class="normal"><a href="#__codelineno-0-1524">1524</a></span>
<span class="normal"><a href="#__codelineno-0-1525">1525</a></span>
<span class="normal"><a href="#__codelineno-0-1526">1526</a></span>
<span class="normal"><a href="#__codelineno-0-1527">1527</a></span>
<span class="normal"><a href="#__codelineno-0-1528">1528</a></span>
<span class="normal"><a href="#__codelineno-0-1529">1529</a></span>
<span class="normal"><a href="#__codelineno-0-1530">1530</a></span>
<span class="normal"><a href="#__codelineno-0-1531">1531</a></span>
<span class="normal"><a href="#__codelineno-0-1532">1532</a></span>
<span class="normal"><a href="#__codelineno-0-1533">1533</a></span>
<span class="normal"><a href="#__codelineno-0-1534">1534</a></span>
<span class="normal"><a href="#__codelineno-0-1535">1535</a></span>
<span class="normal"><a href="#__codelineno-0-1536">1536</a></span>
<span class="normal"><a href="#__codelineno-0-1537">1537</a></span>
<span class="normal"><a href="#__codelineno-0-1538">1538</a></span>
<span class="normal"><a href="#__codelineno-0-1539">1539</a></span>
<span class="normal"><a href="#__codelineno-0-1540">1540</a></span>
<span class="normal"><a href="#__codelineno-0-1541">1541</a></span>
<span class="normal"><a href="#__codelineno-0-1542">1542</a></span>
<span class="normal"><a href="#__codelineno-0-1543">1543</a></span>
<span class="normal"><a href="#__codelineno-0-1544">1544</a></span>
<span class="normal"><a href="#__codelineno-0-1545">1545</a></span>
<span class="normal"><a href="#__codelineno-0-1546">1546</a></span>
<span class="normal"><a href="#__codelineno-0-1547">1547</a></span>
<span class="normal"><a href="#__codelineno-0-1548">1548</a></span>
<span class="normal"><a href="#__codelineno-0-1549">1549</a></span>
<span class="normal"><a href="#__codelineno-0-1550">1550</a></span>
<span class="normal"><a href="#__codelineno-0-1551">1551</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1509"><a id="__codelineno-0-1509" name="__codelineno-0-1509"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_config_set_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1510"><a id="__codelineno-0-1510" name="__codelineno-0-1510"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;config set&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1511"><a id="__codelineno-0-1511" name="__codelineno-0-1511"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Config set command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1512"><a id="__codelineno-0-1512" name="__codelineno-0-1512"></a>
</span><span id="__span-0-1513"><a id="__codelineno-0-1513" name="__codelineno-0-1513"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-1514"><a id="__codelineno-0-1514" name="__codelineno-0-1514"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1515"><a id="__codelineno-0-1515" name="__codelineno-0-1515"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1516"><a id="__codelineno-0-1516" name="__codelineno-0-1516"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;   Run &#39;serv app init&#39; to create a configuration file&quot;</span><span class="p">)</span>
</span><span id="__span-0-1517"><a id="__codelineno-0-1517" name="__codelineno-0-1517"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1518"><a id="__codelineno-0-1518" name="__codelineno-0-1518"></a>
</span><span id="__span-0-1519"><a id="__codelineno-0-1519" name="__codelineno-0-1519"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1520"><a id="__codelineno-0-1520" name="__codelineno-0-1520"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1521"><a id="__codelineno-0-1521" name="__codelineno-0-1521"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-1522"><a id="__codelineno-0-1522" name="__codelineno-0-1522"></a>
</span><span id="__span-0-1523"><a id="__codelineno-0-1523" name="__codelineno-0-1523"></a>        <span class="c1"># Convert value to appropriate type</span>
</span><span id="__span-0-1524"><a id="__codelineno-0-1524" name="__codelineno-0-1524"></a>        <span class="n">value</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">value</span>
</span><span id="__span-0-1525"><a id="__codelineno-0-1525" name="__codelineno-0-1525"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">type</span> <span class="o">==</span> <span class="s2">&quot;int&quot;</span><span class="p">:</span>
</span><span id="__span-0-1526"><a id="__codelineno-0-1526" name="__codelineno-0-1526"></a>            <span class="n">value</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
</span><span id="__span-0-1527"><a id="__codelineno-0-1527" name="__codelineno-0-1527"></a>        <span class="k">elif</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">type</span> <span class="o">==</span> <span class="s2">&quot;float&quot;</span><span class="p">:</span>
</span><span id="__span-0-1528"><a id="__codelineno-0-1528" name="__codelineno-0-1528"></a>            <span class="n">value</span> <span class="o">=</span> <span class="nb">float</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
</span><span id="__span-0-1529"><a id="__codelineno-0-1529" name="__codelineno-0-1529"></a>        <span class="k">elif</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">type</span> <span class="o">==</span> <span class="s2">&quot;bool&quot;</span><span class="p">:</span>
</span><span id="__span-0-1530"><a id="__codelineno-0-1530" name="__codelineno-0-1530"></a>            <span class="n">value</span> <span class="o">=</span> <span class="n">value</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="p">(</span><span class="s2">&quot;true&quot;</span><span class="p">,</span> <span class="s2">&quot;yes&quot;</span><span class="p">,</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span> <span class="s2">&quot;on&quot;</span><span class="p">)</span>
</span><span id="__span-0-1531"><a id="__codelineno-0-1531" name="__codelineno-0-1531"></a>        <span class="k">elif</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">type</span> <span class="o">==</span> <span class="s2">&quot;list&quot;</span><span class="p">:</span>
</span><span id="__span-0-1532"><a id="__codelineno-0-1532" name="__codelineno-0-1532"></a>            <span class="c1"># Simple comma-separated list</span>
</span><span id="__span-0-1533"><a id="__codelineno-0-1533" name="__codelineno-0-1533"></a>            <span class="n">value</span> <span class="o">=</span> <span class="p">[</span><span class="n">item</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span> <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">value</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;,&quot;</span><span class="p">)]</span>
</span><span id="__span-0-1534"><a id="__codelineno-0-1534" name="__codelineno-0-1534"></a>
</span><span id="__span-0-1535"><a id="__codelineno-0-1535" name="__codelineno-0-1535"></a>        <span class="c1"># Set the value</span>
</span><span id="__span-0-1536"><a id="__codelineno-0-1536" name="__codelineno-0-1536"></a>        <span class="n">_set_config_value</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</span><span id="__span-0-1537"><a id="__codelineno-0-1537" name="__codelineno-0-1537"></a>
</span><span id="__span-0-1538"><a id="__codelineno-0-1538" name="__codelineno-0-1538"></a>        <span class="c1"># Write back to file</span>
</span><span id="__span-0-1539"><a id="__codelineno-0-1539" name="__codelineno-0-1539"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1540"><a id="__codelineno-0-1540" name="__codelineno-0-1540"></a>            <span class="n">yaml</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">default_flow_style</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="__span-0-1541"><a id="__codelineno-0-1541" name="__codelineno-0-1541"></a>
</span><span id="__span-0-1542"><a id="__codelineno-0-1542" name="__codelineno-0-1542"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ Set </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">key</span><span class="si">}</span><span class="s2"> = </span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1543"><a id="__codelineno-0-1543" name="__codelineno-0-1543"></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1544"><a id="__codelineno-0-1544" name="__codelineno-0-1544"></a>
</span><span id="__span-0-1545"><a id="__codelineno-0-1545" name="__codelineno-0-1545"></a>    <span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1546"><a id="__codelineno-0-1546" name="__codelineno-0-1546"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Invalid value type: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1547"><a id="__codelineno-0-1547" name="__codelineno-0-1547"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1548"><a id="__codelineno-0-1548" name="__codelineno-0-1548"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1549"><a id="__codelineno-0-1549" name="__codelineno-0-1549"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error setting configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1550"><a id="__codelineno-0-1550" name="__codelineno-0-1550"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Error setting configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1551"><a id="__codelineno-0-1551" name="__codelineno-0-1551"></a>        <span class="k">return</span> <span class="kc">False</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_config_show_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_config_show_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_config_show_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'config show' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1372">1372</a></span>
<span class="normal"><a href="#__codelineno-0-1373">1373</a></span>
<span class="normal"><a href="#__codelineno-0-1374">1374</a></span>
<span class="normal"><a href="#__codelineno-0-1375">1375</a></span>
<span class="normal"><a href="#__codelineno-0-1376">1376</a></span>
<span class="normal"><a href="#__codelineno-0-1377">1377</a></span>
<span class="normal"><a href="#__codelineno-0-1378">1378</a></span>
<span class="normal"><a href="#__codelineno-0-1379">1379</a></span>
<span class="normal"><a href="#__codelineno-0-1380">1380</a></span>
<span class="normal"><a href="#__codelineno-0-1381">1381</a></span>
<span class="normal"><a href="#__codelineno-0-1382">1382</a></span>
<span class="normal"><a href="#__codelineno-0-1383">1383</a></span>
<span class="normal"><a href="#__codelineno-0-1384">1384</a></span>
<span class="normal"><a href="#__codelineno-0-1385">1385</a></span>
<span class="normal"><a href="#__codelineno-0-1386">1386</a></span>
<span class="normal"><a href="#__codelineno-0-1387">1387</a></span>
<span class="normal"><a href="#__codelineno-0-1388">1388</a></span>
<span class="normal"><a href="#__codelineno-0-1389">1389</a></span>
<span class="normal"><a href="#__codelineno-0-1390">1390</a></span>
<span class="normal"><a href="#__codelineno-0-1391">1391</a></span>
<span class="normal"><a href="#__codelineno-0-1392">1392</a></span>
<span class="normal"><a href="#__codelineno-0-1393">1393</a></span>
<span class="normal"><a href="#__codelineno-0-1394">1394</a></span>
<span class="normal"><a href="#__codelineno-0-1395">1395</a></span>
<span class="normal"><a href="#__codelineno-0-1396">1396</a></span>
<span class="normal"><a href="#__codelineno-0-1397">1397</a></span>
<span class="normal"><a href="#__codelineno-0-1398">1398</a></span>
<span class="normal"><a href="#__codelineno-0-1399">1399</a></span>
<span class="normal"><a href="#__codelineno-0-1400">1400</a></span>
<span class="normal"><a href="#__codelineno-0-1401">1401</a></span>
<span class="normal"><a href="#__codelineno-0-1402">1402</a></span>
<span class="normal"><a href="#__codelineno-0-1403">1403</a></span>
<span class="normal"><a href="#__codelineno-0-1404">1404</a></span>
<span class="normal"><a href="#__codelineno-0-1405">1405</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1372"><a id="__codelineno-0-1372" name="__codelineno-0-1372"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_config_show_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1373"><a id="__codelineno-0-1373" name="__codelineno-0-1373"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;config show&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1374"><a id="__codelineno-0-1374" name="__codelineno-0-1374"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Config show command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1375"><a id="__codelineno-0-1375" name="__codelineno-0-1375"></a>
</span><span id="__span-0-1376"><a id="__codelineno-0-1376" name="__codelineno-0-1376"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-1377"><a id="__codelineno-0-1377" name="__codelineno-0-1377"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1378"><a id="__codelineno-0-1378" name="__codelineno-0-1378"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1379"><a id="__codelineno-0-1379" name="__codelineno-0-1379"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;   Run &#39;serv app init&#39; to create a configuration file&quot;</span><span class="p">)</span>
</span><span id="__span-0-1380"><a id="__codelineno-0-1380" name="__codelineno-0-1380"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1381"><a id="__codelineno-0-1381" name="__codelineno-0-1381"></a>
</span><span id="__span-0-1382"><a id="__codelineno-0-1382" name="__codelineno-0-1382"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1383"><a id="__codelineno-0-1383" name="__codelineno-0-1383"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1384"><a id="__codelineno-0-1384" name="__codelineno-0-1384"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-1385"><a id="__codelineno-0-1385" name="__codelineno-0-1385"></a>
</span><span id="__span-0-1386"><a id="__codelineno-0-1386" name="__codelineno-0-1386"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1387"><a id="__codelineno-0-1387" name="__codelineno-0-1387"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ Configuration file is empty&quot;</span><span class="p">)</span>
</span><span id="__span-0-1388"><a id="__codelineno-0-1388" name="__codelineno-0-1388"></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1389"><a id="__codelineno-0-1389" name="__codelineno-0-1389"></a>
</span><span id="__span-0-1390"><a id="__codelineno-0-1390" name="__codelineno-0-1390"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;📄 Configuration from &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;:&quot;</span><span class="p">)</span>
</span><span id="__span-0-1391"><a id="__codelineno-0-1391" name="__codelineno-0-1391"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;=&quot;</span> <span class="o">*</span> <span class="mi">50</span><span class="p">)</span>
</span><span id="__span-0-1392"><a id="__codelineno-0-1392" name="__codelineno-0-1392"></a>
</span><span id="__span-0-1393"><a id="__codelineno-0-1393" name="__codelineno-0-1393"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">format</span> <span class="o">==</span> <span class="s2">&quot;json&quot;</span><span class="p">:</span>
</span><span id="__span-0-1394"><a id="__codelineno-0-1394" name="__codelineno-0-1394"></a>            <span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="nb">str</span><span class="p">))</span>
</span><span id="__span-0-1395"><a id="__codelineno-0-1395" name="__codelineno-0-1395"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1396"><a id="__codelineno-0-1396" name="__codelineno-0-1396"></a>            <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-1397"><a id="__codelineno-0-1397" name="__codelineno-0-1397"></a>                <span class="n">yaml</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">default_flow_style</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="__span-0-1398"><a id="__codelineno-0-1398" name="__codelineno-0-1398"></a>            <span class="p">)</span>
</span><span id="__span-0-1399"><a id="__codelineno-0-1399" name="__codelineno-0-1399"></a>
</span><span id="__span-0-1400"><a id="__codelineno-0-1400" name="__codelineno-0-1400"></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1401"><a id="__codelineno-0-1401" name="__codelineno-0-1401"></a>
</span><span id="__span-0-1402"><a id="__codelineno-0-1402" name="__codelineno-0-1402"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1403"><a id="__codelineno-0-1403" name="__codelineno-0-1403"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error reading configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1404"><a id="__codelineno-0-1404" name="__codelineno-0-1404"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Error reading configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1405"><a id="__codelineno-0-1405" name="__codelineno-0-1405"></a>        <span class="k">return</span> <span class="kc">False</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_config_validate_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_config_validate_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_config_validate_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'config validate' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1408">1408</a></span>
<span class="normal"><a href="#__codelineno-0-1409">1409</a></span>
<span class="normal"><a href="#__codelineno-0-1410">1410</a></span>
<span class="normal"><a href="#__codelineno-0-1411">1411</a></span>
<span class="normal"><a href="#__codelineno-0-1412">1412</a></span>
<span class="normal"><a href="#__codelineno-0-1413">1413</a></span>
<span class="normal"><a href="#__codelineno-0-1414">1414</a></span>
<span class="normal"><a href="#__codelineno-0-1415">1415</a></span>
<span class="normal"><a href="#__codelineno-0-1416">1416</a></span>
<span class="normal"><a href="#__codelineno-0-1417">1417</a></span>
<span class="normal"><a href="#__codelineno-0-1418">1418</a></span>
<span class="normal"><a href="#__codelineno-0-1419">1419</a></span>
<span class="normal"><a href="#__codelineno-0-1420">1420</a></span>
<span class="normal"><a href="#__codelineno-0-1421">1421</a></span>
<span class="normal"><a href="#__codelineno-0-1422">1422</a></span>
<span class="normal"><a href="#__codelineno-0-1423">1423</a></span>
<span class="normal"><a href="#__codelineno-0-1424">1424</a></span>
<span class="normal"><a href="#__codelineno-0-1425">1425</a></span>
<span class="normal"><a href="#__codelineno-0-1426">1426</a></span>
<span class="normal"><a href="#__codelineno-0-1427">1427</a></span>
<span class="normal"><a href="#__codelineno-0-1428">1428</a></span>
<span class="normal"><a href="#__codelineno-0-1429">1429</a></span>
<span class="normal"><a href="#__codelineno-0-1430">1430</a></span>
<span class="normal"><a href="#__codelineno-0-1431">1431</a></span>
<span class="normal"><a href="#__codelineno-0-1432">1432</a></span>
<span class="normal"><a href="#__codelineno-0-1433">1433</a></span>
<span class="normal"><a href="#__codelineno-0-1434">1434</a></span>
<span class="normal"><a href="#__codelineno-0-1435">1435</a></span>
<span class="normal"><a href="#__codelineno-0-1436">1436</a></span>
<span class="normal"><a href="#__codelineno-0-1437">1437</a></span>
<span class="normal"><a href="#__codelineno-0-1438">1438</a></span>
<span class="normal"><a href="#__codelineno-0-1439">1439</a></span>
<span class="normal"><a href="#__codelineno-0-1440">1440</a></span>
<span class="normal"><a href="#__codelineno-0-1441">1441</a></span>
<span class="normal"><a href="#__codelineno-0-1442">1442</a></span>
<span class="normal"><a href="#__codelineno-0-1443">1443</a></span>
<span class="normal"><a href="#__codelineno-0-1444">1444</a></span>
<span class="normal"><a href="#__codelineno-0-1445">1445</a></span>
<span class="normal"><a href="#__codelineno-0-1446">1446</a></span>
<span class="normal"><a href="#__codelineno-0-1447">1447</a></span>
<span class="normal"><a href="#__codelineno-0-1448">1448</a></span>
<span class="normal"><a href="#__codelineno-0-1449">1449</a></span>
<span class="normal"><a href="#__codelineno-0-1450">1450</a></span>
<span class="normal"><a href="#__codelineno-0-1451">1451</a></span>
<span class="normal"><a href="#__codelineno-0-1452">1452</a></span>
<span class="normal"><a href="#__codelineno-0-1453">1453</a></span>
<span class="normal"><a href="#__codelineno-0-1454">1454</a></span>
<span class="normal"><a href="#__codelineno-0-1455">1455</a></span>
<span class="normal"><a href="#__codelineno-0-1456">1456</a></span>
<span class="normal"><a href="#__codelineno-0-1457">1457</a></span>
<span class="normal"><a href="#__codelineno-0-1458">1458</a></span>
<span class="normal"><a href="#__codelineno-0-1459">1459</a></span>
<span class="normal"><a href="#__codelineno-0-1460">1460</a></span>
<span class="normal"><a href="#__codelineno-0-1461">1461</a></span>
<span class="normal"><a href="#__codelineno-0-1462">1462</a></span>
<span class="normal"><a href="#__codelineno-0-1463">1463</a></span>
<span class="normal"><a href="#__codelineno-0-1464">1464</a></span>
<span class="normal"><a href="#__codelineno-0-1465">1465</a></span>
<span class="normal"><a href="#__codelineno-0-1466">1466</a></span>
<span class="normal"><a href="#__codelineno-0-1467">1467</a></span>
<span class="normal"><a href="#__codelineno-0-1468">1468</a></span>
<span class="normal"><a href="#__codelineno-0-1469">1469</a></span>
<span class="normal"><a href="#__codelineno-0-1470">1470</a></span>
<span class="normal"><a href="#__codelineno-0-1471">1471</a></span>
<span class="normal"><a href="#__codelineno-0-1472">1472</a></span>
<span class="normal"><a href="#__codelineno-0-1473">1473</a></span>
<span class="normal"><a href="#__codelineno-0-1474">1474</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1408"><a id="__codelineno-0-1408" name="__codelineno-0-1408"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_config_validate_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1409"><a id="__codelineno-0-1409" name="__codelineno-0-1409"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;config validate&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1410"><a id="__codelineno-0-1410" name="__codelineno-0-1410"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Config validate command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1411"><a id="__codelineno-0-1411" name="__codelineno-0-1411"></a>
</span><span id="__span-0-1412"><a id="__codelineno-0-1412" name="__codelineno-0-1412"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-1413"><a id="__codelineno-0-1413" name="__codelineno-0-1413"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1414"><a id="__codelineno-0-1414" name="__codelineno-0-1414"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1415"><a id="__codelineno-0-1415" name="__codelineno-0-1415"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1416"><a id="__codelineno-0-1416" name="__codelineno-0-1416"></a>
</span><span id="__span-0-1417"><a id="__codelineno-0-1417" name="__codelineno-0-1417"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1418"><a id="__codelineno-0-1418" name="__codelineno-0-1418"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1419"><a id="__codelineno-0-1419" name="__codelineno-0-1419"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-1420"><a id="__codelineno-0-1420" name="__codelineno-0-1420"></a>
</span><span id="__span-0-1421"><a id="__codelineno-0-1421" name="__codelineno-0-1421"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1422"><a id="__codelineno-0-1422" name="__codelineno-0-1422"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ Configuration file is empty&quot;</span><span class="p">)</span>
</span><span id="__span-0-1423"><a id="__codelineno-0-1423" name="__codelineno-0-1423"></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1424"><a id="__codelineno-0-1424" name="__codelineno-0-1424"></a>
</span><span id="__span-0-1425"><a id="__codelineno-0-1425" name="__codelineno-0-1425"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;✅ Configuration file is valid YAML&quot;</span><span class="p">)</span>
</span><span id="__span-0-1426"><a id="__codelineno-0-1426" name="__codelineno-0-1426"></a>
</span><span id="__span-0-1427"><a id="__codelineno-0-1427" name="__codelineno-0-1427"></a>        <span class="c1"># Basic structure validation</span>
</span><span id="__span-0-1428"><a id="__codelineno-0-1428" name="__codelineno-0-1428"></a>        <span class="n">issues</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="__span-0-1429"><a id="__codelineno-0-1429" name="__codelineno-0-1429"></a>
</span><span id="__span-0-1430"><a id="__codelineno-0-1430" name="__codelineno-0-1430"></a>        <span class="c1"># Check required sections</span>
</span><span id="__span-0-1431"><a id="__codelineno-0-1431" name="__codelineno-0-1431"></a>        <span class="n">required_sections</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;site_info&quot;</span><span class="p">]</span>
</span><span id="__span-0-1432"><a id="__codelineno-0-1432" name="__codelineno-0-1432"></a>        <span class="k">for</span> <span class="n">section</span> <span class="ow">in</span> <span class="n">required_sections</span><span class="p">:</span>
</span><span id="__span-0-1433"><a id="__codelineno-0-1433" name="__codelineno-0-1433"></a>            <span class="k">if</span> <span class="n">section</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1434"><a id="__codelineno-0-1434" name="__codelineno-0-1434"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Missing recommended section: </span><span class="si">{</span><span class="n">section</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1435"><a id="__codelineno-0-1435" name="__codelineno-0-1435"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-1436"><a id="__codelineno-0-1436" name="__codelineno-0-1436"></a>
</span><span id="__span-0-1437"><a id="__codelineno-0-1437" name="__codelineno-0-1437"></a>        <span class="c1"># Check site_info structure</span>
</span><span id="__span-0-1438"><a id="__codelineno-0-1438" name="__codelineno-0-1438"></a>        <span class="k">if</span> <span class="s2">&quot;site_info&quot;</span> <span class="ow">in</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1439"><a id="__codelineno-0-1439" name="__codelineno-0-1439"></a>            <span class="n">site_info</span> <span class="o">=</span> <span class="n">config</span><span class="p">[</span><span class="s2">&quot;site_info&quot;</span><span class="p">]</span>
</span><span id="__span-0-1440"><a id="__codelineno-0-1440" name="__codelineno-0-1440"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">site_info</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
</span><span id="__span-0-1441"><a id="__codelineno-0-1441" name="__codelineno-0-1441"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ &#39;site_info&#39; must be a dictionary&quot;</span><span class="p">)</span>
</span><span id="__span-0-1442"><a id="__codelineno-0-1442" name="__codelineno-0-1442"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-1443"><a id="__codelineno-0-1443" name="__codelineno-0-1443"></a>            <span class="k">elif</span> <span class="ow">not</span> <span class="n">site_info</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">):</span>
</span><span id="__span-0-1444"><a id="__codelineno-0-1444" name="__codelineno-0-1444"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  Missing &#39;site_info.name&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-1445"><a id="__codelineno-0-1445" name="__codelineno-0-1445"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-1446"><a id="__codelineno-0-1446" name="__codelineno-0-1446"></a>
</span><span id="__span-0-1447"><a id="__codelineno-0-1447" name="__codelineno-0-1447"></a>        <span class="c1"># Check plugins structure</span>
</span><span id="__span-0-1448"><a id="__codelineno-0-1448" name="__codelineno-0-1448"></a>        <span class="k">if</span> <span class="s2">&quot;plugins&quot;</span> <span class="ow">in</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1449"><a id="__codelineno-0-1449" name="__codelineno-0-1449"></a>            <span class="n">plugins</span> <span class="o">=</span> <span class="n">config</span><span class="p">[</span><span class="s2">&quot;plugins&quot;</span><span class="p">]</span>
</span><span id="__span-0-1450"><a id="__codelineno-0-1450" name="__codelineno-0-1450"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plugins</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
</span><span id="__span-0-1451"><a id="__codelineno-0-1451" name="__codelineno-0-1451"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ &#39;plugins&#39; must be a list&quot;</span><span class="p">)</span>
</span><span id="__span-0-1452"><a id="__codelineno-0-1452" name="__codelineno-0-1452"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-1453"><a id="__codelineno-0-1453" name="__codelineno-0-1453"></a>
</span><span id="__span-0-1454"><a id="__codelineno-0-1454" name="__codelineno-0-1454"></a>        <span class="c1"># Check middleware structure</span>
</span><span id="__span-0-1455"><a id="__codelineno-0-1455" name="__codelineno-0-1455"></a>        <span class="k">if</span> <span class="s2">&quot;middleware&quot;</span> <span class="ow">in</span> <span class="n">config</span><span class="p">:</span>
</span><span id="__span-0-1456"><a id="__codelineno-0-1456" name="__codelineno-0-1456"></a>            <span class="n">middleware</span> <span class="o">=</span> <span class="n">config</span><span class="p">[</span><span class="s2">&quot;middleware&quot;</span><span class="p">]</span>
</span><span id="__span-0-1457"><a id="__codelineno-0-1457" name="__codelineno-0-1457"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">middleware</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
</span><span id="__span-0-1458"><a id="__codelineno-0-1458" name="__codelineno-0-1458"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ &#39;middleware&#39; must be a list&quot;</span><span class="p">)</span>
</span><span id="__span-0-1459"><a id="__codelineno-0-1459" name="__codelineno-0-1459"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-1460"><a id="__codelineno-0-1460" name="__codelineno-0-1460"></a>
</span><span id="__span-0-1461"><a id="__codelineno-0-1461" name="__codelineno-0-1461"></a>        <span class="k">if</span> <span class="n">issues</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="__span-0-1462"><a id="__codelineno-0-1462" name="__codelineno-0-1462"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🎉 Configuration validation passed!&quot;</span><span class="p">)</span>
</span><span id="__span-0-1463"><a id="__codelineno-0-1463" name="__codelineno-0-1463"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1464"><a id="__codelineno-0-1464" name="__codelineno-0-1464"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Found </span><span class="si">{</span><span class="n">issues</span><span class="si">}</span><span class="s2"> validation issue(s)&quot;</span><span class="p">)</span>
</span><span id="__span-0-1465"><a id="__codelineno-0-1465" name="__codelineno-0-1465"></a>
</span><span id="__span-0-1466"><a id="__codelineno-0-1466" name="__codelineno-0-1466"></a>        <span class="k">return</span> <span class="n">issues</span> <span class="o">==</span> <span class="mi">0</span>
</span><span id="__span-0-1467"><a id="__codelineno-0-1467" name="__codelineno-0-1467"></a>
</span><span id="__span-0-1468"><a id="__codelineno-0-1468" name="__codelineno-0-1468"></a>    <span class="k">except</span> <span class="n">yaml</span><span class="o">.</span><span class="n">YAMLError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1469"><a id="__codelineno-0-1469" name="__codelineno-0-1469"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Invalid YAML syntax: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1470"><a id="__codelineno-0-1470" name="__codelineno-0-1470"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1471"><a id="__codelineno-0-1471" name="__codelineno-0-1471"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1472"><a id="__codelineno-0-1472" name="__codelineno-0-1472"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error validating configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1473"><a id="__codelineno-0-1473" name="__codelineno-0-1473"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Error validating configuration: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1474"><a id="__codelineno-0-1474" name="__codelineno-0-1474"></a>        <span class="k">return</span> <span class="kc">False</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_create_entrypoint_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_create_entrypoint_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_create_entrypoint_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'create entrypoint' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-766">766</a></span>
<span class="normal"><a href="#__codelineno-0-767">767</a></span>
<span class="normal"><a href="#__codelineno-0-768">768</a></span>
<span class="normal"><a href="#__codelineno-0-769">769</a></span>
<span class="normal"><a href="#__codelineno-0-770">770</a></span>
<span class="normal"><a href="#__codelineno-0-771">771</a></span>
<span class="normal"><a href="#__codelineno-0-772">772</a></span>
<span class="normal"><a href="#__codelineno-0-773">773</a></span>
<span class="normal"><a href="#__codelineno-0-774">774</a></span>
<span class="normal"><a href="#__codelineno-0-775">775</a></span>
<span class="normal"><a href="#__codelineno-0-776">776</a></span>
<span class="normal"><a href="#__codelineno-0-777">777</a></span>
<span class="normal"><a href="#__codelineno-0-778">778</a></span>
<span class="normal"><a href="#__codelineno-0-779">779</a></span>
<span class="normal"><a href="#__codelineno-0-780">780</a></span>
<span class="normal"><a href="#__codelineno-0-781">781</a></span>
<span class="normal"><a href="#__codelineno-0-782">782</a></span>
<span class="normal"><a href="#__codelineno-0-783">783</a></span>
<span class="normal"><a href="#__codelineno-0-784">784</a></span>
<span class="normal"><a href="#__codelineno-0-785">785</a></span>
<span class="normal"><a href="#__codelineno-0-786">786</a></span>
<span class="normal"><a href="#__codelineno-0-787">787</a></span>
<span class="normal"><a href="#__codelineno-0-788">788</a></span>
<span class="normal"><a href="#__codelineno-0-789">789</a></span>
<span class="normal"><a href="#__codelineno-0-790">790</a></span>
<span class="normal"><a href="#__codelineno-0-791">791</a></span>
<span class="normal"><a href="#__codelineno-0-792">792</a></span>
<span class="normal"><a href="#__codelineno-0-793">793</a></span>
<span class="normal"><a href="#__codelineno-0-794">794</a></span>
<span class="normal"><a href="#__codelineno-0-795">795</a></span>
<span class="normal"><a href="#__codelineno-0-796">796</a></span>
<span class="normal"><a href="#__codelineno-0-797">797</a></span>
<span class="normal"><a href="#__codelineno-0-798">798</a></span>
<span class="normal"><a href="#__codelineno-0-799">799</a></span>
<span class="normal"><a href="#__codelineno-0-800">800</a></span>
<span class="normal"><a href="#__codelineno-0-801">801</a></span>
<span class="normal"><a href="#__codelineno-0-802">802</a></span>
<span class="normal"><a href="#__codelineno-0-803">803</a></span>
<span class="normal"><a href="#__codelineno-0-804">804</a></span>
<span class="normal"><a href="#__codelineno-0-805">805</a></span>
<span class="normal"><a href="#__codelineno-0-806">806</a></span>
<span class="normal"><a href="#__codelineno-0-807">807</a></span>
<span class="normal"><a href="#__codelineno-0-808">808</a></span>
<span class="normal"><a href="#__codelineno-0-809">809</a></span>
<span class="normal"><a href="#__codelineno-0-810">810</a></span>
<span class="normal"><a href="#__codelineno-0-811">811</a></span>
<span class="normal"><a href="#__codelineno-0-812">812</a></span>
<span class="normal"><a href="#__codelineno-0-813">813</a></span>
<span class="normal"><a href="#__codelineno-0-814">814</a></span>
<span class="normal"><a href="#__codelineno-0-815">815</a></span>
<span class="normal"><a href="#__codelineno-0-816">816</a></span>
<span class="normal"><a href="#__codelineno-0-817">817</a></span>
<span class="normal"><a href="#__codelineno-0-818">818</a></span>
<span class="normal"><a href="#__codelineno-0-819">819</a></span>
<span class="normal"><a href="#__codelineno-0-820">820</a></span>
<span class="normal"><a href="#__codelineno-0-821">821</a></span>
<span class="normal"><a href="#__codelineno-0-822">822</a></span>
<span class="normal"><a href="#__codelineno-0-823">823</a></span>
<span class="normal"><a href="#__codelineno-0-824">824</a></span>
<span class="normal"><a href="#__codelineno-0-825">825</a></span>
<span class="normal"><a href="#__codelineno-0-826">826</a></span>
<span class="normal"><a href="#__codelineno-0-827">827</a></span>
<span class="normal"><a href="#__codelineno-0-828">828</a></span>
<span class="normal"><a href="#__codelineno-0-829">829</a></span>
<span class="normal"><a href="#__codelineno-0-830">830</a></span>
<span class="normal"><a href="#__codelineno-0-831">831</a></span>
<span class="normal"><a href="#__codelineno-0-832">832</a></span>
<span class="normal"><a href="#__codelineno-0-833">833</a></span>
<span class="normal"><a href="#__codelineno-0-834">834</a></span>
<span class="normal"><a href="#__codelineno-0-835">835</a></span>
<span class="normal"><a href="#__codelineno-0-836">836</a></span>
<span class="normal"><a href="#__codelineno-0-837">837</a></span>
<span class="normal"><a href="#__codelineno-0-838">838</a></span>
<span class="normal"><a href="#__codelineno-0-839">839</a></span>
<span class="normal"><a href="#__codelineno-0-840">840</a></span>
<span class="normal"><a href="#__codelineno-0-841">841</a></span>
<span class="normal"><a href="#__codelineno-0-842">842</a></span>
<span class="normal"><a href="#__codelineno-0-843">843</a></span>
<span class="normal"><a href="#__codelineno-0-844">844</a></span>
<span class="normal"><a href="#__codelineno-0-845">845</a></span>
<span class="normal"><a href="#__codelineno-0-846">846</a></span>
<span class="normal"><a href="#__codelineno-0-847">847</a></span>
<span class="normal"><a href="#__codelineno-0-848">848</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-766"><a id="__codelineno-0-766" name="__codelineno-0-766"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_create_entrypoint_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-767"><a id="__codelineno-0-767" name="__codelineno-0-767"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;create entrypoint&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-768"><a id="__codelineno-0-768" name="__codelineno-0-768"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Create entrypoint command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-769"><a id="__codelineno-0-769" name="__codelineno-0-769"></a>
</span><span id="__span-0-770"><a id="__codelineno-0-770" name="__codelineno-0-770"></a>    <span class="n">component_name</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-771"><a id="__codelineno-0-771" name="__codelineno-0-771"></a>    <span class="n">plugin_name</span><span class="p">,</span> <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">_detect_plugin_context</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">)</span>
</span><span id="__span-0-772"><a id="__codelineno-0-772" name="__codelineno-0-772"></a>
</span><span id="__span-0-773"><a id="__codelineno-0-773" name="__codelineno-0-773"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-774"><a id="__codelineno-0-774" name="__codelineno-0-774"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">:</span>
</span><span id="__span-0-775"><a id="__codelineno-0-775" name="__codelineno-0-775"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&#39; not found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-776"><a id="__codelineno-0-776" name="__codelineno-0-776"></a>            <span class="k">return</span>
</span><span id="__span-0-777"><a id="__codelineno-0-777" name="__codelineno-0-777"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-778"><a id="__codelineno-0-778" name="__codelineno-0-778"></a>            <span class="c1"># Interactive prompt for plugin</span>
</span><span id="__span-0-779"><a id="__codelineno-0-779" name="__codelineno-0-779"></a>            <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-780"><a id="__codelineno-0-780" name="__codelineno-0-780"></a>            <span class="k">if</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-781"><a id="__codelineno-0-781" name="__codelineno-0-781"></a>                <span class="n">available_plugins</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-782"><a id="__codelineno-0-782" name="__codelineno-0-782"></a>                    <span class="n">d</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-783"><a id="__codelineno-0-783" name="__codelineno-0-783"></a>                    <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span>
</span><span id="__span-0-784"><a id="__codelineno-0-784" name="__codelineno-0-784"></a>                    <span class="k">if</span> <span class="n">d</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
</span><span id="__span-0-785"><a id="__codelineno-0-785" name="__codelineno-0-785"></a>                    <span class="ow">and</span> <span class="p">(</span><span class="n">d</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-786"><a id="__codelineno-0-786" name="__codelineno-0-786"></a>                    <span class="ow">and</span> <span class="ow">not</span> <span class="n">d</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">)</span>
</span><span id="__span-0-787"><a id="__codelineno-0-787" name="__codelineno-0-787"></a>                <span class="p">]</span>
</span><span id="__span-0-788"><a id="__codelineno-0-788" name="__codelineno-0-788"></a>                <span class="k">if</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-789"><a id="__codelineno-0-789" name="__codelineno-0-789"></a>                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Available plugins:&quot;</span><span class="p">)</span>
</span><span id="__span-0-790"><a id="__codelineno-0-790" name="__codelineno-0-790"></a>                    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">plugin</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">,</span> <span class="mi">1</span><span class="p">):</span>
</span><span id="__span-0-791"><a id="__codelineno-0-791" name="__codelineno-0-791"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-792"><a id="__codelineno-0-792" name="__codelineno-0-792"></a>                    <span class="n">plugin_choice</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Select plugin (name or number)&quot;</span><span class="p">)</span>
</span><span id="__span-0-793"><a id="__codelineno-0-793" name="__codelineno-0-793"></a>                    <span class="k">if</span> <span class="n">plugin_choice</span> <span class="ow">and</span> <span class="n">plugin_choice</span><span class="o">.</span><span class="n">isdigit</span><span class="p">():</span>
</span><span id="__span-0-794"><a id="__codelineno-0-794" name="__codelineno-0-794"></a>                        <span class="n">idx</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">plugin_choice</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="__span-0-795"><a id="__codelineno-0-795" name="__codelineno-0-795"></a>                        <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">idx</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">):</span>
</span><span id="__span-0-796"><a id="__codelineno-0-796" name="__codelineno-0-796"></a>                            <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">available_plugins</span><span class="p">[</span><span class="n">idx</span><span class="p">]</span>
</span><span id="__span-0-797"><a id="__codelineno-0-797" name="__codelineno-0-797"></a>                            <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-798"><a id="__codelineno-0-798" name="__codelineno-0-798"></a>                    <span class="k">elif</span> <span class="n">plugin_choice</span> <span class="ow">in</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-799"><a id="__codelineno-0-799" name="__codelineno-0-799"></a>                        <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_choice</span>
</span><span id="__span-0-800"><a id="__codelineno-0-800" name="__codelineno-0-800"></a>                        <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-801"><a id="__codelineno-0-801" name="__codelineno-0-801"></a>
</span><span id="__span-0-802"><a id="__codelineno-0-802" name="__codelineno-0-802"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-803"><a id="__codelineno-0-803" name="__codelineno-0-803"></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;No plugin specified and none could be auto-detected.&quot;</span><span class="p">)</span>
</span><span id="__span-0-804"><a id="__codelineno-0-804" name="__codelineno-0-804"></a>                <span class="k">return</span>
</span><span id="__span-0-805"><a id="__codelineno-0-805" name="__codelineno-0-805"></a>
</span><span id="__span-0-806"><a id="__codelineno-0-806" name="__codelineno-0-806"></a>    <span class="n">class_name</span> <span class="o">=</span> <span class="n">to_pascal_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span>
</span><span id="__span-0-807"><a id="__codelineno-0-807" name="__codelineno-0-807"></a>    <span class="n">file_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;entrypoint_</span><span class="si">{</span><span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span><span class="si">}</span><span class="s2">.py&quot;</span>
</span><span id="__span-0-808"><a id="__codelineno-0-808" name="__codelineno-0-808"></a>    <span class="n">file_path</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="n">file_name</span>
</span><span id="__span-0-809"><a id="__codelineno-0-809" name="__codelineno-0-809"></a>
</span><span id="__span-0-810"><a id="__codelineno-0-810" name="__codelineno-0-810"></a>    <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">force</span><span class="p">:</span>
</span><span id="__span-0-811"><a id="__codelineno-0-811" name="__codelineno-0-811"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Warning: File &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39; already exists. Use --force to overwrite.&quot;</span><span class="p">)</span>
</span><span id="__span-0-812"><a id="__codelineno-0-812" name="__codelineno-0-812"></a>        <span class="k">return</span>
</span><span id="__span-0-813"><a id="__codelineno-0-813" name="__codelineno-0-813"></a>
</span><span id="__span-0-814"><a id="__codelineno-0-814" name="__codelineno-0-814"></a>    <span class="c1"># Create the entrypoint file</span>
</span><span id="__span-0-815"><a id="__codelineno-0-815" name="__codelineno-0-815"></a>    <span class="n">context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-816"><a id="__codelineno-0-816" name="__codelineno-0-816"></a>        <span class="s2">&quot;class_name&quot;</span><span class="p">:</span> <span class="n">class_name</span><span class="p">,</span>
</span><span id="__span-0-817"><a id="__codelineno-0-817" name="__codelineno-0-817"></a>        <span class="s2">&quot;entrypoint_name&quot;</span><span class="p">:</span> <span class="n">component_name</span><span class="p">,</span>
</span><span id="__span-0-818"><a id="__codelineno-0-818" name="__codelineno-0-818"></a>        <span class="s2">&quot;route_path&quot;</span><span class="p">:</span> <span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">),</span>
</span><span id="__span-0-819"><a id="__codelineno-0-819" name="__codelineno-0-819"></a>        <span class="s2">&quot;handler_name&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;handle_</span><span class="si">{</span><span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
</span><span id="__span-0-820"><a id="__codelineno-0-820" name="__codelineno-0-820"></a>    <span class="p">}</span>
</span><span id="__span-0-821"><a id="__codelineno-0-821" name="__codelineno-0-821"></a>
</span><span id="__span-0-822"><a id="__codelineno-0-822" name="__codelineno-0-822"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-823"><a id="__codelineno-0-823" name="__codelineno-0-823"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-824"><a id="__codelineno-0-824" name="__codelineno-0-824"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-825"><a id="__codelineno-0-825" name="__codelineno-0-825"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-826"><a id="__codelineno-0-826" name="__codelineno-0-826"></a>        <span class="p">)</span>
</span><span id="__span-0-827"><a id="__codelineno-0-827" name="__codelineno-0-827"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-828"><a id="__codelineno-0-828" name="__codelineno-0-828"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;entrypoint_main_py.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-829"><a id="__codelineno-0-829" name="__codelineno-0-829"></a>        <span class="n">content</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">context</span><span class="p">)</span>
</span><span id="__span-0-830"><a id="__codelineno-0-830" name="__codelineno-0-830"></a>
</span><span id="__span-0-831"><a id="__codelineno-0-831" name="__codelineno-0-831"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-832"><a id="__codelineno-0-832" name="__codelineno-0-832"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">content</span><span class="p">)</span>
</span><span id="__span-0-833"><a id="__codelineno-0-833" name="__codelineno-0-833"></a>
</span><span id="__span-0-834"><a id="__codelineno-0-834" name="__codelineno-0-834"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-835"><a id="__codelineno-0-835" name="__codelineno-0-835"></a>
</span><span id="__span-0-836"><a id="__codelineno-0-836" name="__codelineno-0-836"></a>        <span class="c1"># Update plugin config</span>
</span><span id="__span-0-837"><a id="__codelineno-0-837" name="__codelineno-0-837"></a>        <span class="n">entry_path</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">file_name</span><span class="p">[:</span><span class="o">-</span><span class="mi">3</span><span class="p">]</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">class_name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-838"><a id="__codelineno-0-838" name="__codelineno-0-838"></a>        <span class="k">if</span> <span class="n">_update_plugin_config</span><span class="p">(</span>
</span><span id="__span-0-839"><a id="__codelineno-0-839" name="__codelineno-0-839"></a>            <span class="n">plugin_dir</span><span class="p">,</span> <span class="s2">&quot;entry_points&quot;</span><span class="p">,</span> <span class="n">component_name</span><span class="p">,</span> <span class="n">entry_path</span>
</span><span id="__span-0-840"><a id="__codelineno-0-840" name="__codelineno-0-840"></a>        <span class="p">):</span>
</span><span id="__span-0-841"><a id="__codelineno-0-841" name="__codelineno-0-841"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Added entrypoint to plugin configuration&quot;</span><span class="p">)</span>
</span><span id="__span-0-842"><a id="__codelineno-0-842" name="__codelineno-0-842"></a>
</span><span id="__span-0-843"><a id="__codelineno-0-843" name="__codelineno-0-843"></a>        <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-844"><a id="__codelineno-0-844" name="__codelineno-0-844"></a>            <span class="sa">f</span><span class="s2">&quot;Entrypoint &#39;</span><span class="si">{</span><span class="n">component_name</span><span class="si">}</span><span class="s2">&#39; created successfully in plugin &#39;</span><span class="si">{</span><span class="n">plugin_name</span><span class="si">}</span><span class="s2">&#39;.&quot;</span>
</span><span id="__span-0-845"><a id="__codelineno-0-845" name="__codelineno-0-845"></a>        <span class="p">)</span>
</span><span id="__span-0-846"><a id="__codelineno-0-846" name="__codelineno-0-846"></a>
</span><span id="__span-0-847"><a id="__codelineno-0-847" name="__codelineno-0-847"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-848"><a id="__codelineno-0-848" name="__codelineno-0-848"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating entrypoint: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_create_middleware_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_create_middleware_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_create_middleware_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'create middleware' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1013">1013</a></span>
<span class="normal"><a href="#__codelineno-0-1014">1014</a></span>
<span class="normal"><a href="#__codelineno-0-1015">1015</a></span>
<span class="normal"><a href="#__codelineno-0-1016">1016</a></span>
<span class="normal"><a href="#__codelineno-0-1017">1017</a></span>
<span class="normal"><a href="#__codelineno-0-1018">1018</a></span>
<span class="normal"><a href="#__codelineno-0-1019">1019</a></span>
<span class="normal"><a href="#__codelineno-0-1020">1020</a></span>
<span class="normal"><a href="#__codelineno-0-1021">1021</a></span>
<span class="normal"><a href="#__codelineno-0-1022">1022</a></span>
<span class="normal"><a href="#__codelineno-0-1023">1023</a></span>
<span class="normal"><a href="#__codelineno-0-1024">1024</a></span>
<span class="normal"><a href="#__codelineno-0-1025">1025</a></span>
<span class="normal"><a href="#__codelineno-0-1026">1026</a></span>
<span class="normal"><a href="#__codelineno-0-1027">1027</a></span>
<span class="normal"><a href="#__codelineno-0-1028">1028</a></span>
<span class="normal"><a href="#__codelineno-0-1029">1029</a></span>
<span class="normal"><a href="#__codelineno-0-1030">1030</a></span>
<span class="normal"><a href="#__codelineno-0-1031">1031</a></span>
<span class="normal"><a href="#__codelineno-0-1032">1032</a></span>
<span class="normal"><a href="#__codelineno-0-1033">1033</a></span>
<span class="normal"><a href="#__codelineno-0-1034">1034</a></span>
<span class="normal"><a href="#__codelineno-0-1035">1035</a></span>
<span class="normal"><a href="#__codelineno-0-1036">1036</a></span>
<span class="normal"><a href="#__codelineno-0-1037">1037</a></span>
<span class="normal"><a href="#__codelineno-0-1038">1038</a></span>
<span class="normal"><a href="#__codelineno-0-1039">1039</a></span>
<span class="normal"><a href="#__codelineno-0-1040">1040</a></span>
<span class="normal"><a href="#__codelineno-0-1041">1041</a></span>
<span class="normal"><a href="#__codelineno-0-1042">1042</a></span>
<span class="normal"><a href="#__codelineno-0-1043">1043</a></span>
<span class="normal"><a href="#__codelineno-0-1044">1044</a></span>
<span class="normal"><a href="#__codelineno-0-1045">1045</a></span>
<span class="normal"><a href="#__codelineno-0-1046">1046</a></span>
<span class="normal"><a href="#__codelineno-0-1047">1047</a></span>
<span class="normal"><a href="#__codelineno-0-1048">1048</a></span>
<span class="normal"><a href="#__codelineno-0-1049">1049</a></span>
<span class="normal"><a href="#__codelineno-0-1050">1050</a></span>
<span class="normal"><a href="#__codelineno-0-1051">1051</a></span>
<span class="normal"><a href="#__codelineno-0-1052">1052</a></span>
<span class="normal"><a href="#__codelineno-0-1053">1053</a></span>
<span class="normal"><a href="#__codelineno-0-1054">1054</a></span>
<span class="normal"><a href="#__codelineno-0-1055">1055</a></span>
<span class="normal"><a href="#__codelineno-0-1056">1056</a></span>
<span class="normal"><a href="#__codelineno-0-1057">1057</a></span>
<span class="normal"><a href="#__codelineno-0-1058">1058</a></span>
<span class="normal"><a href="#__codelineno-0-1059">1059</a></span>
<span class="normal"><a href="#__codelineno-0-1060">1060</a></span>
<span class="normal"><a href="#__codelineno-0-1061">1061</a></span>
<span class="normal"><a href="#__codelineno-0-1062">1062</a></span>
<span class="normal"><a href="#__codelineno-0-1063">1063</a></span>
<span class="normal"><a href="#__codelineno-0-1064">1064</a></span>
<span class="normal"><a href="#__codelineno-0-1065">1065</a></span>
<span class="normal"><a href="#__codelineno-0-1066">1066</a></span>
<span class="normal"><a href="#__codelineno-0-1067">1067</a></span>
<span class="normal"><a href="#__codelineno-0-1068">1068</a></span>
<span class="normal"><a href="#__codelineno-0-1069">1069</a></span>
<span class="normal"><a href="#__codelineno-0-1070">1070</a></span>
<span class="normal"><a href="#__codelineno-0-1071">1071</a></span>
<span class="normal"><a href="#__codelineno-0-1072">1072</a></span>
<span class="normal"><a href="#__codelineno-0-1073">1073</a></span>
<span class="normal"><a href="#__codelineno-0-1074">1074</a></span>
<span class="normal"><a href="#__codelineno-0-1075">1075</a></span>
<span class="normal"><a href="#__codelineno-0-1076">1076</a></span>
<span class="normal"><a href="#__codelineno-0-1077">1077</a></span>
<span class="normal"><a href="#__codelineno-0-1078">1078</a></span>
<span class="normal"><a href="#__codelineno-0-1079">1079</a></span>
<span class="normal"><a href="#__codelineno-0-1080">1080</a></span>
<span class="normal"><a href="#__codelineno-0-1081">1081</a></span>
<span class="normal"><a href="#__codelineno-0-1082">1082</a></span>
<span class="normal"><a href="#__codelineno-0-1083">1083</a></span>
<span class="normal"><a href="#__codelineno-0-1084">1084</a></span>
<span class="normal"><a href="#__codelineno-0-1085">1085</a></span>
<span class="normal"><a href="#__codelineno-0-1086">1086</a></span>
<span class="normal"><a href="#__codelineno-0-1087">1087</a></span>
<span class="normal"><a href="#__codelineno-0-1088">1088</a></span>
<span class="normal"><a href="#__codelineno-0-1089">1089</a></span>
<span class="normal"><a href="#__codelineno-0-1090">1090</a></span>
<span class="normal"><a href="#__codelineno-0-1091">1091</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1013"><a id="__codelineno-0-1013" name="__codelineno-0-1013"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_create_middleware_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1014"><a id="__codelineno-0-1014" name="__codelineno-0-1014"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;create middleware&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1015"><a id="__codelineno-0-1015" name="__codelineno-0-1015"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Create middleware command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1016"><a id="__codelineno-0-1016" name="__codelineno-0-1016"></a>
</span><span id="__span-0-1017"><a id="__codelineno-0-1017" name="__codelineno-0-1017"></a>    <span class="n">component_name</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-1018"><a id="__codelineno-0-1018" name="__codelineno-0-1018"></a>    <span class="n">plugin_name</span><span class="p">,</span> <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">_detect_plugin_context</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">)</span>
</span><span id="__span-0-1019"><a id="__codelineno-0-1019" name="__codelineno-0-1019"></a>
</span><span id="__span-0-1020"><a id="__codelineno-0-1020" name="__codelineno-0-1020"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-1021"><a id="__codelineno-0-1021" name="__codelineno-0-1021"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">:</span>
</span><span id="__span-0-1022"><a id="__codelineno-0-1022" name="__codelineno-0-1022"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&#39; not found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1023"><a id="__codelineno-0-1023" name="__codelineno-0-1023"></a>            <span class="k">return</span>
</span><span id="__span-0-1024"><a id="__codelineno-0-1024" name="__codelineno-0-1024"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1025"><a id="__codelineno-0-1025" name="__codelineno-0-1025"></a>            <span class="c1"># Interactive prompt for plugin</span>
</span><span id="__span-0-1026"><a id="__codelineno-0-1026" name="__codelineno-0-1026"></a>            <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-1027"><a id="__codelineno-0-1027" name="__codelineno-0-1027"></a>            <span class="k">if</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1028"><a id="__codelineno-0-1028" name="__codelineno-0-1028"></a>                <span class="n">available_plugins</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-1029"><a id="__codelineno-0-1029" name="__codelineno-0-1029"></a>                    <span class="n">d</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-1030"><a id="__codelineno-0-1030" name="__codelineno-0-1030"></a>                    <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span>
</span><span id="__span-0-1031"><a id="__codelineno-0-1031" name="__codelineno-0-1031"></a>                    <span class="k">if</span> <span class="n">d</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
</span><span id="__span-0-1032"><a id="__codelineno-0-1032" name="__codelineno-0-1032"></a>                    <span class="ow">and</span> <span class="p">(</span><span class="n">d</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-1033"><a id="__codelineno-0-1033" name="__codelineno-0-1033"></a>                    <span class="ow">and</span> <span class="ow">not</span> <span class="n">d</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">)</span>
</span><span id="__span-0-1034"><a id="__codelineno-0-1034" name="__codelineno-0-1034"></a>                <span class="p">]</span>
</span><span id="__span-0-1035"><a id="__codelineno-0-1035" name="__codelineno-0-1035"></a>                <span class="k">if</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-1036"><a id="__codelineno-0-1036" name="__codelineno-0-1036"></a>                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Available plugins:&quot;</span><span class="p">)</span>
</span><span id="__span-0-1037"><a id="__codelineno-0-1037" name="__codelineno-0-1037"></a>                    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">plugin</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">,</span> <span class="mi">1</span><span class="p">):</span>
</span><span id="__span-0-1038"><a id="__codelineno-0-1038" name="__codelineno-0-1038"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1039"><a id="__codelineno-0-1039" name="__codelineno-0-1039"></a>                    <span class="n">plugin_choice</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Select plugin (name or number)&quot;</span><span class="p">)</span>
</span><span id="__span-0-1040"><a id="__codelineno-0-1040" name="__codelineno-0-1040"></a>                    <span class="k">if</span> <span class="n">plugin_choice</span> <span class="ow">and</span> <span class="n">plugin_choice</span><span class="o">.</span><span class="n">isdigit</span><span class="p">():</span>
</span><span id="__span-0-1041"><a id="__codelineno-0-1041" name="__codelineno-0-1041"></a>                        <span class="n">idx</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">plugin_choice</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="__span-0-1042"><a id="__codelineno-0-1042" name="__codelineno-0-1042"></a>                        <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">idx</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">):</span>
</span><span id="__span-0-1043"><a id="__codelineno-0-1043" name="__codelineno-0-1043"></a>                            <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">available_plugins</span><span class="p">[</span><span class="n">idx</span><span class="p">]</span>
</span><span id="__span-0-1044"><a id="__codelineno-0-1044" name="__codelineno-0-1044"></a>                            <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-1045"><a id="__codelineno-0-1045" name="__codelineno-0-1045"></a>                    <span class="k">elif</span> <span class="n">plugin_choice</span> <span class="ow">in</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-1046"><a id="__codelineno-0-1046" name="__codelineno-0-1046"></a>                        <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_choice</span>
</span><span id="__span-0-1047"><a id="__codelineno-0-1047" name="__codelineno-0-1047"></a>                        <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-1048"><a id="__codelineno-0-1048" name="__codelineno-0-1048"></a>
</span><span id="__span-0-1049"><a id="__codelineno-0-1049" name="__codelineno-0-1049"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-1050"><a id="__codelineno-0-1050" name="__codelineno-0-1050"></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;No plugin specified and none could be auto-detected.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1051"><a id="__codelineno-0-1051" name="__codelineno-0-1051"></a>                <span class="k">return</span>
</span><span id="__span-0-1052"><a id="__codelineno-0-1052" name="__codelineno-0-1052"></a>
</span><span id="__span-0-1053"><a id="__codelineno-0-1053" name="__codelineno-0-1053"></a>    <span class="n">middleware_name</span> <span class="o">=</span> <span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span>
</span><span id="__span-0-1054"><a id="__codelineno-0-1054" name="__codelineno-0-1054"></a>    <span class="n">file_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;middleware_</span><span class="si">{</span><span class="n">middleware_name</span><span class="si">}</span><span class="s2">.py&quot;</span>
</span><span id="__span-0-1055"><a id="__codelineno-0-1055" name="__codelineno-0-1055"></a>    <span class="n">file_path</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="n">file_name</span>
</span><span id="__span-0-1056"><a id="__codelineno-0-1056" name="__codelineno-0-1056"></a>
</span><span id="__span-0-1057"><a id="__codelineno-0-1057" name="__codelineno-0-1057"></a>    <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">force</span><span class="p">:</span>
</span><span id="__span-0-1058"><a id="__codelineno-0-1058" name="__codelineno-0-1058"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Warning: File &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39; already exists. Use --force to overwrite.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1059"><a id="__codelineno-0-1059" name="__codelineno-0-1059"></a>        <span class="k">return</span>
</span><span id="__span-0-1060"><a id="__codelineno-0-1060" name="__codelineno-0-1060"></a>
</span><span id="__span-0-1061"><a id="__codelineno-0-1061" name="__codelineno-0-1061"></a>    <span class="c1"># Create the middleware file</span>
</span><span id="__span-0-1062"><a id="__codelineno-0-1062" name="__codelineno-0-1062"></a>    <span class="n">context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-1063"><a id="__codelineno-0-1063" name="__codelineno-0-1063"></a>        <span class="s2">&quot;middleware_name&quot;</span><span class="p">:</span> <span class="n">middleware_name</span><span class="p">,</span>
</span><span id="__span-0-1064"><a id="__codelineno-0-1064" name="__codelineno-0-1064"></a>        <span class="s2">&quot;middleware_description&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Middleware for </span><span class="si">{</span><span class="n">component_name</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;_&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39; &#39;</span><span class="p">)</span><span class="si">}</span><span class="s2"> functionality.&quot;</span><span class="p">,</span>
</span><span id="__span-0-1065"><a id="__codelineno-0-1065" name="__codelineno-0-1065"></a>    <span class="p">}</span>
</span><span id="__span-0-1066"><a id="__codelineno-0-1066" name="__codelineno-0-1066"></a>
</span><span id="__span-0-1067"><a id="__codelineno-0-1067" name="__codelineno-0-1067"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1068"><a id="__codelineno-0-1068" name="__codelineno-0-1068"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-1069"><a id="__codelineno-0-1069" name="__codelineno-0-1069"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-1070"><a id="__codelineno-0-1070" name="__codelineno-0-1070"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-1071"><a id="__codelineno-0-1071" name="__codelineno-0-1071"></a>        <span class="p">)</span>
</span><span id="__span-0-1072"><a id="__codelineno-0-1072" name="__codelineno-0-1072"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-1073"><a id="__codelineno-0-1073" name="__codelineno-0-1073"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;middleware_main_py.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-1074"><a id="__codelineno-0-1074" name="__codelineno-0-1074"></a>        <span class="n">content</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">context</span><span class="p">)</span>
</span><span id="__span-0-1075"><a id="__codelineno-0-1075" name="__codelineno-0-1075"></a>
</span><span id="__span-0-1076"><a id="__codelineno-0-1076" name="__codelineno-0-1076"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-1077"><a id="__codelineno-0-1077" name="__codelineno-0-1077"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">content</span><span class="p">)</span>
</span><span id="__span-0-1078"><a id="__codelineno-0-1078" name="__codelineno-0-1078"></a>
</span><span id="__span-0-1079"><a id="__codelineno-0-1079" name="__codelineno-0-1079"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-1080"><a id="__codelineno-0-1080" name="__codelineno-0-1080"></a>
</span><span id="__span-0-1081"><a id="__codelineno-0-1081" name="__codelineno-0-1081"></a>        <span class="c1"># Update plugin config</span>
</span><span id="__span-0-1082"><a id="__codelineno-0-1082" name="__codelineno-0-1082"></a>        <span class="n">entry_path</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">file_name</span><span class="p">[:</span><span class="o">-</span><span class="mi">3</span><span class="p">]</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">middleware_name</span><span class="si">}</span><span class="s2">_middleware&quot;</span>
</span><span id="__span-0-1083"><a id="__codelineno-0-1083" name="__codelineno-0-1083"></a>        <span class="k">if</span> <span class="n">_update_plugin_config</span><span class="p">(</span><span class="n">plugin_dir</span><span class="p">,</span> <span class="s2">&quot;middleware&quot;</span><span class="p">,</span> <span class="n">component_name</span><span class="p">,</span> <span class="n">entry_path</span><span class="p">):</span>
</span><span id="__span-0-1084"><a id="__codelineno-0-1084" name="__codelineno-0-1084"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Added middleware to plugin configuration&quot;</span><span class="p">)</span>
</span><span id="__span-0-1085"><a id="__codelineno-0-1085" name="__codelineno-0-1085"></a>
</span><span id="__span-0-1086"><a id="__codelineno-0-1086" name="__codelineno-0-1086"></a>        <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-1087"><a id="__codelineno-0-1087" name="__codelineno-0-1087"></a>            <span class="sa">f</span><span class="s2">&quot;Middleware &#39;</span><span class="si">{</span><span class="n">component_name</span><span class="si">}</span><span class="s2">&#39; created successfully in plugin &#39;</span><span class="si">{</span><span class="n">plugin_name</span><span class="si">}</span><span class="s2">&#39;.&quot;</span>
</span><span id="__span-0-1088"><a id="__codelineno-0-1088" name="__codelineno-0-1088"></a>        <span class="p">)</span>
</span><span id="__span-0-1089"><a id="__codelineno-0-1089" name="__codelineno-0-1089"></a>
</span><span id="__span-0-1090"><a id="__codelineno-0-1090" name="__codelineno-0-1090"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1091"><a id="__codelineno-0-1091" name="__codelineno-0-1091"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating middleware: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_create_plugin_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_create_plugin_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_create_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'create plugin' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-196">196</a></span>
<span class="normal"><a href="#__codelineno-0-197">197</a></span>
<span class="normal"><a href="#__codelineno-0-198">198</a></span>
<span class="normal"><a href="#__codelineno-0-199">199</a></span>
<span class="normal"><a href="#__codelineno-0-200">200</a></span>
<span class="normal"><a href="#__codelineno-0-201">201</a></span>
<span class="normal"><a href="#__codelineno-0-202">202</a></span>
<span class="normal"><a href="#__codelineno-0-203">203</a></span>
<span class="normal"><a href="#__codelineno-0-204">204</a></span>
<span class="normal"><a href="#__codelineno-0-205">205</a></span>
<span class="normal"><a href="#__codelineno-0-206">206</a></span>
<span class="normal"><a href="#__codelineno-0-207">207</a></span>
<span class="normal"><a href="#__codelineno-0-208">208</a></span>
<span class="normal"><a href="#__codelineno-0-209">209</a></span>
<span class="normal"><a href="#__codelineno-0-210">210</a></span>
<span class="normal"><a href="#__codelineno-0-211">211</a></span>
<span class="normal"><a href="#__codelineno-0-212">212</a></span>
<span class="normal"><a href="#__codelineno-0-213">213</a></span>
<span class="normal"><a href="#__codelineno-0-214">214</a></span>
<span class="normal"><a href="#__codelineno-0-215">215</a></span>
<span class="normal"><a href="#__codelineno-0-216">216</a></span>
<span class="normal"><a href="#__codelineno-0-217">217</a></span>
<span class="normal"><a href="#__codelineno-0-218">218</a></span>
<span class="normal"><a href="#__codelineno-0-219">219</a></span>
<span class="normal"><a href="#__codelineno-0-220">220</a></span>
<span class="normal"><a href="#__codelineno-0-221">221</a></span>
<span class="normal"><a href="#__codelineno-0-222">222</a></span>
<span class="normal"><a href="#__codelineno-0-223">223</a></span>
<span class="normal"><a href="#__codelineno-0-224">224</a></span>
<span class="normal"><a href="#__codelineno-0-225">225</a></span>
<span class="normal"><a href="#__codelineno-0-226">226</a></span>
<span class="normal"><a href="#__codelineno-0-227">227</a></span>
<span class="normal"><a href="#__codelineno-0-228">228</a></span>
<span class="normal"><a href="#__codelineno-0-229">229</a></span>
<span class="normal"><a href="#__codelineno-0-230">230</a></span>
<span class="normal"><a href="#__codelineno-0-231">231</a></span>
<span class="normal"><a href="#__codelineno-0-232">232</a></span>
<span class="normal"><a href="#__codelineno-0-233">233</a></span>
<span class="normal"><a href="#__codelineno-0-234">234</a></span>
<span class="normal"><a href="#__codelineno-0-235">235</a></span>
<span class="normal"><a href="#__codelineno-0-236">236</a></span>
<span class="normal"><a href="#__codelineno-0-237">237</a></span>
<span class="normal"><a href="#__codelineno-0-238">238</a></span>
<span class="normal"><a href="#__codelineno-0-239">239</a></span>
<span class="normal"><a href="#__codelineno-0-240">240</a></span>
<span class="normal"><a href="#__codelineno-0-241">241</a></span>
<span class="normal"><a href="#__codelineno-0-242">242</a></span>
<span class="normal"><a href="#__codelineno-0-243">243</a></span>
<span class="normal"><a href="#__codelineno-0-244">244</a></span>
<span class="normal"><a href="#__codelineno-0-245">245</a></span>
<span class="normal"><a href="#__codelineno-0-246">246</a></span>
<span class="normal"><a href="#__codelineno-0-247">247</a></span>
<span class="normal"><a href="#__codelineno-0-248">248</a></span>
<span class="normal"><a href="#__codelineno-0-249">249</a></span>
<span class="normal"><a href="#__codelineno-0-250">250</a></span>
<span class="normal"><a href="#__codelineno-0-251">251</a></span>
<span class="normal"><a href="#__codelineno-0-252">252</a></span>
<span class="normal"><a href="#__codelineno-0-253">253</a></span>
<span class="normal"><a href="#__codelineno-0-254">254</a></span>
<span class="normal"><a href="#__codelineno-0-255">255</a></span>
<span class="normal"><a href="#__codelineno-0-256">256</a></span>
<span class="normal"><a href="#__codelineno-0-257">257</a></span>
<span class="normal"><a href="#__codelineno-0-258">258</a></span>
<span class="normal"><a href="#__codelineno-0-259">259</a></span>
<span class="normal"><a href="#__codelineno-0-260">260</a></span>
<span class="normal"><a href="#__codelineno-0-261">261</a></span>
<span class="normal"><a href="#__codelineno-0-262">262</a></span>
<span class="normal"><a href="#__codelineno-0-263">263</a></span>
<span class="normal"><a href="#__codelineno-0-264">264</a></span>
<span class="normal"><a href="#__codelineno-0-265">265</a></span>
<span class="normal"><a href="#__codelineno-0-266">266</a></span>
<span class="normal"><a href="#__codelineno-0-267">267</a></span>
<span class="normal"><a href="#__codelineno-0-268">268</a></span>
<span class="normal"><a href="#__codelineno-0-269">269</a></span>
<span class="normal"><a href="#__codelineno-0-270">270</a></span>
<span class="normal"><a href="#__codelineno-0-271">271</a></span>
<span class="normal"><a href="#__codelineno-0-272">272</a></span>
<span class="normal"><a href="#__codelineno-0-273">273</a></span>
<span class="normal"><a href="#__codelineno-0-274">274</a></span>
<span class="normal"><a href="#__codelineno-0-275">275</a></span>
<span class="normal"><a href="#__codelineno-0-276">276</a></span>
<span class="normal"><a href="#__codelineno-0-277">277</a></span>
<span class="normal"><a href="#__codelineno-0-278">278</a></span>
<span class="normal"><a href="#__codelineno-0-279">279</a></span>
<span class="normal"><a href="#__codelineno-0-280">280</a></span>
<span class="normal"><a href="#__codelineno-0-281">281</a></span>
<span class="normal"><a href="#__codelineno-0-282">282</a></span>
<span class="normal"><a href="#__codelineno-0-283">283</a></span>
<span class="normal"><a href="#__codelineno-0-284">284</a></span>
<span class="normal"><a href="#__codelineno-0-285">285</a></span>
<span class="normal"><a href="#__codelineno-0-286">286</a></span>
<span class="normal"><a href="#__codelineno-0-287">287</a></span>
<span class="normal"><a href="#__codelineno-0-288">288</a></span>
<span class="normal"><a href="#__codelineno-0-289">289</a></span>
<span class="normal"><a href="#__codelineno-0-290">290</a></span>
<span class="normal"><a href="#__codelineno-0-291">291</a></span>
<span class="normal"><a href="#__codelineno-0-292">292</a></span>
<span class="normal"><a href="#__codelineno-0-293">293</a></span>
<span class="normal"><a href="#__codelineno-0-294">294</a></span>
<span class="normal"><a href="#__codelineno-0-295">295</a></span>
<span class="normal"><a href="#__codelineno-0-296">296</a></span>
<span class="normal"><a href="#__codelineno-0-297">297</a></span>
<span class="normal"><a href="#__codelineno-0-298">298</a></span>
<span class="normal"><a href="#__codelineno-0-299">299</a></span>
<span class="normal"><a href="#__codelineno-0-300">300</a></span>
<span class="normal"><a href="#__codelineno-0-301">301</a></span>
<span class="normal"><a href="#__codelineno-0-302">302</a></span>
<span class="normal"><a href="#__codelineno-0-303">303</a></span>
<span class="normal"><a href="#__codelineno-0-304">304</a></span>
<span class="normal"><a href="#__codelineno-0-305">305</a></span>
<span class="normal"><a href="#__codelineno-0-306">306</a></span>
<span class="normal"><a href="#__codelineno-0-307">307</a></span>
<span class="normal"><a href="#__codelineno-0-308">308</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-196"><a id="__codelineno-0-196" name="__codelineno-0-196"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_create_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-197"><a id="__codelineno-0-197" name="__codelineno-0-197"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;create plugin&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-198"><a id="__codelineno-0-198" name="__codelineno-0-198"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Create plugin command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-199"><a id="__codelineno-0-199" name="__codelineno-0-199"></a>
</span><span id="__span-0-200"><a id="__codelineno-0-200" name="__codelineno-0-200"></a>    <span class="c1"># Get plugin name from args</span>
</span><span id="__span-0-201"><a id="__codelineno-0-201" name="__codelineno-0-201"></a>    <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-202"><a id="__codelineno-0-202" name="__codelineno-0-202"></a>
</span><span id="__span-0-203"><a id="__codelineno-0-203" name="__codelineno-0-203"></a>    <span class="c1"># For non-interactive mode, use default values</span>
</span><span id="__span-0-204"><a id="__codelineno-0-204" name="__codelineno-0-204"></a>    <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">args_ns</span><span class="p">,</span> <span class="s2">&quot;non_interactive&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
</span><span id="__span-0-205"><a id="__codelineno-0-205" name="__codelineno-0-205"></a>        <span class="n">plugin_author</span> <span class="o">=</span> <span class="s2">&quot;Test Author&quot;</span>
</span><span id="__span-0-206"><a id="__codelineno-0-206" name="__codelineno-0-206"></a>        <span class="n">plugin_description</span> <span class="o">=</span> <span class="s2">&quot;A test plugin for Serv&quot;</span>
</span><span id="__span-0-207"><a id="__codelineno-0-207" name="__codelineno-0-207"></a>        <span class="n">plugin_version</span> <span class="o">=</span> <span class="s2">&quot;1.0.0&quot;</span>
</span><span id="__span-0-208"><a id="__codelineno-0-208" name="__codelineno-0-208"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-209"><a id="__codelineno-0-209" name="__codelineno-0-209"></a>        <span class="n">plugin_author</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Author&quot;</span><span class="p">,</span> <span class="s2">&quot;Your Name&quot;</span><span class="p">)</span> <span class="ow">or</span> <span class="s2">&quot;Your Name&quot;</span>
</span><span id="__span-0-210"><a id="__codelineno-0-210" name="__codelineno-0-210"></a>        <span class="n">plugin_description</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-211"><a id="__codelineno-0-211" name="__codelineno-0-211"></a>            <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Description&quot;</span><span class="p">,</span> <span class="s2">&quot;A cool Serv plugin.&quot;</span><span class="p">)</span> <span class="ow">or</span> <span class="s2">&quot;A cool Serv plugin.&quot;</span>
</span><span id="__span-0-212"><a id="__codelineno-0-212" name="__codelineno-0-212"></a>        <span class="p">)</span>
</span><span id="__span-0-213"><a id="__codelineno-0-213" name="__codelineno-0-213"></a>        <span class="n">plugin_version</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Version&quot;</span><span class="p">,</span> <span class="s2">&quot;0.1.0&quot;</span><span class="p">)</span> <span class="ow">or</span> <span class="s2">&quot;0.1.0&quot;</span>
</span><span id="__span-0-214"><a id="__codelineno-0-214" name="__codelineno-0-214"></a>
</span><span id="__span-0-215"><a id="__codelineno-0-215" name="__codelineno-0-215"></a>    <span class="n">class_name</span> <span class="o">=</span> <span class="n">to_pascal_case</span><span class="p">(</span><span class="n">plugin_name_human</span><span class="p">)</span>
</span><span id="__span-0-216"><a id="__codelineno-0-216" name="__codelineno-0-216"></a>    <span class="n">plugin_dir_name</span> <span class="o">=</span> <span class="n">to_snake_case</span><span class="p">(</span><span class="n">plugin_name_human</span><span class="p">)</span>
</span><span id="__span-0-217"><a id="__codelineno-0-217" name="__codelineno-0-217"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_dir_name</span><span class="p">:</span>
</span><span id="__span-0-218"><a id="__codelineno-0-218" name="__codelineno-0-218"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
</span><span id="__span-0-219"><a id="__codelineno-0-219" name="__codelineno-0-219"></a>            <span class="sa">f</span><span class="s2">&quot;Could not derive a valid module name from &#39;</span><span class="si">{</span><span class="n">plugin_name_human</span><span class="si">}</span><span class="s2">&#39;. Please use alphanumeric characters.&quot;</span>
</span><span id="__span-0-220"><a id="__codelineno-0-220" name="__codelineno-0-220"></a>        <span class="p">)</span>
</span><span id="__span-0-221"><a id="__codelineno-0-221" name="__codelineno-0-221"></a>        <span class="k">return</span>
</span><span id="__span-0-222"><a id="__codelineno-0-222" name="__codelineno-0-222"></a>
</span><span id="__span-0-223"><a id="__codelineno-0-223" name="__codelineno-0-223"></a>    <span class="n">python_file_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">plugin_dir_name</span><span class="si">}</span><span class="s2">.py&quot;</span>
</span><span id="__span-0-224"><a id="__codelineno-0-224" name="__codelineno-0-224"></a>
</span><span id="__span-0-225"><a id="__codelineno-0-225" name="__codelineno-0-225"></a>    <span class="n">plugins_root_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-226"><a id="__codelineno-0-226" name="__codelineno-0-226"></a>    <span class="n">plugin_specific_dir</span> <span class="o">=</span> <span class="n">plugins_root_dir</span> <span class="o">/</span> <span class="n">plugin_dir_name</span>
</span><span id="__span-0-227"><a id="__codelineno-0-227" name="__codelineno-0-227"></a>
</span><span id="__span-0-228"><a id="__codelineno-0-228" name="__codelineno-0-228"></a>    <span class="k">if</span> <span class="n">plugin_specific_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">args_ns</span><span class="p">,</span> <span class="s2">&quot;force&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
</span><span id="__span-0-229"><a id="__codelineno-0-229" name="__codelineno-0-229"></a>        <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-230"><a id="__codelineno-0-230" name="__codelineno-0-230"></a>            <span class="sa">f</span><span class="s2">&quot;Warning: Plugin directory &#39;</span><span class="si">{</span><span class="n">plugin_specific_dir</span><span class="si">}</span><span class="s2">&#39; already exists. Files might be overwritten.&quot;</span>
</span><span id="__span-0-231"><a id="__codelineno-0-231" name="__codelineno-0-231"></a>        <span class="p">)</span>
</span><span id="__span-0-232"><a id="__codelineno-0-232" name="__codelineno-0-232"></a>
</span><span id="__span-0-233"><a id="__codelineno-0-233" name="__codelineno-0-233"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-234"><a id="__codelineno-0-234" name="__codelineno-0-234"></a>        <span class="n">os</span><span class="o">.</span><span class="n">makedirs</span><span class="p">(</span><span class="n">plugin_specific_dir</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="__span-0-235"><a id="__codelineno-0-235" name="__codelineno-0-235"></a>        <span class="p">(</span><span class="n">plugins_root_dir</span> <span class="o">/</span> <span class="s2">&quot;__init__.py&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">touch</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="__span-0-236"><a id="__codelineno-0-236" name="__codelineno-0-236"></a>        <span class="p">(</span><span class="n">plugin_specific_dir</span> <span class="o">/</span> <span class="s2">&quot;__init__.py&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">touch</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="__span-0-237"><a id="__codelineno-0-237" name="__codelineno-0-237"></a>
</span><span id="__span-0-238"><a id="__codelineno-0-238" name="__codelineno-0-238"></a>    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-239"><a id="__codelineno-0-239" name="__codelineno-0-239"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
</span><span id="__span-0-240"><a id="__codelineno-0-240" name="__codelineno-0-240"></a>            <span class="sa">f</span><span class="s2">&quot;Error creating plugin directory structure &#39;</span><span class="si">{</span><span class="n">plugin_specific_dir</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-241"><a id="__codelineno-0-241" name="__codelineno-0-241"></a>        <span class="p">)</span>
</span><span id="__span-0-242"><a id="__codelineno-0-242" name="__codelineno-0-242"></a>        <span class="k">return</span>
</span><span id="__span-0-243"><a id="__codelineno-0-243" name="__codelineno-0-243"></a>
</span><span id="__span-0-244"><a id="__codelineno-0-244" name="__codelineno-0-244"></a>    <span class="c1"># Create plugin.yaml</span>
</span><span id="__span-0-245"><a id="__codelineno-0-245" name="__codelineno-0-245"></a>    <span class="n">plugin_yaml_path</span> <span class="o">=</span> <span class="n">plugin_specific_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span>
</span><span id="__span-0-246"><a id="__codelineno-0-246" name="__codelineno-0-246"></a>    <span class="n">plugin_entry_path</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">python_file_name</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;.py&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">class_name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-247"><a id="__codelineno-0-247" name="__codelineno-0-247"></a>
</span><span id="__span-0-248"><a id="__codelineno-0-248" name="__codelineno-0-248"></a>    <span class="n">plugin_yaml_context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-249"><a id="__codelineno-0-249" name="__codelineno-0-249"></a>        <span class="s2">&quot;plugin_name&quot;</span><span class="p">:</span> <span class="n">plugin_name_human</span><span class="p">,</span>
</span><span id="__span-0-250"><a id="__codelineno-0-250" name="__codelineno-0-250"></a>        <span class="s2">&quot;plugin_entry_path&quot;</span><span class="p">:</span> <span class="n">plugin_entry_path</span><span class="p">,</span>
</span><span id="__span-0-251"><a id="__codelineno-0-251" name="__codelineno-0-251"></a>        <span class="s2">&quot;plugin_version&quot;</span><span class="p">:</span> <span class="n">plugin_version</span><span class="p">,</span>
</span><span id="__span-0-252"><a id="__codelineno-0-252" name="__codelineno-0-252"></a>        <span class="s2">&quot;plugin_author&quot;</span><span class="p">:</span> <span class="n">plugin_author</span><span class="p">,</span>
</span><span id="__span-0-253"><a id="__codelineno-0-253" name="__codelineno-0-253"></a>        <span class="s2">&quot;plugin_description&quot;</span><span class="p">:</span> <span class="n">plugin_description</span><span class="p">,</span>
</span><span id="__span-0-254"><a id="__codelineno-0-254" name="__codelineno-0-254"></a>    <span class="p">}</span>
</span><span id="__span-0-255"><a id="__codelineno-0-255" name="__codelineno-0-255"></a>
</span><span id="__span-0-256"><a id="__codelineno-0-256" name="__codelineno-0-256"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-257"><a id="__codelineno-0-257" name="__codelineno-0-257"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-258"><a id="__codelineno-0-258" name="__codelineno-0-258"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-259"><a id="__codelineno-0-259" name="__codelineno-0-259"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-260"><a id="__codelineno-0-260" name="__codelineno-0-260"></a>        <span class="p">)</span>
</span><span id="__span-0-261"><a id="__codelineno-0-261" name="__codelineno-0-261"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-262"><a id="__codelineno-0-262" name="__codelineno-0-262"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;plugin_yaml.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-263"><a id="__codelineno-0-263" name="__codelineno-0-263"></a>        <span class="n">plugin_yaml_content_str</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">plugin_yaml_context</span><span class="p">)</span>
</span><span id="__span-0-264"><a id="__codelineno-0-264" name="__codelineno-0-264"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e_template</span><span class="p">:</span>
</span><span id="__span-0-265"><a id="__codelineno-0-265" name="__codelineno-0-265"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error loading plugin_yaml.template: </span><span class="si">{</span><span class="n">e_template</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-266"><a id="__codelineno-0-266" name="__codelineno-0-266"></a>        <span class="k">return</span>
</span><span id="__span-0-267"><a id="__codelineno-0-267" name="__codelineno-0-267"></a>
</span><span id="__span-0-268"><a id="__codelineno-0-268" name="__codelineno-0-268"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-269"><a id="__codelineno-0-269" name="__codelineno-0-269"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_yaml_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-270"><a id="__codelineno-0-270" name="__codelineno-0-270"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">plugin_yaml_content_str</span><span class="p">)</span>
</span><span id="__span-0-271"><a id="__codelineno-0-271" name="__codelineno-0-271"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created &#39;</span><span class="si">{</span><span class="n">plugin_yaml_path</span><span class="si">}</span><span class="s2">&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-272"><a id="__codelineno-0-272" name="__codelineno-0-272"></a>    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-273"><a id="__codelineno-0-273" name="__codelineno-0-273"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error writing &#39;</span><span class="si">{</span><span class="n">plugin_yaml_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-274"><a id="__codelineno-0-274" name="__codelineno-0-274"></a>        <span class="k">return</span>
</span><span id="__span-0-275"><a id="__codelineno-0-275" name="__codelineno-0-275"></a>
</span><span id="__span-0-276"><a id="__codelineno-0-276" name="__codelineno-0-276"></a>    <span class="c1"># Create main.py (plugin Python file)</span>
</span><span id="__span-0-277"><a id="__codelineno-0-277" name="__codelineno-0-277"></a>    <span class="n">plugin_py_path</span> <span class="o">=</span> <span class="n">plugin_specific_dir</span> <span class="o">/</span> <span class="n">python_file_name</span>
</span><span id="__span-0-278"><a id="__codelineno-0-278" name="__codelineno-0-278"></a>
</span><span id="__span-0-279"><a id="__codelineno-0-279" name="__codelineno-0-279"></a>    <span class="n">plugin_py_context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-280"><a id="__codelineno-0-280" name="__codelineno-0-280"></a>        <span class="s2">&quot;class_name&quot;</span><span class="p">:</span> <span class="n">class_name</span><span class="p">,</span>
</span><span id="__span-0-281"><a id="__codelineno-0-281" name="__codelineno-0-281"></a>        <span class="s2">&quot;plugin_name&quot;</span><span class="p">:</span> <span class="n">plugin_name_human</span><span class="p">,</span>
</span><span id="__span-0-282"><a id="__codelineno-0-282" name="__codelineno-0-282"></a>    <span class="p">}</span>
</span><span id="__span-0-283"><a id="__codelineno-0-283" name="__codelineno-0-283"></a>
</span><span id="__span-0-284"><a id="__codelineno-0-284" name="__codelineno-0-284"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-285"><a id="__codelineno-0-285" name="__codelineno-0-285"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-286"><a id="__codelineno-0-286" name="__codelineno-0-286"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-287"><a id="__codelineno-0-287" name="__codelineno-0-287"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-288"><a id="__codelineno-0-288" name="__codelineno-0-288"></a>        <span class="p">)</span>
</span><span id="__span-0-289"><a id="__codelineno-0-289" name="__codelineno-0-289"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-290"><a id="__codelineno-0-290" name="__codelineno-0-290"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;plugin_main_py.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-291"><a id="__codelineno-0-291" name="__codelineno-0-291"></a>        <span class="n">plugin_py_content_str</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">plugin_py_context</span><span class="p">)</span>
</span><span id="__span-0-292"><a id="__codelineno-0-292" name="__codelineno-0-292"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e_template</span><span class="p">:</span>
</span><span id="__span-0-293"><a id="__codelineno-0-293" name="__codelineno-0-293"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error loading plugin_main_py.template: </span><span class="si">{</span><span class="n">e_template</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-294"><a id="__codelineno-0-294" name="__codelineno-0-294"></a>        <span class="k">return</span>
</span><span id="__span-0-295"><a id="__codelineno-0-295" name="__codelineno-0-295"></a>
</span><span id="__span-0-296"><a id="__codelineno-0-296" name="__codelineno-0-296"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-297"><a id="__codelineno-0-297" name="__codelineno-0-297"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_py_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-298"><a id="__codelineno-0-298" name="__codelineno-0-298"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">plugin_py_content_str</span><span class="p">)</span>
</span><span id="__span-0-299"><a id="__codelineno-0-299" name="__codelineno-0-299"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created &#39;</span><span class="si">{</span><span class="n">plugin_py_path</span><span class="si">}</span><span class="s2">&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-300"><a id="__codelineno-0-300" name="__codelineno-0-300"></a>        <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-301"><a id="__codelineno-0-301" name="__codelineno-0-301"></a>            <span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_name_human</span><span class="si">}</span><span class="s2">&#39; created successfully in &#39;</span><span class="si">{</span><span class="n">plugin_specific_dir</span><span class="si">}</span><span class="s2">&#39;.&quot;</span>
</span><span id="__span-0-302"><a id="__codelineno-0-302" name="__codelineno-0-302"></a>        <span class="p">)</span>
</span><span id="__span-0-303"><a id="__codelineno-0-303" name="__codelineno-0-303"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;To use it, add its entry path to your &#39;</span><span class="si">{</span><span class="n">DEFAULT_CONFIG_FILE</span><span class="si">}</span><span class="s2">&#39;:&quot;</span><span class="p">)</span>
</span><span id="__span-0-304"><a id="__codelineno-0-304" name="__codelineno-0-304"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  - plugin: </span><span class="si">{</span><span class="n">plugin_dir_name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-305"><a id="__codelineno-0-305" name="__codelineno-0-305"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;    config: </span><span class="si">{}</span><span class="s2"> # Optional config&quot;</span><span class="p">)</span>
</span><span id="__span-0-306"><a id="__codelineno-0-306" name="__codelineno-0-306"></a>
</span><span id="__span-0-307"><a id="__codelineno-0-307" name="__codelineno-0-307"></a>    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-308"><a id="__codelineno-0-308" name="__codelineno-0-308"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error writing &#39;</span><span class="si">{</span><span class="n">plugin_py_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_create_route_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_create_route_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_create_route_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'create route' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-851"> 851</a></span>
<span class="normal"><a href="#__codelineno-0-852"> 852</a></span>
<span class="normal"><a href="#__codelineno-0-853"> 853</a></span>
<span class="normal"><a href="#__codelineno-0-854"> 854</a></span>
<span class="normal"><a href="#__codelineno-0-855"> 855</a></span>
<span class="normal"><a href="#__codelineno-0-856"> 856</a></span>
<span class="normal"><a href="#__codelineno-0-857"> 857</a></span>
<span class="normal"><a href="#__codelineno-0-858"> 858</a></span>
<span class="normal"><a href="#__codelineno-0-859"> 859</a></span>
<span class="normal"><a href="#__codelineno-0-860"> 860</a></span>
<span class="normal"><a href="#__codelineno-0-861"> 861</a></span>
<span class="normal"><a href="#__codelineno-0-862"> 862</a></span>
<span class="normal"><a href="#__codelineno-0-863"> 863</a></span>
<span class="normal"><a href="#__codelineno-0-864"> 864</a></span>
<span class="normal"><a href="#__codelineno-0-865"> 865</a></span>
<span class="normal"><a href="#__codelineno-0-866"> 866</a></span>
<span class="normal"><a href="#__codelineno-0-867"> 867</a></span>
<span class="normal"><a href="#__codelineno-0-868"> 868</a></span>
<span class="normal"><a href="#__codelineno-0-869"> 869</a></span>
<span class="normal"><a href="#__codelineno-0-870"> 870</a></span>
<span class="normal"><a href="#__codelineno-0-871"> 871</a></span>
<span class="normal"><a href="#__codelineno-0-872"> 872</a></span>
<span class="normal"><a href="#__codelineno-0-873"> 873</a></span>
<span class="normal"><a href="#__codelineno-0-874"> 874</a></span>
<span class="normal"><a href="#__codelineno-0-875"> 875</a></span>
<span class="normal"><a href="#__codelineno-0-876"> 876</a></span>
<span class="normal"><a href="#__codelineno-0-877"> 877</a></span>
<span class="normal"><a href="#__codelineno-0-878"> 878</a></span>
<span class="normal"><a href="#__codelineno-0-879"> 879</a></span>
<span class="normal"><a href="#__codelineno-0-880"> 880</a></span>
<span class="normal"><a href="#__codelineno-0-881"> 881</a></span>
<span class="normal"><a href="#__codelineno-0-882"> 882</a></span>
<span class="normal"><a href="#__codelineno-0-883"> 883</a></span>
<span class="normal"><a href="#__codelineno-0-884"> 884</a></span>
<span class="normal"><a href="#__codelineno-0-885"> 885</a></span>
<span class="normal"><a href="#__codelineno-0-886"> 886</a></span>
<span class="normal"><a href="#__codelineno-0-887"> 887</a></span>
<span class="normal"><a href="#__codelineno-0-888"> 888</a></span>
<span class="normal"><a href="#__codelineno-0-889"> 889</a></span>
<span class="normal"><a href="#__codelineno-0-890"> 890</a></span>
<span class="normal"><a href="#__codelineno-0-891"> 891</a></span>
<span class="normal"><a href="#__codelineno-0-892"> 892</a></span>
<span class="normal"><a href="#__codelineno-0-893"> 893</a></span>
<span class="normal"><a href="#__codelineno-0-894"> 894</a></span>
<span class="normal"><a href="#__codelineno-0-895"> 895</a></span>
<span class="normal"><a href="#__codelineno-0-896"> 896</a></span>
<span class="normal"><a href="#__codelineno-0-897"> 897</a></span>
<span class="normal"><a href="#__codelineno-0-898"> 898</a></span>
<span class="normal"><a href="#__codelineno-0-899"> 899</a></span>
<span class="normal"><a href="#__codelineno-0-900"> 900</a></span>
<span class="normal"><a href="#__codelineno-0-901"> 901</a></span>
<span class="normal"><a href="#__codelineno-0-902"> 902</a></span>
<span class="normal"><a href="#__codelineno-0-903"> 903</a></span>
<span class="normal"><a href="#__codelineno-0-904"> 904</a></span>
<span class="normal"><a href="#__codelineno-0-905"> 905</a></span>
<span class="normal"><a href="#__codelineno-0-906"> 906</a></span>
<span class="normal"><a href="#__codelineno-0-907"> 907</a></span>
<span class="normal"><a href="#__codelineno-0-908"> 908</a></span>
<span class="normal"><a href="#__codelineno-0-909"> 909</a></span>
<span class="normal"><a href="#__codelineno-0-910"> 910</a></span>
<span class="normal"><a href="#__codelineno-0-911"> 911</a></span>
<span class="normal"><a href="#__codelineno-0-912"> 912</a></span>
<span class="normal"><a href="#__codelineno-0-913"> 913</a></span>
<span class="normal"><a href="#__codelineno-0-914"> 914</a></span>
<span class="normal"><a href="#__codelineno-0-915"> 915</a></span>
<span class="normal"><a href="#__codelineno-0-916"> 916</a></span>
<span class="normal"><a href="#__codelineno-0-917"> 917</a></span>
<span class="normal"><a href="#__codelineno-0-918"> 918</a></span>
<span class="normal"><a href="#__codelineno-0-919"> 919</a></span>
<span class="normal"><a href="#__codelineno-0-920"> 920</a></span>
<span class="normal"><a href="#__codelineno-0-921"> 921</a></span>
<span class="normal"><a href="#__codelineno-0-922"> 922</a></span>
<span class="normal"><a href="#__codelineno-0-923"> 923</a></span>
<span class="normal"><a href="#__codelineno-0-924"> 924</a></span>
<span class="normal"><a href="#__codelineno-0-925"> 925</a></span>
<span class="normal"><a href="#__codelineno-0-926"> 926</a></span>
<span class="normal"><a href="#__codelineno-0-927"> 927</a></span>
<span class="normal"><a href="#__codelineno-0-928"> 928</a></span>
<span class="normal"><a href="#__codelineno-0-929"> 929</a></span>
<span class="normal"><a href="#__codelineno-0-930"> 930</a></span>
<span class="normal"><a href="#__codelineno-0-931"> 931</a></span>
<span class="normal"><a href="#__codelineno-0-932"> 932</a></span>
<span class="normal"><a href="#__codelineno-0-933"> 933</a></span>
<span class="normal"><a href="#__codelineno-0-934"> 934</a></span>
<span class="normal"><a href="#__codelineno-0-935"> 935</a></span>
<span class="normal"><a href="#__codelineno-0-936"> 936</a></span>
<span class="normal"><a href="#__codelineno-0-937"> 937</a></span>
<span class="normal"><a href="#__codelineno-0-938"> 938</a></span>
<span class="normal"><a href="#__codelineno-0-939"> 939</a></span>
<span class="normal"><a href="#__codelineno-0-940"> 940</a></span>
<span class="normal"><a href="#__codelineno-0-941"> 941</a></span>
<span class="normal"><a href="#__codelineno-0-942"> 942</a></span>
<span class="normal"><a href="#__codelineno-0-943"> 943</a></span>
<span class="normal"><a href="#__codelineno-0-944"> 944</a></span>
<span class="normal"><a href="#__codelineno-0-945"> 945</a></span>
<span class="normal"><a href="#__codelineno-0-946"> 946</a></span>
<span class="normal"><a href="#__codelineno-0-947"> 947</a></span>
<span class="normal"><a href="#__codelineno-0-948"> 948</a></span>
<span class="normal"><a href="#__codelineno-0-949"> 949</a></span>
<span class="normal"><a href="#__codelineno-0-950"> 950</a></span>
<span class="normal"><a href="#__codelineno-0-951"> 951</a></span>
<span class="normal"><a href="#__codelineno-0-952"> 952</a></span>
<span class="normal"><a href="#__codelineno-0-953"> 953</a></span>
<span class="normal"><a href="#__codelineno-0-954"> 954</a></span>
<span class="normal"><a href="#__codelineno-0-955"> 955</a></span>
<span class="normal"><a href="#__codelineno-0-956"> 956</a></span>
<span class="normal"><a href="#__codelineno-0-957"> 957</a></span>
<span class="normal"><a href="#__codelineno-0-958"> 958</a></span>
<span class="normal"><a href="#__codelineno-0-959"> 959</a></span>
<span class="normal"><a href="#__codelineno-0-960"> 960</a></span>
<span class="normal"><a href="#__codelineno-0-961"> 961</a></span>
<span class="normal"><a href="#__codelineno-0-962"> 962</a></span>
<span class="normal"><a href="#__codelineno-0-963"> 963</a></span>
<span class="normal"><a href="#__codelineno-0-964"> 964</a></span>
<span class="normal"><a href="#__codelineno-0-965"> 965</a></span>
<span class="normal"><a href="#__codelineno-0-966"> 966</a></span>
<span class="normal"><a href="#__codelineno-0-967"> 967</a></span>
<span class="normal"><a href="#__codelineno-0-968"> 968</a></span>
<span class="normal"><a href="#__codelineno-0-969"> 969</a></span>
<span class="normal"><a href="#__codelineno-0-970"> 970</a></span>
<span class="normal"><a href="#__codelineno-0-971"> 971</a></span>
<span class="normal"><a href="#__codelineno-0-972"> 972</a></span>
<span class="normal"><a href="#__codelineno-0-973"> 973</a></span>
<span class="normal"><a href="#__codelineno-0-974"> 974</a></span>
<span class="normal"><a href="#__codelineno-0-975"> 975</a></span>
<span class="normal"><a href="#__codelineno-0-976"> 976</a></span>
<span class="normal"><a href="#__codelineno-0-977"> 977</a></span>
<span class="normal"><a href="#__codelineno-0-978"> 978</a></span>
<span class="normal"><a href="#__codelineno-0-979"> 979</a></span>
<span class="normal"><a href="#__codelineno-0-980"> 980</a></span>
<span class="normal"><a href="#__codelineno-0-981"> 981</a></span>
<span class="normal"><a href="#__codelineno-0-982"> 982</a></span>
<span class="normal"><a href="#__codelineno-0-983"> 983</a></span>
<span class="normal"><a href="#__codelineno-0-984"> 984</a></span>
<span class="normal"><a href="#__codelineno-0-985"> 985</a></span>
<span class="normal"><a href="#__codelineno-0-986"> 986</a></span>
<span class="normal"><a href="#__codelineno-0-987"> 987</a></span>
<span class="normal"><a href="#__codelineno-0-988"> 988</a></span>
<span class="normal"><a href="#__codelineno-0-989"> 989</a></span>
<span class="normal"><a href="#__codelineno-0-990"> 990</a></span>
<span class="normal"><a href="#__codelineno-0-991"> 991</a></span>
<span class="normal"><a href="#__codelineno-0-992"> 992</a></span>
<span class="normal"><a href="#__codelineno-0-993"> 993</a></span>
<span class="normal"><a href="#__codelineno-0-994"> 994</a></span>
<span class="normal"><a href="#__codelineno-0-995"> 995</a></span>
<span class="normal"><a href="#__codelineno-0-996"> 996</a></span>
<span class="normal"><a href="#__codelineno-0-997"> 997</a></span>
<span class="normal"><a href="#__codelineno-0-998"> 998</a></span>
<span class="normal"><a href="#__codelineno-0-999"> 999</a></span>
<span class="normal"><a href="#__codelineno-0-1000">1000</a></span>
<span class="normal"><a href="#__codelineno-0-1001">1001</a></span>
<span class="normal"><a href="#__codelineno-0-1002">1002</a></span>
<span class="normal"><a href="#__codelineno-0-1003">1003</a></span>
<span class="normal"><a href="#__codelineno-0-1004">1004</a></span>
<span class="normal"><a href="#__codelineno-0-1005">1005</a></span>
<span class="normal"><a href="#__codelineno-0-1006">1006</a></span>
<span class="normal"><a href="#__codelineno-0-1007">1007</a></span>
<span class="normal"><a href="#__codelineno-0-1008">1008</a></span>
<span class="normal"><a href="#__codelineno-0-1009">1009</a></span>
<span class="normal"><a href="#__codelineno-0-1010">1010</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-851"><a id="__codelineno-0-851" name="__codelineno-0-851"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_create_route_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-852"><a id="__codelineno-0-852" name="__codelineno-0-852"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;create route&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-853"><a id="__codelineno-0-853" name="__codelineno-0-853"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Create route command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-854"><a id="__codelineno-0-854" name="__codelineno-0-854"></a>
</span><span id="__span-0-855"><a id="__codelineno-0-855" name="__codelineno-0-855"></a>    <span class="n">component_name</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-856"><a id="__codelineno-0-856" name="__codelineno-0-856"></a>    <span class="n">plugin_name</span><span class="p">,</span> <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">_detect_plugin_context</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">)</span>
</span><span id="__span-0-857"><a id="__codelineno-0-857" name="__codelineno-0-857"></a>
</span><span id="__span-0-858"><a id="__codelineno-0-858" name="__codelineno-0-858"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-859"><a id="__codelineno-0-859" name="__codelineno-0-859"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="p">:</span>
</span><span id="__span-0-860"><a id="__codelineno-0-860" name="__codelineno-0-860"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&#39; not found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-861"><a id="__codelineno-0-861" name="__codelineno-0-861"></a>            <span class="k">return</span>
</span><span id="__span-0-862"><a id="__codelineno-0-862" name="__codelineno-0-862"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-863"><a id="__codelineno-0-863" name="__codelineno-0-863"></a>            <span class="c1"># Interactive prompt for plugin</span>
</span><span id="__span-0-864"><a id="__codelineno-0-864" name="__codelineno-0-864"></a>            <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-865"><a id="__codelineno-0-865" name="__codelineno-0-865"></a>            <span class="k">if</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-866"><a id="__codelineno-0-866" name="__codelineno-0-866"></a>                <span class="n">available_plugins</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-867"><a id="__codelineno-0-867" name="__codelineno-0-867"></a>                    <span class="n">d</span><span class="o">.</span><span class="n">name</span>
</span><span id="__span-0-868"><a id="__codelineno-0-868" name="__codelineno-0-868"></a>                    <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span>
</span><span id="__span-0-869"><a id="__codelineno-0-869" name="__codelineno-0-869"></a>                    <span class="k">if</span> <span class="n">d</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
</span><span id="__span-0-870"><a id="__codelineno-0-870" name="__codelineno-0-870"></a>                    <span class="ow">and</span> <span class="p">(</span><span class="n">d</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-871"><a id="__codelineno-0-871" name="__codelineno-0-871"></a>                    <span class="ow">and</span> <span class="ow">not</span> <span class="n">d</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">)</span>
</span><span id="__span-0-872"><a id="__codelineno-0-872" name="__codelineno-0-872"></a>                <span class="p">]</span>
</span><span id="__span-0-873"><a id="__codelineno-0-873" name="__codelineno-0-873"></a>                <span class="k">if</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-874"><a id="__codelineno-0-874" name="__codelineno-0-874"></a>                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Available plugins:&quot;</span><span class="p">)</span>
</span><span id="__span-0-875"><a id="__codelineno-0-875" name="__codelineno-0-875"></a>                    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">plugin</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">,</span> <span class="mi">1</span><span class="p">):</span>
</span><span id="__span-0-876"><a id="__codelineno-0-876" name="__codelineno-0-876"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">plugin</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-877"><a id="__codelineno-0-877" name="__codelineno-0-877"></a>                    <span class="n">plugin_choice</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Select plugin (name or number)&quot;</span><span class="p">)</span>
</span><span id="__span-0-878"><a id="__codelineno-0-878" name="__codelineno-0-878"></a>                    <span class="k">if</span> <span class="n">plugin_choice</span> <span class="ow">and</span> <span class="n">plugin_choice</span><span class="o">.</span><span class="n">isdigit</span><span class="p">():</span>
</span><span id="__span-0-879"><a id="__codelineno-0-879" name="__codelineno-0-879"></a>                        <span class="n">idx</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">plugin_choice</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="__span-0-880"><a id="__codelineno-0-880" name="__codelineno-0-880"></a>                        <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">idx</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">):</span>
</span><span id="__span-0-881"><a id="__codelineno-0-881" name="__codelineno-0-881"></a>                            <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">available_plugins</span><span class="p">[</span><span class="n">idx</span><span class="p">]</span>
</span><span id="__span-0-882"><a id="__codelineno-0-882" name="__codelineno-0-882"></a>                            <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-883"><a id="__codelineno-0-883" name="__codelineno-0-883"></a>                    <span class="k">elif</span> <span class="n">plugin_choice</span> <span class="ow">in</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-884"><a id="__codelineno-0-884" name="__codelineno-0-884"></a>                        <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_choice</span>
</span><span id="__span-0-885"><a id="__codelineno-0-885" name="__codelineno-0-885"></a>                        <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_name</span>
</span><span id="__span-0-886"><a id="__codelineno-0-886" name="__codelineno-0-886"></a>
</span><span id="__span-0-887"><a id="__codelineno-0-887" name="__codelineno-0-887"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_name</span><span class="p">:</span>
</span><span id="__span-0-888"><a id="__codelineno-0-888" name="__codelineno-0-888"></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;No plugin specified and none could be auto-detected.&quot;</span><span class="p">)</span>
</span><span id="__span-0-889"><a id="__codelineno-0-889" name="__codelineno-0-889"></a>                <span class="k">return</span>
</span><span id="__span-0-890"><a id="__codelineno-0-890" name="__codelineno-0-890"></a>
</span><span id="__span-0-891"><a id="__codelineno-0-891" name="__codelineno-0-891"></a>    <span class="c1"># Get route path</span>
</span><span id="__span-0-892"><a id="__codelineno-0-892" name="__codelineno-0-892"></a>    <span class="n">route_path</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">path</span>
</span><span id="__span-0-893"><a id="__codelineno-0-893" name="__codelineno-0-893"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">route_path</span><span class="p">:</span>
</span><span id="__span-0-894"><a id="__codelineno-0-894" name="__codelineno-0-894"></a>        <span class="n">default_path</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;/</span><span class="si">{</span><span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-895"><a id="__codelineno-0-895" name="__codelineno-0-895"></a>        <span class="c1"># Check if stdin is available for interactive input</span>
</span><span id="__span-0-896"><a id="__codelineno-0-896" name="__codelineno-0-896"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-897"><a id="__codelineno-0-897" name="__codelineno-0-897"></a>            <span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>
</span><span id="__span-0-898"><a id="__codelineno-0-898" name="__codelineno-0-898"></a>
</span><span id="__span-0-899"><a id="__codelineno-0-899" name="__codelineno-0-899"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">isatty</span><span class="p">():</span>
</span><span id="__span-0-900"><a id="__codelineno-0-900" name="__codelineno-0-900"></a>                <span class="c1"># Non-interactive environment (like tests), use default</span>
</span><span id="__span-0-901"><a id="__codelineno-0-901" name="__codelineno-0-901"></a>                <span class="n">route_path</span> <span class="o">=</span> <span class="n">default_path</span>
</span><span id="__span-0-902"><a id="__codelineno-0-902" name="__codelineno-0-902"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-903"><a id="__codelineno-0-903" name="__codelineno-0-903"></a>                <span class="n">route_path</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Route path&quot;</span><span class="p">,</span> <span class="n">default_path</span><span class="p">)</span> <span class="ow">or</span> <span class="n">default_path</span>
</span><span id="__span-0-904"><a id="__codelineno-0-904" name="__codelineno-0-904"></a>        <span class="k">except</span> <span class="p">(</span><span class="ne">EOFError</span><span class="p">,</span> <span class="ne">OSError</span><span class="p">):</span>
</span><span id="__span-0-905"><a id="__codelineno-0-905" name="__codelineno-0-905"></a>            <span class="c1"># Fallback to default if input fails</span>
</span><span id="__span-0-906"><a id="__codelineno-0-906" name="__codelineno-0-906"></a>            <span class="n">route_path</span> <span class="o">=</span> <span class="n">default_path</span>
</span><span id="__span-0-907"><a id="__codelineno-0-907" name="__codelineno-0-907"></a>
</span><span id="__span-0-908"><a id="__codelineno-0-908" name="__codelineno-0-908"></a>    <span class="c1"># Ensure path starts with /</span>
</span><span id="__span-0-909"><a id="__codelineno-0-909" name="__codelineno-0-909"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">route_path</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">):</span>
</span><span id="__span-0-910"><a id="__codelineno-0-910" name="__codelineno-0-910"></a>        <span class="n">route_path</span> <span class="o">=</span> <span class="s2">&quot;/&quot;</span> <span class="o">+</span> <span class="n">route_path</span>
</span><span id="__span-0-911"><a id="__codelineno-0-911" name="__codelineno-0-911"></a>
</span><span id="__span-0-912"><a id="__codelineno-0-912" name="__codelineno-0-912"></a>    <span class="c1"># Get router name</span>
</span><span id="__span-0-913"><a id="__codelineno-0-913" name="__codelineno-0-913"></a>    <span class="n">router_name</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">router</span>
</span><span id="__span-0-914"><a id="__codelineno-0-914" name="__codelineno-0-914"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">router_name</span><span class="p">:</span>
</span><span id="__span-0-915"><a id="__codelineno-0-915" name="__codelineno-0-915"></a>        <span class="c1"># Check existing routers in plugin config</span>
</span><span id="__span-0-916"><a id="__codelineno-0-916" name="__codelineno-0-916"></a>        <span class="n">plugin_yaml_path</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span>
</span><span id="__span-0-917"><a id="__codelineno-0-917" name="__codelineno-0-917"></a>        <span class="n">existing_routers</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-918"><a id="__codelineno-0-918" name="__codelineno-0-918"></a>
</span><span id="__span-0-919"><a id="__codelineno-0-919" name="__codelineno-0-919"></a>        <span class="k">if</span> <span class="n">plugin_yaml_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-920"><a id="__codelineno-0-920" name="__codelineno-0-920"></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-921"><a id="__codelineno-0-921" name="__codelineno-0-921"></a>                <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_yaml_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-922"><a id="__codelineno-0-922" name="__codelineno-0-922"></a>                    <span class="n">plugin_config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-923"><a id="__codelineno-0-923" name="__codelineno-0-923"></a>
</span><span id="__span-0-924"><a id="__codelineno-0-924" name="__codelineno-0-924"></a>                <span class="n">routers</span> <span class="o">=</span> <span class="n">plugin_config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;routers&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="__span-0-925"><a id="__codelineno-0-925" name="__codelineno-0-925"></a>                <span class="n">existing_routers</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-926"><a id="__codelineno-0-926" name="__codelineno-0-926"></a>                    <span class="n">router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">)</span> <span class="k">for</span> <span class="n">router</span> <span class="ow">in</span> <span class="n">routers</span> <span class="k">if</span> <span class="n">router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">)</span>
</span><span id="__span-0-927"><a id="__codelineno-0-927" name="__codelineno-0-927"></a>                <span class="p">]</span>
</span><span id="__span-0-928"><a id="__codelineno-0-928" name="__codelineno-0-928"></a>            <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
</span><span id="__span-0-929"><a id="__codelineno-0-929" name="__codelineno-0-929"></a>                <span class="k">pass</span>
</span><span id="__span-0-930"><a id="__codelineno-0-930" name="__codelineno-0-930"></a>
</span><span id="__span-0-931"><a id="__codelineno-0-931" name="__codelineno-0-931"></a>        <span class="c1"># Check if stdin is available for interactive input</span>
</span><span id="__span-0-932"><a id="__codelineno-0-932" name="__codelineno-0-932"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-933"><a id="__codelineno-0-933" name="__codelineno-0-933"></a>            <span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>
</span><span id="__span-0-934"><a id="__codelineno-0-934" name="__codelineno-0-934"></a>
</span><span id="__span-0-935"><a id="__codelineno-0-935" name="__codelineno-0-935"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">isatty</span><span class="p">():</span>
</span><span id="__span-0-936"><a id="__codelineno-0-936" name="__codelineno-0-936"></a>                <span class="c1"># Non-interactive environment (like tests), use default</span>
</span><span id="__span-0-937"><a id="__codelineno-0-937" name="__codelineno-0-937"></a>                <span class="n">router_name</span> <span class="o">=</span> <span class="s2">&quot;main_router&quot;</span>
</span><span id="__span-0-938"><a id="__codelineno-0-938" name="__codelineno-0-938"></a>            <span class="k">elif</span> <span class="n">existing_routers</span><span class="p">:</span>
</span><span id="__span-0-939"><a id="__codelineno-0-939" name="__codelineno-0-939"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Existing routers:&quot;</span><span class="p">)</span>
</span><span id="__span-0-940"><a id="__codelineno-0-940" name="__codelineno-0-940"></a>                <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">router</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">existing_routers</span><span class="p">,</span> <span class="mi">1</span><span class="p">):</span>
</span><span id="__span-0-941"><a id="__codelineno-0-941" name="__codelineno-0-941"></a>                    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">router</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-942"><a id="__codelineno-0-942" name="__codelineno-0-942"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">existing_routers</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="si">}</span><span class="s2">. Create new router&quot;</span><span class="p">)</span>
</span><span id="__span-0-943"><a id="__codelineno-0-943" name="__codelineno-0-943"></a>
</span><span id="__span-0-944"><a id="__codelineno-0-944" name="__codelineno-0-944"></a>                <span class="n">router_choice</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Select router (name or number)&quot;</span><span class="p">,</span> <span class="s2">&quot;1&quot;</span><span class="p">)</span>
</span><span id="__span-0-945"><a id="__codelineno-0-945" name="__codelineno-0-945"></a>                <span class="k">if</span> <span class="n">router_choice</span> <span class="ow">and</span> <span class="n">router_choice</span><span class="o">.</span><span class="n">isdigit</span><span class="p">():</span>
</span><span id="__span-0-946"><a id="__codelineno-0-946" name="__codelineno-0-946"></a>                    <span class="n">idx</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">router_choice</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="__span-0-947"><a id="__codelineno-0-947" name="__codelineno-0-947"></a>                    <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">idx</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">existing_routers</span><span class="p">):</span>
</span><span id="__span-0-948"><a id="__codelineno-0-948" name="__codelineno-0-948"></a>                        <span class="n">router_name</span> <span class="o">=</span> <span class="n">existing_routers</span><span class="p">[</span><span class="n">idx</span><span class="p">]</span>
</span><span id="__span-0-949"><a id="__codelineno-0-949" name="__codelineno-0-949"></a>                    <span class="k">elif</span> <span class="n">idx</span> <span class="o">==</span> <span class="nb">len</span><span class="p">(</span><span class="n">existing_routers</span><span class="p">):</span>
</span><span id="__span-0-950"><a id="__codelineno-0-950" name="__codelineno-0-950"></a>                        <span class="n">router_name</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-951"><a id="__codelineno-0-951" name="__codelineno-0-951"></a>                            <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;New router name&quot;</span><span class="p">,</span> <span class="s2">&quot;main_router&quot;</span><span class="p">)</span>
</span><span id="__span-0-952"><a id="__codelineno-0-952" name="__codelineno-0-952"></a>                            <span class="ow">or</span> <span class="s2">&quot;main_router&quot;</span>
</span><span id="__span-0-953"><a id="__codelineno-0-953" name="__codelineno-0-953"></a>                        <span class="p">)</span>
</span><span id="__span-0-954"><a id="__codelineno-0-954" name="__codelineno-0-954"></a>                <span class="k">elif</span> <span class="n">router_choice</span> <span class="ow">in</span> <span class="n">existing_routers</span><span class="p">:</span>
</span><span id="__span-0-955"><a id="__codelineno-0-955" name="__codelineno-0-955"></a>                    <span class="n">router_name</span> <span class="o">=</span> <span class="n">router_choice</span>
</span><span id="__span-0-956"><a id="__codelineno-0-956" name="__codelineno-0-956"></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-957"><a id="__codelineno-0-957" name="__codelineno-0-957"></a>                    <span class="n">router_name</span> <span class="o">=</span> <span class="n">router_choice</span> <span class="ow">or</span> <span class="s2">&quot;main_router&quot;</span>
</span><span id="__span-0-958"><a id="__codelineno-0-958" name="__codelineno-0-958"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-959"><a id="__codelineno-0-959" name="__codelineno-0-959"></a>                <span class="n">router_name</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Router name&quot;</span><span class="p">,</span> <span class="s2">&quot;main_router&quot;</span><span class="p">)</span> <span class="ow">or</span> <span class="s2">&quot;main_router&quot;</span>
</span><span id="__span-0-960"><a id="__codelineno-0-960" name="__codelineno-0-960"></a>        <span class="k">except</span> <span class="p">(</span><span class="ne">EOFError</span><span class="p">,</span> <span class="ne">OSError</span><span class="p">):</span>
</span><span id="__span-0-961"><a id="__codelineno-0-961" name="__codelineno-0-961"></a>            <span class="c1"># Fallback to default if input fails</span>
</span><span id="__span-0-962"><a id="__codelineno-0-962" name="__codelineno-0-962"></a>            <span class="n">router_name</span> <span class="o">=</span> <span class="s2">&quot;main_router&quot;</span>
</span><span id="__span-0-963"><a id="__codelineno-0-963" name="__codelineno-0-963"></a>
</span><span id="__span-0-964"><a id="__codelineno-0-964" name="__codelineno-0-964"></a>    <span class="n">class_name</span> <span class="o">=</span> <span class="n">to_pascal_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span>
</span><span id="__span-0-965"><a id="__codelineno-0-965" name="__codelineno-0-965"></a>    <span class="n">file_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;route_</span><span class="si">{</span><span class="n">to_snake_case</span><span class="p">(</span><span class="n">component_name</span><span class="p">)</span><span class="si">}</span><span class="s2">.py&quot;</span>
</span><span id="__span-0-966"><a id="__codelineno-0-966" name="__codelineno-0-966"></a>    <span class="n">file_path</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="n">file_name</span>
</span><span id="__span-0-967"><a id="__codelineno-0-967" name="__codelineno-0-967"></a>
</span><span id="__span-0-968"><a id="__codelineno-0-968" name="__codelineno-0-968"></a>    <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">force</span><span class="p">:</span>
</span><span id="__span-0-969"><a id="__codelineno-0-969" name="__codelineno-0-969"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Warning: File &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39; already exists. Use --force to overwrite.&quot;</span><span class="p">)</span>
</span><span id="__span-0-970"><a id="__codelineno-0-970" name="__codelineno-0-970"></a>        <span class="k">return</span>
</span><span id="__span-0-971"><a id="__codelineno-0-971" name="__codelineno-0-971"></a>
</span><span id="__span-0-972"><a id="__codelineno-0-972" name="__codelineno-0-972"></a>    <span class="c1"># Create the route file</span>
</span><span id="__span-0-973"><a id="__codelineno-0-973" name="__codelineno-0-973"></a>    <span class="n">context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-974"><a id="__codelineno-0-974" name="__codelineno-0-974"></a>        <span class="s2">&quot;class_name&quot;</span><span class="p">:</span> <span class="n">class_name</span><span class="p">,</span>
</span><span id="__span-0-975"><a id="__codelineno-0-975" name="__codelineno-0-975"></a>        <span class="s2">&quot;route_name&quot;</span><span class="p">:</span> <span class="n">component_name</span><span class="p">,</span>
</span><span id="__span-0-976"><a id="__codelineno-0-976" name="__codelineno-0-976"></a>        <span class="s2">&quot;route_path&quot;</span><span class="p">:</span> <span class="n">route_path</span><span class="p">,</span>
</span><span id="__span-0-977"><a id="__codelineno-0-977" name="__codelineno-0-977"></a>    <span class="p">}</span>
</span><span id="__span-0-978"><a id="__codelineno-0-978" name="__codelineno-0-978"></a>
</span><span id="__span-0-979"><a id="__codelineno-0-979" name="__codelineno-0-979"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-980"><a id="__codelineno-0-980" name="__codelineno-0-980"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-981"><a id="__codelineno-0-981" name="__codelineno-0-981"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-982"><a id="__codelineno-0-982" name="__codelineno-0-982"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-983"><a id="__codelineno-0-983" name="__codelineno-0-983"></a>        <span class="p">)</span>
</span><span id="__span-0-984"><a id="__codelineno-0-984" name="__codelineno-0-984"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-985"><a id="__codelineno-0-985" name="__codelineno-0-985"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;route_main_py.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-986"><a id="__codelineno-0-986" name="__codelineno-0-986"></a>        <span class="n">content</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">context</span><span class="p">)</span>
</span><span id="__span-0-987"><a id="__codelineno-0-987" name="__codelineno-0-987"></a>
</span><span id="__span-0-988"><a id="__codelineno-0-988" name="__codelineno-0-988"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-989"><a id="__codelineno-0-989" name="__codelineno-0-989"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">content</span><span class="p">)</span>
</span><span id="__span-0-990"><a id="__codelineno-0-990" name="__codelineno-0-990"></a>
</span><span id="__span-0-991"><a id="__codelineno-0-991" name="__codelineno-0-991"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created &#39;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&#39;&quot;</span><span class="p">)</span>
</span><span id="__span-0-992"><a id="__codelineno-0-992" name="__codelineno-0-992"></a>
</span><span id="__span-0-993"><a id="__codelineno-0-993" name="__codelineno-0-993"></a>        <span class="c1"># Update plugin config with router name and path</span>
</span><span id="__span-0-994"><a id="__codelineno-0-994" name="__codelineno-0-994"></a>        <span class="n">entry_path</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">file_name</span><span class="p">[:</span><span class="o">-</span><span class="mi">3</span><span class="p">]</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">class_name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-995"><a id="__codelineno-0-995" name="__codelineno-0-995"></a>        <span class="n">route_config</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-996"><a id="__codelineno-0-996" name="__codelineno-0-996"></a>            <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="n">route_path</span><span class="p">,</span>
</span><span id="__span-0-997"><a id="__codelineno-0-997" name="__codelineno-0-997"></a>            <span class="s2">&quot;handler&quot;</span><span class="p">:</span> <span class="n">entry_path</span><span class="p">,</span>
</span><span id="__span-0-998"><a id="__codelineno-0-998" name="__codelineno-0-998"></a>            <span class="s2">&quot;router_name&quot;</span><span class="p">:</span> <span class="n">router_name</span><span class="p">,</span>
</span><span id="__span-0-999"><a id="__codelineno-0-999" name="__codelineno-0-999"></a>            <span class="s2">&quot;component_name&quot;</span><span class="p">:</span> <span class="n">component_name</span><span class="p">,</span>
</span><span id="__span-0-1000"><a id="__codelineno-0-1000" name="__codelineno-0-1000"></a>        <span class="p">}</span>
</span><span id="__span-0-1001"><a id="__codelineno-0-1001" name="__codelineno-0-1001"></a>
</span><span id="__span-0-1002"><a id="__codelineno-0-1002" name="__codelineno-0-1002"></a>        <span class="k">if</span> <span class="n">_update_plugin_config</span><span class="p">(</span><span class="n">plugin_dir</span><span class="p">,</span> <span class="s2">&quot;routers&quot;</span><span class="p">,</span> <span class="n">component_name</span><span class="p">,</span> <span class="n">route_config</span><span class="p">):</span>
</span><span id="__span-0-1003"><a id="__codelineno-0-1003" name="__codelineno-0-1003"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Added route to router &#39;</span><span class="si">{</span><span class="n">router_name</span><span class="si">}</span><span class="s2">&#39; in plugin configuration&quot;</span><span class="p">)</span>
</span><span id="__span-0-1004"><a id="__codelineno-0-1004" name="__codelineno-0-1004"></a>
</span><span id="__span-0-1005"><a id="__codelineno-0-1005" name="__codelineno-0-1005"></a>        <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-1006"><a id="__codelineno-0-1006" name="__codelineno-0-1006"></a>            <span class="sa">f</span><span class="s2">&quot;Route &#39;</span><span class="si">{</span><span class="n">component_name</span><span class="si">}</span><span class="s2">&#39; created successfully in plugin &#39;</span><span class="si">{</span><span class="n">plugin_name</span><span class="si">}</span><span class="s2">&#39; at path &#39;</span><span class="si">{</span><span class="n">route_path</span><span class="si">}</span><span class="s2">&#39;.&quot;</span>
</span><span id="__span-0-1007"><a id="__codelineno-0-1007" name="__codelineno-0-1007"></a>        <span class="p">)</span>
</span><span id="__span-0-1008"><a id="__codelineno-0-1008" name="__codelineno-0-1008"></a>
</span><span id="__span-0-1009"><a id="__codelineno-0-1009" name="__codelineno-0-1009"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1010"><a id="__codelineno-0-1010" name="__codelineno-0-1010"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating route: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_dev_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_dev_command</span>


  <span class="doc doc-labels">
      <small class="doc doc-label doc-label-async"><code>async</code></small>
  </span>

</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_dev_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'dev' command for enhanced development server.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1139">1139</a></span>
<span class="normal"><a href="#__codelineno-0-1140">1140</a></span>
<span class="normal"><a href="#__codelineno-0-1141">1141</a></span>
<span class="normal"><a href="#__codelineno-0-1142">1142</a></span>
<span class="normal"><a href="#__codelineno-0-1143">1143</a></span>
<span class="normal"><a href="#__codelineno-0-1144">1144</a></span>
<span class="normal"><a href="#__codelineno-0-1145">1145</a></span>
<span class="normal"><a href="#__codelineno-0-1146">1146</a></span>
<span class="normal"><a href="#__codelineno-0-1147">1147</a></span>
<span class="normal"><a href="#__codelineno-0-1148">1148</a></span>
<span class="normal"><a href="#__codelineno-0-1149">1149</a></span>
<span class="normal"><a href="#__codelineno-0-1150">1150</a></span>
<span class="normal"><a href="#__codelineno-0-1151">1151</a></span>
<span class="normal"><a href="#__codelineno-0-1152">1152</a></span>
<span class="normal"><a href="#__codelineno-0-1153">1153</a></span>
<span class="normal"><a href="#__codelineno-0-1154">1154</a></span>
<span class="normal"><a href="#__codelineno-0-1155">1155</a></span>
<span class="normal"><a href="#__codelineno-0-1156">1156</a></span>
<span class="normal"><a href="#__codelineno-0-1157">1157</a></span>
<span class="normal"><a href="#__codelineno-0-1158">1158</a></span>
<span class="normal"><a href="#__codelineno-0-1159">1159</a></span>
<span class="normal"><a href="#__codelineno-0-1160">1160</a></span>
<span class="normal"><a href="#__codelineno-0-1161">1161</a></span>
<span class="normal"><a href="#__codelineno-0-1162">1162</a></span>
<span class="normal"><a href="#__codelineno-0-1163">1163</a></span>
<span class="normal"><a href="#__codelineno-0-1164">1164</a></span>
<span class="normal"><a href="#__codelineno-0-1165">1165</a></span>
<span class="normal"><a href="#__codelineno-0-1166">1166</a></span>
<span class="normal"><a href="#__codelineno-0-1167">1167</a></span>
<span class="normal"><a href="#__codelineno-0-1168">1168</a></span>
<span class="normal"><a href="#__codelineno-0-1169">1169</a></span>
<span class="normal"><a href="#__codelineno-0-1170">1170</a></span>
<span class="normal"><a href="#__codelineno-0-1171">1171</a></span>
<span class="normal"><a href="#__codelineno-0-1172">1172</a></span>
<span class="normal"><a href="#__codelineno-0-1173">1173</a></span>
<span class="normal"><a href="#__codelineno-0-1174">1174</a></span>
<span class="normal"><a href="#__codelineno-0-1175">1175</a></span>
<span class="normal"><a href="#__codelineno-0-1176">1176</a></span>
<span class="normal"><a href="#__codelineno-0-1177">1177</a></span>
<span class="normal"><a href="#__codelineno-0-1178">1178</a></span>
<span class="normal"><a href="#__codelineno-0-1179">1179</a></span>
<span class="normal"><a href="#__codelineno-0-1180">1180</a></span>
<span class="normal"><a href="#__codelineno-0-1181">1181</a></span>
<span class="normal"><a href="#__codelineno-0-1182">1182</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1139"><a id="__codelineno-0-1139" name="__codelineno-0-1139"></a><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">handle_dev_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1140"><a id="__codelineno-0-1140" name="__codelineno-0-1140"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;dev&#39; command for enhanced development server.&quot;&quot;&quot;</span>
</span><span id="__span-0-1141"><a id="__codelineno-0-1141" name="__codelineno-0-1141"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Dev command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1142"><a id="__codelineno-0-1142" name="__codelineno-0-1142"></a>
</span><span id="__span-0-1143"><a id="__codelineno-0-1143" name="__codelineno-0-1143"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1144"><a id="__codelineno-0-1144" name="__codelineno-0-1144"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🚀 Starting Serv development server...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1145"><a id="__codelineno-0-1145" name="__codelineno-0-1145"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;📝 Development mode features:&quot;</span><span class="p">)</span>
</span><span id="__span-0-1146"><a id="__codelineno-0-1146" name="__codelineno-0-1146"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;   • Auto-reload enabled (unless --no-reload)&quot;</span><span class="p">)</span>
</span><span id="__span-0-1147"><a id="__codelineno-0-1147" name="__codelineno-0-1147"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;   • Enhanced error reporting&quot;</span><span class="p">)</span>
</span><span id="__span-0-1148"><a id="__codelineno-0-1148" name="__codelineno-0-1148"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;   • Development mode enabled&quot;</span><span class="p">)</span>
</span><span id="__span-0-1149"><a id="__codelineno-0-1149" name="__codelineno-0-1149"></a>
</span><span id="__span-0-1150"><a id="__codelineno-0-1150" name="__codelineno-0-1150"></a>        <span class="n">app</span> <span class="o">=</span> <span class="n">_get_configured_app</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">app</span><span class="p">,</span> <span class="n">args_ns</span><span class="p">)</span>
</span><span id="__span-0-1151"><a id="__codelineno-0-1151" name="__codelineno-0-1151"></a>
</span><span id="__span-0-1152"><a id="__codelineno-0-1152" name="__codelineno-0-1152"></a>        <span class="c1"># Force development mode</span>
</span><span id="__span-0-1153"><a id="__codelineno-0-1153" name="__codelineno-0-1153"></a>        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">app</span><span class="p">,</span> <span class="s2">&quot;dev_mode&quot;</span><span class="p">):</span>
</span><span id="__span-0-1154"><a id="__codelineno-0-1154" name="__codelineno-0-1154"></a>            <span class="n">app</span><span class="o">.</span><span class="n">dev_mode</span> <span class="o">=</span> <span class="kc">True</span>
</span><span id="__span-0-1155"><a id="__codelineno-0-1155" name="__codelineno-0-1155"></a>
</span><span id="__span-0-1156"><a id="__codelineno-0-1156" name="__codelineno-0-1156"></a>        <span class="c1"># Configure uvicorn for development</span>
</span><span id="__span-0-1157"><a id="__codelineno-0-1157" name="__codelineno-0-1157"></a>        <span class="n">reload</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">no_reload</span>
</span><span id="__span-0-1158"><a id="__codelineno-0-1158" name="__codelineno-0-1158"></a>        <span class="n">uvicorn_config</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-1159"><a id="__codelineno-0-1159" name="__codelineno-0-1159"></a>            <span class="s2">&quot;app&quot;</span><span class="p">:</span> <span class="n">app</span><span class="p">,</span>
</span><span id="__span-0-1160"><a id="__codelineno-0-1160" name="__codelineno-0-1160"></a>            <span class="s2">&quot;host&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">host</span><span class="p">,</span>
</span><span id="__span-0-1161"><a id="__codelineno-0-1161" name="__codelineno-0-1161"></a>            <span class="s2">&quot;port&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">port</span><span class="p">,</span>
</span><span id="__span-0-1162"><a id="__codelineno-0-1162" name="__codelineno-0-1162"></a>            <span class="s2">&quot;reload&quot;</span><span class="p">:</span> <span class="n">reload</span><span class="p">,</span>
</span><span id="__span-0-1163"><a id="__codelineno-0-1163" name="__codelineno-0-1163"></a>            <span class="s2">&quot;workers&quot;</span><span class="p">:</span> <span class="mi">1</span>
</span><span id="__span-0-1164"><a id="__codelineno-0-1164" name="__codelineno-0-1164"></a>            <span class="k">if</span> <span class="n">reload</span>
</span><span id="__span-0-1165"><a id="__codelineno-0-1165" name="__codelineno-0-1165"></a>            <span class="k">else</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">workers</span><span class="p">,</span>  <span class="c1"># Reload doesn&#39;t work with multiple workers</span>
</span><span id="__span-0-1166"><a id="__codelineno-0-1166" name="__codelineno-0-1166"></a>            <span class="s2">&quot;log_level&quot;</span><span class="p">:</span> <span class="s2">&quot;debug&quot;</span><span class="p">,</span>
</span><span id="__span-0-1167"><a id="__codelineno-0-1167" name="__codelineno-0-1167"></a>            <span class="s2">&quot;access_log&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
</span><span id="__span-0-1168"><a id="__codelineno-0-1168" name="__codelineno-0-1168"></a>        <span class="p">}</span>
</span><span id="__span-0-1169"><a id="__codelineno-0-1169" name="__codelineno-0-1169"></a>
</span><span id="__span-0-1170"><a id="__codelineno-0-1170" name="__codelineno-0-1170"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Starting development server on </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1171"><a id="__codelineno-0-1171" name="__codelineno-0-1171"></a>        <span class="k">if</span> <span class="n">reload</span><span class="p">:</span>
</span><span id="__span-0-1172"><a id="__codelineno-0-1172" name="__codelineno-0-1172"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🔄 Auto-reload is enabled - files will be watched for changes&quot;</span><span class="p">)</span>
</span><span id="__span-0-1173"><a id="__codelineno-0-1173" name="__codelineno-0-1173"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1174"><a id="__codelineno-0-1174" name="__codelineno-0-1174"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  Auto-reload is disabled&quot;</span><span class="p">)</span>
</span><span id="__span-0-1175"><a id="__codelineno-0-1175" name="__codelineno-0-1175"></a>
</span><span id="__span-0-1176"><a id="__codelineno-0-1176" name="__codelineno-0-1176"></a>        <span class="c1"># Start the server</span>
</span><span id="__span-0-1177"><a id="__codelineno-0-1177" name="__codelineno-0-1177"></a>        <span class="n">server</span> <span class="o">=</span> <span class="n">uvicorn</span><span class="o">.</span><span class="n">Server</span><span class="p">(</span><span class="n">uvicorn</span><span class="o">.</span><span class="n">Config</span><span class="p">(</span><span class="o">**</span><span class="n">uvicorn_config</span><span class="p">))</span>
</span><span id="__span-0-1178"><a id="__codelineno-0-1178" name="__codelineno-0-1178"></a>        <span class="k">await</span> <span class="n">server</span><span class="o">.</span><span class="n">serve</span><span class="p">()</span>
</span><span id="__span-0-1179"><a id="__codelineno-0-1179" name="__codelineno-0-1179"></a>
</span><span id="__span-0-1180"><a id="__codelineno-0-1180" name="__codelineno-0-1180"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1181"><a id="__codelineno-0-1181" name="__codelineno-0-1181"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error starting development server: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1182"><a id="__codelineno-0-1182" name="__codelineno-0-1182"></a>        <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_disable_plugin_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_disable_plugin_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_disable_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'disable-plugin' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-380">380</a></span>
<span class="normal"><a href="#__codelineno-0-381">381</a></span>
<span class="normal"><a href="#__codelineno-0-382">382</a></span>
<span class="normal"><a href="#__codelineno-0-383">383</a></span>
<span class="normal"><a href="#__codelineno-0-384">384</a></span>
<span class="normal"><a href="#__codelineno-0-385">385</a></span>
<span class="normal"><a href="#__codelineno-0-386">386</a></span>
<span class="normal"><a href="#__codelineno-0-387">387</a></span>
<span class="normal"><a href="#__codelineno-0-388">388</a></span>
<span class="normal"><a href="#__codelineno-0-389">389</a></span>
<span class="normal"><a href="#__codelineno-0-390">390</a></span>
<span class="normal"><a href="#__codelineno-0-391">391</a></span>
<span class="normal"><a href="#__codelineno-0-392">392</a></span>
<span class="normal"><a href="#__codelineno-0-393">393</a></span>
<span class="normal"><a href="#__codelineno-0-394">394</a></span>
<span class="normal"><a href="#__codelineno-0-395">395</a></span>
<span class="normal"><a href="#__codelineno-0-396">396</a></span>
<span class="normal"><a href="#__codelineno-0-397">397</a></span>
<span class="normal"><a href="#__codelineno-0-398">398</a></span>
<span class="normal"><a href="#__codelineno-0-399">399</a></span>
<span class="normal"><a href="#__codelineno-0-400">400</a></span>
<span class="normal"><a href="#__codelineno-0-401">401</a></span>
<span class="normal"><a href="#__codelineno-0-402">402</a></span>
<span class="normal"><a href="#__codelineno-0-403">403</a></span>
<span class="normal"><a href="#__codelineno-0-404">404</a></span>
<span class="normal"><a href="#__codelineno-0-405">405</a></span>
<span class="normal"><a href="#__codelineno-0-406">406</a></span>
<span class="normal"><a href="#__codelineno-0-407">407</a></span>
<span class="normal"><a href="#__codelineno-0-408">408</a></span>
<span class="normal"><a href="#__codelineno-0-409">409</a></span>
<span class="normal"><a href="#__codelineno-0-410">410</a></span>
<span class="normal"><a href="#__codelineno-0-411">411</a></span>
<span class="normal"><a href="#__codelineno-0-412">412</a></span>
<span class="normal"><a href="#__codelineno-0-413">413</a></span>
<span class="normal"><a href="#__codelineno-0-414">414</a></span>
<span class="normal"><a href="#__codelineno-0-415">415</a></span>
<span class="normal"><a href="#__codelineno-0-416">416</a></span>
<span class="normal"><a href="#__codelineno-0-417">417</a></span>
<span class="normal"><a href="#__codelineno-0-418">418</a></span>
<span class="normal"><a href="#__codelineno-0-419">419</a></span>
<span class="normal"><a href="#__codelineno-0-420">420</a></span>
<span class="normal"><a href="#__codelineno-0-421">421</a></span>
<span class="normal"><a href="#__codelineno-0-422">422</a></span>
<span class="normal"><a href="#__codelineno-0-423">423</a></span>
<span class="normal"><a href="#__codelineno-0-424">424</a></span>
<span class="normal"><a href="#__codelineno-0-425">425</a></span>
<span class="normal"><a href="#__codelineno-0-426">426</a></span>
<span class="normal"><a href="#__codelineno-0-427">427</a></span>
<span class="normal"><a href="#__codelineno-0-428">428</a></span>
<span class="normal"><a href="#__codelineno-0-429">429</a></span>
<span class="normal"><a href="#__codelineno-0-430">430</a></span>
<span class="normal"><a href="#__codelineno-0-431">431</a></span>
<span class="normal"><a href="#__codelineno-0-432">432</a></span>
<span class="normal"><a href="#__codelineno-0-433">433</a></span>
<span class="normal"><a href="#__codelineno-0-434">434</a></span>
<span class="normal"><a href="#__codelineno-0-435">435</a></span>
<span class="normal"><a href="#__codelineno-0-436">436</a></span>
<span class="normal"><a href="#__codelineno-0-437">437</a></span>
<span class="normal"><a href="#__codelineno-0-438">438</a></span>
<span class="normal"><a href="#__codelineno-0-439">439</a></span>
<span class="normal"><a href="#__codelineno-0-440">440</a></span>
<span class="normal"><a href="#__codelineno-0-441">441</a></span>
<span class="normal"><a href="#__codelineno-0-442">442</a></span>
<span class="normal"><a href="#__codelineno-0-443">443</a></span>
<span class="normal"><a href="#__codelineno-0-444">444</a></span>
<span class="normal"><a href="#__codelineno-0-445">445</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-380"><a id="__codelineno-0-380" name="__codelineno-0-380"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_disable_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-381"><a id="__codelineno-0-381" name="__codelineno-0-381"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;disable-plugin&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-382"><a id="__codelineno-0-382" name="__codelineno-0-382"></a>    <span class="n">plugin_identifier</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin_identifier</span>
</span><span id="__span-0-383"><a id="__codelineno-0-383" name="__codelineno-0-383"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Attempting to disable plugin: &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39;...&quot;</span><span class="p">)</span>
</span><span id="__span-0-384"><a id="__codelineno-0-384" name="__codelineno-0-384"></a>
</span><span id="__span-0-385"><a id="__codelineno-0-385" name="__codelineno-0-385"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-386"><a id="__codelineno-0-386" name="__codelineno-0-386"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-387"><a id="__codelineno-0-387" name="__codelineno-0-387"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
</span><span id="__span-0-388"><a id="__codelineno-0-388" name="__codelineno-0-388"></a>            <span class="sa">f</span><span class="s2">&quot;Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found. Please run &#39;serv init&#39; first.&quot;</span>
</span><span id="__span-0-389"><a id="__codelineno-0-389" name="__codelineno-0-389"></a>        <span class="p">)</span>
</span><span id="__span-0-390"><a id="__codelineno-0-390" name="__codelineno-0-390"></a>        <span class="k">return</span>
</span><span id="__span-0-391"><a id="__codelineno-0-391" name="__codelineno-0-391"></a>
</span><span id="__span-0-392"><a id="__codelineno-0-392" name="__codelineno-0-392"></a>    <span class="c1"># Convert plugin identifier to directory name</span>
</span><span id="__span-0-393"><a id="__codelineno-0-393" name="__codelineno-0-393"></a>    <span class="n">plugin_id</span> <span class="o">=</span> <span class="n">to_snake_case</span><span class="p">(</span><span class="n">plugin_identifier</span><span class="p">)</span>
</span><span id="__span-0-394"><a id="__codelineno-0-394" name="__codelineno-0-394"></a>    <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_identifier</span>
</span><span id="__span-0-395"><a id="__codelineno-0-395" name="__codelineno-0-395"></a>
</span><span id="__span-0-396"><a id="__codelineno-0-396" name="__codelineno-0-396"></a>    <span class="c1"># Check if plugin directory exists and get human name</span>
</span><span id="__span-0-397"><a id="__codelineno-0-397" name="__codelineno-0-397"></a>    <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-398"><a id="__codelineno-0-398" name="__codelineno-0-398"></a>    <span class="n">plugin_yaml_path</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_id</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span>
</span><span id="__span-0-399"><a id="__codelineno-0-399" name="__codelineno-0-399"></a>
</span><span id="__span-0-400"><a id="__codelineno-0-400" name="__codelineno-0-400"></a>    <span class="k">if</span> <span class="n">plugin_yaml_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-401"><a id="__codelineno-0-401" name="__codelineno-0-401"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-402"><a id="__codelineno-0-402" name="__codelineno-0-402"></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_yaml_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-403"><a id="__codelineno-0-403" name="__codelineno-0-403"></a>                <span class="n">plugin_meta</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-404"><a id="__codelineno-0-404" name="__codelineno-0-404"></a>            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plugin_meta</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
</span><span id="__span-0-405"><a id="__codelineno-0-405" name="__codelineno-0-405"></a>                <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="n">plugin_identifier</span><span class="p">)</span>
</span><span id="__span-0-406"><a id="__codelineno-0-406" name="__codelineno-0-406"></a>        <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
</span><span id="__span-0-407"><a id="__codelineno-0-407" name="__codelineno-0-407"></a>            <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_identifier</span>
</span><span id="__span-0-408"><a id="__codelineno-0-408" name="__codelineno-0-408"></a>
</span><span id="__span-0-409"><a id="__codelineno-0-409" name="__codelineno-0-409"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-410"><a id="__codelineno-0-410" name="__codelineno-0-410"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-411"><a id="__codelineno-0-411" name="__codelineno-0-411"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-412"><a id="__codelineno-0-412" name="__codelineno-0-412"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-413"><a id="__codelineno-0-413" name="__codelineno-0-413"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error reading config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-414"><a id="__codelineno-0-414" name="__codelineno-0-414"></a>        <span class="k">return</span>
</span><span id="__span-0-415"><a id="__codelineno-0-415" name="__codelineno-0-415"></a>
</span><span id="__span-0-416"><a id="__codelineno-0-416" name="__codelineno-0-416"></a>    <span class="n">plugins</span> <span class="o">=</span> <span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugins&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="__span-0-417"><a id="__codelineno-0-417" name="__codelineno-0-417"></a>    <span class="n">original_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">plugins</span><span class="p">)</span>
</span><span id="__span-0-418"><a id="__codelineno-0-418" name="__codelineno-0-418"></a>
</span><span id="__span-0-419"><a id="__codelineno-0-419" name="__codelineno-0-419"></a>    <span class="c1"># Remove the plugin</span>
</span><span id="__span-0-420"><a id="__codelineno-0-420" name="__codelineno-0-420"></a>    <span class="n">plugins</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-421"><a id="__codelineno-0-421" name="__codelineno-0-421"></a>        <span class="n">p</span>
</span><span id="__span-0-422"><a id="__codelineno-0-422" name="__codelineno-0-422"></a>        <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">plugins</span>
</span><span id="__span-0-423"><a id="__codelineno-0-423" name="__codelineno-0-423"></a>        <span class="k">if</span> <span class="p">(</span>
</span><span id="__span-0-424"><a id="__codelineno-0-424" name="__codelineno-0-424"></a>            <span class="p">(</span>
</span><span id="__span-0-425"><a id="__codelineno-0-425" name="__codelineno-0-425"></a>                <span class="nb">isinstance</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="nb">dict</span><span class="p">)</span>
</span><span id="__span-0-426"><a id="__codelineno-0-426" name="__codelineno-0-426"></a>                <span class="ow">and</span> <span class="n">p</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugin&quot;</span><span class="p">)</span> <span class="ow">not</span> <span class="ow">in</span> <span class="p">[</span><span class="n">plugin_id</span><span class="p">,</span> <span class="n">plugin_identifier</span><span class="p">]</span>
</span><span id="__span-0-427"><a id="__codelineno-0-427" name="__codelineno-0-427"></a>            <span class="p">)</span>
</span><span id="__span-0-428"><a id="__codelineno-0-428" name="__codelineno-0-428"></a>            <span class="ow">or</span> <span class="p">(</span><span class="nb">isinstance</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span> <span class="ow">and</span> <span class="n">p</span> <span class="ow">not</span> <span class="ow">in</span> <span class="p">[</span><span class="n">plugin_id</span><span class="p">,</span> <span class="n">plugin_identifier</span><span class="p">])</span>
</span><span id="__span-0-429"><a id="__codelineno-0-429" name="__codelineno-0-429"></a>        <span class="p">)</span>
</span><span id="__span-0-430"><a id="__codelineno-0-430" name="__codelineno-0-430"></a>    <span class="p">]</span>
</span><span id="__span-0-431"><a id="__codelineno-0-431" name="__codelineno-0-431"></a>
</span><span id="__span-0-432"><a id="__codelineno-0-432" name="__codelineno-0-432"></a>    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">plugins</span><span class="p">)</span> <span class="o">==</span> <span class="n">original_count</span><span class="p">:</span>
</span><span id="__span-0-433"><a id="__codelineno-0-433" name="__codelineno-0-433"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; was not found in the configuration.&quot;</span><span class="p">)</span>
</span><span id="__span-0-434"><a id="__codelineno-0-434" name="__codelineno-0-434"></a>        <span class="k">return</span>
</span><span id="__span-0-435"><a id="__codelineno-0-435" name="__codelineno-0-435"></a>
</span><span id="__span-0-436"><a id="__codelineno-0-436" name="__codelineno-0-436"></a>    <span class="n">config</span><span class="p">[</span><span class="s2">&quot;plugins&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plugins</span>
</span><span id="__span-0-437"><a id="__codelineno-0-437" name="__codelineno-0-437"></a>
</span><span id="__span-0-438"><a id="__codelineno-0-438" name="__codelineno-0-438"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-439"><a id="__codelineno-0-439" name="__codelineno-0-439"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-440"><a id="__codelineno-0-440" name="__codelineno-0-440"></a>            <span class="n">yaml</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">default_flow_style</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="__span-0-441"><a id="__codelineno-0-441" name="__codelineno-0-441"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; disabled successfully.&quot;</span><span class="p">)</span>
</span><span id="__span-0-442"><a id="__codelineno-0-442" name="__codelineno-0-442"></a>        <span class="k">if</span> <span class="n">plugin_name_human</span> <span class="ow">and</span> <span class="n">plugin_name_human</span> <span class="o">!=</span> <span class="n">plugin_identifier</span><span class="p">:</span>
</span><span id="__span-0-443"><a id="__codelineno-0-443" name="__codelineno-0-443"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Human name: </span><span class="si">{</span><span class="n">plugin_name_human</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-444"><a id="__codelineno-0-444" name="__codelineno-0-444"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-445"><a id="__codelineno-0-445" name="__codelineno-0-445"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error writing config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_enable_plugin_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_enable_plugin_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_enable_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'enable-plugin' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-311">311</a></span>
<span class="normal"><a href="#__codelineno-0-312">312</a></span>
<span class="normal"><a href="#__codelineno-0-313">313</a></span>
<span class="normal"><a href="#__codelineno-0-314">314</a></span>
<span class="normal"><a href="#__codelineno-0-315">315</a></span>
<span class="normal"><a href="#__codelineno-0-316">316</a></span>
<span class="normal"><a href="#__codelineno-0-317">317</a></span>
<span class="normal"><a href="#__codelineno-0-318">318</a></span>
<span class="normal"><a href="#__codelineno-0-319">319</a></span>
<span class="normal"><a href="#__codelineno-0-320">320</a></span>
<span class="normal"><a href="#__codelineno-0-321">321</a></span>
<span class="normal"><a href="#__codelineno-0-322">322</a></span>
<span class="normal"><a href="#__codelineno-0-323">323</a></span>
<span class="normal"><a href="#__codelineno-0-324">324</a></span>
<span class="normal"><a href="#__codelineno-0-325">325</a></span>
<span class="normal"><a href="#__codelineno-0-326">326</a></span>
<span class="normal"><a href="#__codelineno-0-327">327</a></span>
<span class="normal"><a href="#__codelineno-0-328">328</a></span>
<span class="normal"><a href="#__codelineno-0-329">329</a></span>
<span class="normal"><a href="#__codelineno-0-330">330</a></span>
<span class="normal"><a href="#__codelineno-0-331">331</a></span>
<span class="normal"><a href="#__codelineno-0-332">332</a></span>
<span class="normal"><a href="#__codelineno-0-333">333</a></span>
<span class="normal"><a href="#__codelineno-0-334">334</a></span>
<span class="normal"><a href="#__codelineno-0-335">335</a></span>
<span class="normal"><a href="#__codelineno-0-336">336</a></span>
<span class="normal"><a href="#__codelineno-0-337">337</a></span>
<span class="normal"><a href="#__codelineno-0-338">338</a></span>
<span class="normal"><a href="#__codelineno-0-339">339</a></span>
<span class="normal"><a href="#__codelineno-0-340">340</a></span>
<span class="normal"><a href="#__codelineno-0-341">341</a></span>
<span class="normal"><a href="#__codelineno-0-342">342</a></span>
<span class="normal"><a href="#__codelineno-0-343">343</a></span>
<span class="normal"><a href="#__codelineno-0-344">344</a></span>
<span class="normal"><a href="#__codelineno-0-345">345</a></span>
<span class="normal"><a href="#__codelineno-0-346">346</a></span>
<span class="normal"><a href="#__codelineno-0-347">347</a></span>
<span class="normal"><a href="#__codelineno-0-348">348</a></span>
<span class="normal"><a href="#__codelineno-0-349">349</a></span>
<span class="normal"><a href="#__codelineno-0-350">350</a></span>
<span class="normal"><a href="#__codelineno-0-351">351</a></span>
<span class="normal"><a href="#__codelineno-0-352">352</a></span>
<span class="normal"><a href="#__codelineno-0-353">353</a></span>
<span class="normal"><a href="#__codelineno-0-354">354</a></span>
<span class="normal"><a href="#__codelineno-0-355">355</a></span>
<span class="normal"><a href="#__codelineno-0-356">356</a></span>
<span class="normal"><a href="#__codelineno-0-357">357</a></span>
<span class="normal"><a href="#__codelineno-0-358">358</a></span>
<span class="normal"><a href="#__codelineno-0-359">359</a></span>
<span class="normal"><a href="#__codelineno-0-360">360</a></span>
<span class="normal"><a href="#__codelineno-0-361">361</a></span>
<span class="normal"><a href="#__codelineno-0-362">362</a></span>
<span class="normal"><a href="#__codelineno-0-363">363</a></span>
<span class="normal"><a href="#__codelineno-0-364">364</a></span>
<span class="normal"><a href="#__codelineno-0-365">365</a></span>
<span class="normal"><a href="#__codelineno-0-366">366</a></span>
<span class="normal"><a href="#__codelineno-0-367">367</a></span>
<span class="normal"><a href="#__codelineno-0-368">368</a></span>
<span class="normal"><a href="#__codelineno-0-369">369</a></span>
<span class="normal"><a href="#__codelineno-0-370">370</a></span>
<span class="normal"><a href="#__codelineno-0-371">371</a></span>
<span class="normal"><a href="#__codelineno-0-372">372</a></span>
<span class="normal"><a href="#__codelineno-0-373">373</a></span>
<span class="normal"><a href="#__codelineno-0-374">374</a></span>
<span class="normal"><a href="#__codelineno-0-375">375</a></span>
<span class="normal"><a href="#__codelineno-0-376">376</a></span>
<span class="normal"><a href="#__codelineno-0-377">377</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-311"><a id="__codelineno-0-311" name="__codelineno-0-311"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_enable_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-312"><a id="__codelineno-0-312" name="__codelineno-0-312"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;enable-plugin&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-313"><a id="__codelineno-0-313" name="__codelineno-0-313"></a>    <span class="n">plugin_identifier</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin_identifier</span>
</span><span id="__span-0-314"><a id="__codelineno-0-314" name="__codelineno-0-314"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Attempting to enable plugin: &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39;...&quot;</span><span class="p">)</span>
</span><span id="__span-0-315"><a id="__codelineno-0-315" name="__codelineno-0-315"></a>
</span><span id="__span-0-316"><a id="__codelineno-0-316" name="__codelineno-0-316"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-317"><a id="__codelineno-0-317" name="__codelineno-0-317"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-318"><a id="__codelineno-0-318" name="__codelineno-0-318"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
</span><span id="__span-0-319"><a id="__codelineno-0-319" name="__codelineno-0-319"></a>            <span class="sa">f</span><span class="s2">&quot;Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found. Please run &#39;serv init&#39; first.&quot;</span>
</span><span id="__span-0-320"><a id="__codelineno-0-320" name="__codelineno-0-320"></a>        <span class="p">)</span>
</span><span id="__span-0-321"><a id="__codelineno-0-321" name="__codelineno-0-321"></a>        <span class="k">return</span>
</span><span id="__span-0-322"><a id="__codelineno-0-322" name="__codelineno-0-322"></a>
</span><span id="__span-0-323"><a id="__codelineno-0-323" name="__codelineno-0-323"></a>    <span class="c1"># Convert plugin identifier to directory name</span>
</span><span id="__span-0-324"><a id="__codelineno-0-324" name="__codelineno-0-324"></a>    <span class="n">plugin_id</span> <span class="o">=</span> <span class="n">to_snake_case</span><span class="p">(</span><span class="n">plugin_identifier</span><span class="p">)</span>
</span><span id="__span-0-325"><a id="__codelineno-0-325" name="__codelineno-0-325"></a>    <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_identifier</span>
</span><span id="__span-0-326"><a id="__codelineno-0-326" name="__codelineno-0-326"></a>
</span><span id="__span-0-327"><a id="__codelineno-0-327" name="__codelineno-0-327"></a>    <span class="c1"># Check if plugin directory exists</span>
</span><span id="__span-0-328"><a id="__codelineno-0-328" name="__codelineno-0-328"></a>    <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-329"><a id="__codelineno-0-329" name="__codelineno-0-329"></a>    <span class="n">plugin_yaml_path</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">plugin_id</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span>
</span><span id="__span-0-330"><a id="__codelineno-0-330" name="__codelineno-0-330"></a>
</span><span id="__span-0-331"><a id="__codelineno-0-331" name="__codelineno-0-331"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_yaml_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-332"><a id="__codelineno-0-332" name="__codelineno-0-332"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
</span><span id="__span-0-333"><a id="__codelineno-0-333" name="__codelineno-0-333"></a>            <span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; not found. Expected plugin.yaml at &#39;</span><span class="si">{</span><span class="n">plugin_yaml_path</span><span class="si">}</span><span class="s2">&#39;.&quot;</span>
</span><span id="__span-0-334"><a id="__codelineno-0-334" name="__codelineno-0-334"></a>        <span class="p">)</span>
</span><span id="__span-0-335"><a id="__codelineno-0-335" name="__codelineno-0-335"></a>        <span class="k">return</span>
</span><span id="__span-0-336"><a id="__codelineno-0-336" name="__codelineno-0-336"></a>
</span><span id="__span-0-337"><a id="__codelineno-0-337" name="__codelineno-0-337"></a>    <span class="c1"># Get human name from plugin.yaml</span>
</span><span id="__span-0-338"><a id="__codelineno-0-338" name="__codelineno-0-338"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-339"><a id="__codelineno-0-339" name="__codelineno-0-339"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_yaml_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-340"><a id="__codelineno-0-340" name="__codelineno-0-340"></a>            <span class="n">plugin_meta</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-341"><a id="__codelineno-0-341" name="__codelineno-0-341"></a>        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plugin_meta</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
</span><span id="__span-0-342"><a id="__codelineno-0-342" name="__codelineno-0-342"></a>            <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="n">plugin_identifier</span><span class="p">)</span>
</span><span id="__span-0-343"><a id="__codelineno-0-343" name="__codelineno-0-343"></a>    <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
</span><span id="__span-0-344"><a id="__codelineno-0-344" name="__codelineno-0-344"></a>        <span class="n">plugin_name_human</span> <span class="o">=</span> <span class="n">plugin_identifier</span>
</span><span id="__span-0-345"><a id="__codelineno-0-345" name="__codelineno-0-345"></a>
</span><span id="__span-0-346"><a id="__codelineno-0-346" name="__codelineno-0-346"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-347"><a id="__codelineno-0-347" name="__codelineno-0-347"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-348"><a id="__codelineno-0-348" name="__codelineno-0-348"></a>            <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-349"><a id="__codelineno-0-349" name="__codelineno-0-349"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-350"><a id="__codelineno-0-350" name="__codelineno-0-350"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error reading config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-351"><a id="__codelineno-0-351" name="__codelineno-0-351"></a>        <span class="k">return</span>
</span><span id="__span-0-352"><a id="__codelineno-0-352" name="__codelineno-0-352"></a>
</span><span id="__span-0-353"><a id="__codelineno-0-353" name="__codelineno-0-353"></a>    <span class="n">plugins</span> <span class="o">=</span> <span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugins&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="__span-0-354"><a id="__codelineno-0-354" name="__codelineno-0-354"></a>
</span><span id="__span-0-355"><a id="__codelineno-0-355" name="__codelineno-0-355"></a>    <span class="c1"># Check if plugin is already enabled</span>
</span><span id="__span-0-356"><a id="__codelineno-0-356" name="__codelineno-0-356"></a>    <span class="k">for</span> <span class="n">plugin_entry</span> <span class="ow">in</span> <span class="n">plugins</span><span class="p">:</span>
</span><span id="__span-0-357"><a id="__codelineno-0-357" name="__codelineno-0-357"></a>        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plugin_entry</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
</span><span id="__span-0-358"><a id="__codelineno-0-358" name="__codelineno-0-358"></a>            <span class="n">existing_plugin</span> <span class="o">=</span> <span class="n">plugin_entry</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugin&quot;</span><span class="p">)</span>
</span><span id="__span-0-359"><a id="__codelineno-0-359" name="__codelineno-0-359"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-360"><a id="__codelineno-0-360" name="__codelineno-0-360"></a>            <span class="n">existing_plugin</span> <span class="o">=</span> <span class="n">plugin_entry</span>
</span><span id="__span-0-361"><a id="__codelineno-0-361" name="__codelineno-0-361"></a>
</span><span id="__span-0-362"><a id="__codelineno-0-362" name="__codelineno-0-362"></a>        <span class="k">if</span> <span class="n">existing_plugin</span> <span class="o">==</span> <span class="n">plugin_id</span> <span class="ow">or</span> <span class="n">existing_plugin</span> <span class="o">==</span> <span class="n">plugin_identifier</span><span class="p">:</span>
</span><span id="__span-0-363"><a id="__codelineno-0-363" name="__codelineno-0-363"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; is already enabled.&quot;</span><span class="p">)</span>
</span><span id="__span-0-364"><a id="__codelineno-0-364" name="__codelineno-0-364"></a>            <span class="k">return</span>
</span><span id="__span-0-365"><a id="__codelineno-0-365" name="__codelineno-0-365"></a>
</span><span id="__span-0-366"><a id="__codelineno-0-366" name="__codelineno-0-366"></a>    <span class="c1"># Add the plugin</span>
</span><span id="__span-0-367"><a id="__codelineno-0-367" name="__codelineno-0-367"></a>    <span class="n">plugins</span><span class="o">.</span><span class="n">append</span><span class="p">({</span><span class="s2">&quot;plugin&quot;</span><span class="p">:</span> <span class="n">plugin_id</span><span class="p">})</span>
</span><span id="__span-0-368"><a id="__codelineno-0-368" name="__codelineno-0-368"></a>    <span class="n">config</span><span class="p">[</span><span class="s2">&quot;plugins&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plugins</span>
</span><span id="__span-0-369"><a id="__codelineno-0-369" name="__codelineno-0-369"></a>
</span><span id="__span-0-370"><a id="__codelineno-0-370" name="__codelineno-0-370"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-371"><a id="__codelineno-0-371" name="__codelineno-0-371"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-372"><a id="__codelineno-0-372" name="__codelineno-0-372"></a>            <span class="n">yaml</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">default_flow_style</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="__span-0-373"><a id="__codelineno-0-373" name="__codelineno-0-373"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Plugin &#39;</span><span class="si">{</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; enabled successfully.&quot;</span><span class="p">)</span>
</span><span id="__span-0-374"><a id="__codelineno-0-374" name="__codelineno-0-374"></a>        <span class="k">if</span> <span class="n">plugin_name_human</span> <span class="ow">and</span> <span class="n">plugin_name_human</span> <span class="o">!=</span> <span class="n">plugin_identifier</span><span class="p">:</span>
</span><span id="__span-0-375"><a id="__codelineno-0-375" name="__codelineno-0-375"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Human name: </span><span class="si">{</span><span class="n">plugin_name_human</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-376"><a id="__codelineno-0-376" name="__codelineno-0-376"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-377"><a id="__codelineno-0-377" name="__codelineno-0-377"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error writing config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_init_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_init_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_init_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'init' command to create serv.config.yaml.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-140">140</a></span>
<span class="normal"><a href="#__codelineno-0-141">141</a></span>
<span class="normal"><a href="#__codelineno-0-142">142</a></span>
<span class="normal"><a href="#__codelineno-0-143">143</a></span>
<span class="normal"><a href="#__codelineno-0-144">144</a></span>
<span class="normal"><a href="#__codelineno-0-145">145</a></span>
<span class="normal"><a href="#__codelineno-0-146">146</a></span>
<span class="normal"><a href="#__codelineno-0-147">147</a></span>
<span class="normal"><a href="#__codelineno-0-148">148</a></span>
<span class="normal"><a href="#__codelineno-0-149">149</a></span>
<span class="normal"><a href="#__codelineno-0-150">150</a></span>
<span class="normal"><a href="#__codelineno-0-151">151</a></span>
<span class="normal"><a href="#__codelineno-0-152">152</a></span>
<span class="normal"><a href="#__codelineno-0-153">153</a></span>
<span class="normal"><a href="#__codelineno-0-154">154</a></span>
<span class="normal"><a href="#__codelineno-0-155">155</a></span>
<span class="normal"><a href="#__codelineno-0-156">156</a></span>
<span class="normal"><a href="#__codelineno-0-157">157</a></span>
<span class="normal"><a href="#__codelineno-0-158">158</a></span>
<span class="normal"><a href="#__codelineno-0-159">159</a></span>
<span class="normal"><a href="#__codelineno-0-160">160</a></span>
<span class="normal"><a href="#__codelineno-0-161">161</a></span>
<span class="normal"><a href="#__codelineno-0-162">162</a></span>
<span class="normal"><a href="#__codelineno-0-163">163</a></span>
<span class="normal"><a href="#__codelineno-0-164">164</a></span>
<span class="normal"><a href="#__codelineno-0-165">165</a></span>
<span class="normal"><a href="#__codelineno-0-166">166</a></span>
<span class="normal"><a href="#__codelineno-0-167">167</a></span>
<span class="normal"><a href="#__codelineno-0-168">168</a></span>
<span class="normal"><a href="#__codelineno-0-169">169</a></span>
<span class="normal"><a href="#__codelineno-0-170">170</a></span>
<span class="normal"><a href="#__codelineno-0-171">171</a></span>
<span class="normal"><a href="#__codelineno-0-172">172</a></span>
<span class="normal"><a href="#__codelineno-0-173">173</a></span>
<span class="normal"><a href="#__codelineno-0-174">174</a></span>
<span class="normal"><a href="#__codelineno-0-175">175</a></span>
<span class="normal"><a href="#__codelineno-0-176">176</a></span>
<span class="normal"><a href="#__codelineno-0-177">177</a></span>
<span class="normal"><a href="#__codelineno-0-178">178</a></span>
<span class="normal"><a href="#__codelineno-0-179">179</a></span>
<span class="normal"><a href="#__codelineno-0-180">180</a></span>
<span class="normal"><a href="#__codelineno-0-181">181</a></span>
<span class="normal"><a href="#__codelineno-0-182">182</a></span>
<span class="normal"><a href="#__codelineno-0-183">183</a></span>
<span class="normal"><a href="#__codelineno-0-184">184</a></span>
<span class="normal"><a href="#__codelineno-0-185">185</a></span>
<span class="normal"><a href="#__codelineno-0-186">186</a></span>
<span class="normal"><a href="#__codelineno-0-187">187</a></span>
<span class="normal"><a href="#__codelineno-0-188">188</a></span>
<span class="normal"><a href="#__codelineno-0-189">189</a></span>
<span class="normal"><a href="#__codelineno-0-190">190</a></span>
<span class="normal"><a href="#__codelineno-0-191">191</a></span>
<span class="normal"><a href="#__codelineno-0-192">192</a></span>
<span class="normal"><a href="#__codelineno-0-193">193</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-140"><a id="__codelineno-0-140" name="__codelineno-0-140"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_init_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-141"><a id="__codelineno-0-141" name="__codelineno-0-141"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;init&#39; command to create serv.config.yaml.&quot;&quot;&quot;</span>
</span><span id="__span-0-142"><a id="__codelineno-0-142" name="__codelineno-0-142"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Init command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-143"><a id="__codelineno-0-143" name="__codelineno-0-143"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-144"><a id="__codelineno-0-144" name="__codelineno-0-144"></a>
</span><span id="__span-0-145"><a id="__codelineno-0-145" name="__codelineno-0-145"></a>    <span class="k">if</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">force</span><span class="p">:</span>
</span><span id="__span-0-146"><a id="__codelineno-0-146" name="__codelineno-0-146"></a>        <span class="n">overwrite_prompt</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span>
</span><span id="__span-0-147"><a id="__codelineno-0-147" name="__codelineno-0-147"></a>            <span class="sa">f</span><span class="s2">&quot;&#39;</span><span class="si">{</span><span class="n">config_path</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39; already exists in &#39;</span><span class="si">{</span><span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span><span class="si">}</span><span class="s2">&#39;. Overwrite? (yes/no)&quot;</span><span class="p">,</span>
</span><span id="__span-0-148"><a id="__codelineno-0-148" name="__codelineno-0-148"></a>            <span class="s2">&quot;no&quot;</span><span class="p">,</span>
</span><span id="__span-0-149"><a id="__codelineno-0-149" name="__codelineno-0-149"></a>        <span class="p">)</span>
</span><span id="__span-0-150"><a id="__codelineno-0-150" name="__codelineno-0-150"></a>        <span class="k">if</span> <span class="n">overwrite_prompt</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">overwrite_prompt</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s2">&quot;yes&quot;</span><span class="p">:</span>
</span><span id="__span-0-151"><a id="__codelineno-0-151" name="__codelineno-0-151"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Initialization cancelled by user.&quot;</span><span class="p">)</span>
</span><span id="__span-0-152"><a id="__codelineno-0-152" name="__codelineno-0-152"></a>            <span class="k">return</span>
</span><span id="__span-0-153"><a id="__codelineno-0-153" name="__codelineno-0-153"></a>
</span><span id="__span-0-154"><a id="__codelineno-0-154" name="__codelineno-0-154"></a>    <span class="c1"># For non-interactive mode, use default values</span>
</span><span id="__span-0-155"><a id="__codelineno-0-155" name="__codelineno-0-155"></a>    <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">args_ns</span><span class="p">,</span> <span class="s2">&quot;non_interactive&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span> <span class="ow">or</span> <span class="p">(</span>
</span><span id="__span-0-156"><a id="__codelineno-0-156" name="__codelineno-0-156"></a>        <span class="n">args_ns</span><span class="o">.</span><span class="n">force</span> <span class="ow">and</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-157"><a id="__codelineno-0-157" name="__codelineno-0-157"></a>    <span class="p">):</span>
</span><span id="__span-0-158"><a id="__codelineno-0-158" name="__codelineno-0-158"></a>        <span class="n">site_name</span> <span class="o">=</span> <span class="s2">&quot;My Serv Site&quot;</span>
</span><span id="__span-0-159"><a id="__codelineno-0-159" name="__codelineno-0-159"></a>        <span class="n">site_description</span> <span class="o">=</span> <span class="s2">&quot;A new website powered by Serv&quot;</span>
</span><span id="__span-0-160"><a id="__codelineno-0-160" name="__codelineno-0-160"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-161"><a id="__codelineno-0-161" name="__codelineno-0-161"></a>        <span class="n">site_name</span> <span class="o">=</span> <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Enter site name&quot;</span><span class="p">,</span> <span class="s2">&quot;My Serv Site&quot;</span><span class="p">)</span> <span class="ow">or</span> <span class="s2">&quot;My Serv Site&quot;</span>
</span><span id="__span-0-162"><a id="__codelineno-0-162" name="__codelineno-0-162"></a>        <span class="n">site_description</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-163"><a id="__codelineno-0-163" name="__codelineno-0-163"></a>            <span class="n">prompt_user</span><span class="p">(</span><span class="s2">&quot;Enter site description&quot;</span><span class="p">,</span> <span class="s2">&quot;A new website powered by Serv&quot;</span><span class="p">)</span>
</span><span id="__span-0-164"><a id="__codelineno-0-164" name="__codelineno-0-164"></a>            <span class="ow">or</span> <span class="s2">&quot;A new website powered by Serv&quot;</span>
</span><span id="__span-0-165"><a id="__codelineno-0-165" name="__codelineno-0-165"></a>        <span class="p">)</span>
</span><span id="__span-0-166"><a id="__codelineno-0-166" name="__codelineno-0-166"></a>
</span><span id="__span-0-167"><a id="__codelineno-0-167" name="__codelineno-0-167"></a>    <span class="c1"># Load and render the config template</span>
</span><span id="__span-0-168"><a id="__codelineno-0-168" name="__codelineno-0-168"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-169"><a id="__codelineno-0-169" name="__codelineno-0-169"></a>        <span class="n">template_dir</span> <span class="o">=</span> <span class="p">(</span>
</span><span id="__span-0-170"><a id="__codelineno-0-170" name="__codelineno-0-170"></a>            <span class="n">Path</span><span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;serv.cli&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">submodule_search_locations</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</span><span id="__span-0-171"><a id="__codelineno-0-171" name="__codelineno-0-171"></a>            <span class="o">/</span> <span class="s2">&quot;scaffolding&quot;</span>
</span><span id="__span-0-172"><a id="__codelineno-0-172" name="__codelineno-0-172"></a>        <span class="p">)</span>
</span><span id="__span-0-173"><a id="__codelineno-0-173" name="__codelineno-0-173"></a>        <span class="n">env</span> <span class="o">=</span> <span class="n">jinja2</span><span class="o">.</span><span class="n">Environment</span><span class="p">(</span><span class="n">loader</span><span class="o">=</span><span class="n">jinja2</span><span class="o">.</span><span class="n">FileSystemLoader</span><span class="p">(</span><span class="n">template_dir</span><span class="p">))</span>
</span><span id="__span-0-174"><a id="__codelineno-0-174" name="__codelineno-0-174"></a>        <span class="n">template</span> <span class="o">=</span> <span class="n">env</span><span class="o">.</span><span class="n">get_template</span><span class="p">(</span><span class="s2">&quot;config_yaml.template&quot;</span><span class="p">)</span>
</span><span id="__span-0-175"><a id="__codelineno-0-175" name="__codelineno-0-175"></a>
</span><span id="__span-0-176"><a id="__codelineno-0-176" name="__codelineno-0-176"></a>        <span class="n">config_context</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-177"><a id="__codelineno-0-177" name="__codelineno-0-177"></a>            <span class="s2">&quot;site_name&quot;</span><span class="p">:</span> <span class="n">site_name</span><span class="p">,</span>
</span><span id="__span-0-178"><a id="__codelineno-0-178" name="__codelineno-0-178"></a>            <span class="s2">&quot;site_description&quot;</span><span class="p">:</span> <span class="n">site_description</span><span class="p">,</span>
</span><span id="__span-0-179"><a id="__codelineno-0-179" name="__codelineno-0-179"></a>        <span class="p">}</span>
</span><span id="__span-0-180"><a id="__codelineno-0-180" name="__codelineno-0-180"></a>
</span><span id="__span-0-181"><a id="__codelineno-0-181" name="__codelineno-0-181"></a>        <span class="n">config_content_str</span> <span class="o">=</span> <span class="n">template</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="o">**</span><span class="n">config_context</span><span class="p">)</span>
</span><span id="__span-0-182"><a id="__codelineno-0-182" name="__codelineno-0-182"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e_template</span><span class="p">:</span>
</span><span id="__span-0-183"><a id="__codelineno-0-183" name="__codelineno-0-183"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error loading config_yaml.template: </span><span class="si">{</span><span class="n">e_template</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-184"><a id="__codelineno-0-184" name="__codelineno-0-184"></a>        <span class="k">return</span>
</span><span id="__span-0-185"><a id="__codelineno-0-185" name="__codelineno-0-185"></a>
</span><span id="__span-0-186"><a id="__codelineno-0-186" name="__codelineno-0-186"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-187"><a id="__codelineno-0-187" name="__codelineno-0-187"></a>        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-188"><a id="__codelineno-0-188" name="__codelineno-0-188"></a>            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">config_content_str</span><span class="p">)</span>
</span><span id="__span-0-189"><a id="__codelineno-0-189" name="__codelineno-0-189"></a>
</span><span id="__span-0-190"><a id="__codelineno-0-190" name="__codelineno-0-190"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Successfully created &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;.&quot;</span><span class="p">)</span>
</span><span id="__span-0-191"><a id="__codelineno-0-191" name="__codelineno-0-191"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;You can now configure your plugins in this file.&quot;</span><span class="p">)</span>
</span><span id="__span-0-192"><a id="__codelineno-0-192" name="__codelineno-0-192"></a>    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-193"><a id="__codelineno-0-193" name="__codelineno-0-193"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error writing config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_launch_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_launch_command</span>


  <span class="doc doc-labels">
      <small class="doc doc-label doc-label-async"><code>async</code></small>
  </span>

</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_launch_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'launch' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1094">1094</a></span>
<span class="normal"><a href="#__codelineno-0-1095">1095</a></span>
<span class="normal"><a href="#__codelineno-0-1096">1096</a></span>
<span class="normal"><a href="#__codelineno-0-1097">1097</a></span>
<span class="normal"><a href="#__codelineno-0-1098">1098</a></span>
<span class="normal"><a href="#__codelineno-0-1099">1099</a></span>
<span class="normal"><a href="#__codelineno-0-1100">1100</a></span>
<span class="normal"><a href="#__codelineno-0-1101">1101</a></span>
<span class="normal"><a href="#__codelineno-0-1102">1102</a></span>
<span class="normal"><a href="#__codelineno-0-1103">1103</a></span>
<span class="normal"><a href="#__codelineno-0-1104">1104</a></span>
<span class="normal"><a href="#__codelineno-0-1105">1105</a></span>
<span class="normal"><a href="#__codelineno-0-1106">1106</a></span>
<span class="normal"><a href="#__codelineno-0-1107">1107</a></span>
<span class="normal"><a href="#__codelineno-0-1108">1108</a></span>
<span class="normal"><a href="#__codelineno-0-1109">1109</a></span>
<span class="normal"><a href="#__codelineno-0-1110">1110</a></span>
<span class="normal"><a href="#__codelineno-0-1111">1111</a></span>
<span class="normal"><a href="#__codelineno-0-1112">1112</a></span>
<span class="normal"><a href="#__codelineno-0-1113">1113</a></span>
<span class="normal"><a href="#__codelineno-0-1114">1114</a></span>
<span class="normal"><a href="#__codelineno-0-1115">1115</a></span>
<span class="normal"><a href="#__codelineno-0-1116">1116</a></span>
<span class="normal"><a href="#__codelineno-0-1117">1117</a></span>
<span class="normal"><a href="#__codelineno-0-1118">1118</a></span>
<span class="normal"><a href="#__codelineno-0-1119">1119</a></span>
<span class="normal"><a href="#__codelineno-0-1120">1120</a></span>
<span class="normal"><a href="#__codelineno-0-1121">1121</a></span>
<span class="normal"><a href="#__codelineno-0-1122">1122</a></span>
<span class="normal"><a href="#__codelineno-0-1123">1123</a></span>
<span class="normal"><a href="#__codelineno-0-1124">1124</a></span>
<span class="normal"><a href="#__codelineno-0-1125">1125</a></span>
<span class="normal"><a href="#__codelineno-0-1126">1126</a></span>
<span class="normal"><a href="#__codelineno-0-1127">1127</a></span>
<span class="normal"><a href="#__codelineno-0-1128">1128</a></span>
<span class="normal"><a href="#__codelineno-0-1129">1129</a></span>
<span class="normal"><a href="#__codelineno-0-1130">1130</a></span>
<span class="normal"><a href="#__codelineno-0-1131">1131</a></span>
<span class="normal"><a href="#__codelineno-0-1132">1132</a></span>
<span class="normal"><a href="#__codelineno-0-1133">1133</a></span>
<span class="normal"><a href="#__codelineno-0-1134">1134</a></span>
<span class="normal"><a href="#__codelineno-0-1135">1135</a></span>
<span class="normal"><a href="#__codelineno-0-1136">1136</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1094"><a id="__codelineno-0-1094" name="__codelineno-0-1094"></a><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">handle_launch_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1095"><a id="__codelineno-0-1095" name="__codelineno-0-1095"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;launch&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1096"><a id="__codelineno-0-1096" name="__codelineno-0-1096"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Launch command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1097"><a id="__codelineno-0-1097" name="__codelineno-0-1097"></a>
</span><span id="__span-0-1098"><a id="__codelineno-0-1098" name="__codelineno-0-1098"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1099"><a id="__codelineno-0-1099" name="__codelineno-0-1099"></a>        <span class="n">app</span> <span class="o">=</span> <span class="n">_get_configured_app</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">app</span><span class="p">,</span> <span class="n">args_ns</span><span class="p">)</span>
</span><span id="__span-0-1100"><a id="__codelineno-0-1100" name="__codelineno-0-1100"></a>
</span><span id="__span-0-1101"><a id="__codelineno-0-1101" name="__codelineno-0-1101"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">dry_run</span><span class="p">:</span>
</span><span id="__span-0-1102"><a id="__codelineno-0-1102" name="__codelineno-0-1102"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;=== Dry Run Mode ===&quot;</span><span class="p">)</span>
</span><span id="__span-0-1103"><a id="__codelineno-0-1103" name="__codelineno-0-1103"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Application loaded successfully. Server would start with:&quot;</span><span class="p">)</span>
</span><span id="__span-0-1104"><a id="__codelineno-0-1104" name="__codelineno-0-1104"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Host: </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1105"><a id="__codelineno-0-1105" name="__codelineno-0-1105"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Port: </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1106"><a id="__codelineno-0-1106" name="__codelineno-0-1106"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Reload: </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">reload</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1107"><a id="__codelineno-0-1107" name="__codelineno-0-1107"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Workers: </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">workers</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1108"><a id="__codelineno-0-1108" name="__codelineno-0-1108"></a>            <span class="k">return</span>
</span><span id="__span-0-1109"><a id="__codelineno-0-1109" name="__codelineno-0-1109"></a>
</span><span id="__span-0-1110"><a id="__codelineno-0-1110" name="__codelineno-0-1110"></a>        <span class="c1"># Configure uvicorn</span>
</span><span id="__span-0-1111"><a id="__codelineno-0-1111" name="__codelineno-0-1111"></a>        <span class="n">uvicorn_config</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="__span-0-1112"><a id="__codelineno-0-1112" name="__codelineno-0-1112"></a>            <span class="s2">&quot;app&quot;</span><span class="p">:</span> <span class="n">app</span><span class="p">,</span>
</span><span id="__span-0-1113"><a id="__codelineno-0-1113" name="__codelineno-0-1113"></a>            <span class="s2">&quot;host&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">host</span><span class="p">,</span>
</span><span id="__span-0-1114"><a id="__codelineno-0-1114" name="__codelineno-0-1114"></a>            <span class="s2">&quot;port&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">port</span><span class="p">,</span>
</span><span id="__span-0-1115"><a id="__codelineno-0-1115" name="__codelineno-0-1115"></a>            <span class="s2">&quot;reload&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">reload</span><span class="p">,</span>
</span><span id="__span-0-1116"><a id="__codelineno-0-1116" name="__codelineno-0-1116"></a>            <span class="s2">&quot;workers&quot;</span><span class="p">:</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">workers</span>
</span><span id="__span-0-1117"><a id="__codelineno-0-1117" name="__codelineno-0-1117"></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">reload</span>
</span><span id="__span-0-1118"><a id="__codelineno-0-1118" name="__codelineno-0-1118"></a>            <span class="k">else</span> <span class="mi">1</span><span class="p">,</span>  <span class="c1"># Reload doesn&#39;t work with multiple workers</span>
</span><span id="__span-0-1119"><a id="__codelineno-0-1119" name="__codelineno-0-1119"></a>        <span class="p">}</span>
</span><span id="__span-0-1120"><a id="__codelineno-0-1120" name="__codelineno-0-1120"></a>
</span><span id="__span-0-1121"><a id="__codelineno-0-1121" name="__codelineno-0-1121"></a>        <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">factory</span><span class="p">:</span>
</span><span id="__span-0-1122"><a id="__codelineno-0-1122" name="__codelineno-0-1122"></a>            <span class="c1"># If factory mode, we need to pass the app as a string</span>
</span><span id="__span-0-1123"><a id="__codelineno-0-1123" name="__codelineno-0-1123"></a>            <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">app</span><span class="p">:</span>
</span><span id="__span-0-1124"><a id="__codelineno-0-1124" name="__codelineno-0-1124"></a>                <span class="n">uvicorn_config</span><span class="p">[</span><span class="s2">&quot;app&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">app</span>
</span><span id="__span-0-1125"><a id="__codelineno-0-1125" name="__codelineno-0-1125"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1126"><a id="__codelineno-0-1126" name="__codelineno-0-1126"></a>                <span class="n">uvicorn_config</span><span class="p">[</span><span class="s2">&quot;app&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;serv.app:App&quot;</span>
</span><span id="__span-0-1127"><a id="__codelineno-0-1127" name="__codelineno-0-1127"></a>
</span><span id="__span-0-1128"><a id="__codelineno-0-1128" name="__codelineno-0-1128"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Starting Serv application on </span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1129"><a id="__codelineno-0-1129" name="__codelineno-0-1129"></a>
</span><span id="__span-0-1130"><a id="__codelineno-0-1130" name="__codelineno-0-1130"></a>        <span class="c1"># Start the server</span>
</span><span id="__span-0-1131"><a id="__codelineno-0-1131" name="__codelineno-0-1131"></a>        <span class="n">server</span> <span class="o">=</span> <span class="n">uvicorn</span><span class="o">.</span><span class="n">Server</span><span class="p">(</span><span class="n">uvicorn</span><span class="o">.</span><span class="n">Config</span><span class="p">(</span><span class="o">**</span><span class="n">uvicorn_config</span><span class="p">))</span>
</span><span id="__span-0-1132"><a id="__codelineno-0-1132" name="__codelineno-0-1132"></a>        <span class="k">await</span> <span class="n">server</span><span class="o">.</span><span class="n">serve</span><span class="p">()</span>
</span><span id="__span-0-1133"><a id="__codelineno-0-1133" name="__codelineno-0-1133"></a>
</span><span id="__span-0-1134"><a id="__codelineno-0-1134" name="__codelineno-0-1134"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1135"><a id="__codelineno-0-1135" name="__codelineno-0-1135"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error launching application: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1136"><a id="__codelineno-0-1136" name="__codelineno-0-1136"></a>        <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_list_plugin_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_list_plugin_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_list_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'list plugin' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-448">448</a></span>
<span class="normal"><a href="#__codelineno-0-449">449</a></span>
<span class="normal"><a href="#__codelineno-0-450">450</a></span>
<span class="normal"><a href="#__codelineno-0-451">451</a></span>
<span class="normal"><a href="#__codelineno-0-452">452</a></span>
<span class="normal"><a href="#__codelineno-0-453">453</a></span>
<span class="normal"><a href="#__codelineno-0-454">454</a></span>
<span class="normal"><a href="#__codelineno-0-455">455</a></span>
<span class="normal"><a href="#__codelineno-0-456">456</a></span>
<span class="normal"><a href="#__codelineno-0-457">457</a></span>
<span class="normal"><a href="#__codelineno-0-458">458</a></span>
<span class="normal"><a href="#__codelineno-0-459">459</a></span>
<span class="normal"><a href="#__codelineno-0-460">460</a></span>
<span class="normal"><a href="#__codelineno-0-461">461</a></span>
<span class="normal"><a href="#__codelineno-0-462">462</a></span>
<span class="normal"><a href="#__codelineno-0-463">463</a></span>
<span class="normal"><a href="#__codelineno-0-464">464</a></span>
<span class="normal"><a href="#__codelineno-0-465">465</a></span>
<span class="normal"><a href="#__codelineno-0-466">466</a></span>
<span class="normal"><a href="#__codelineno-0-467">467</a></span>
<span class="normal"><a href="#__codelineno-0-468">468</a></span>
<span class="normal"><a href="#__codelineno-0-469">469</a></span>
<span class="normal"><a href="#__codelineno-0-470">470</a></span>
<span class="normal"><a href="#__codelineno-0-471">471</a></span>
<span class="normal"><a href="#__codelineno-0-472">472</a></span>
<span class="normal"><a href="#__codelineno-0-473">473</a></span>
<span class="normal"><a href="#__codelineno-0-474">474</a></span>
<span class="normal"><a href="#__codelineno-0-475">475</a></span>
<span class="normal"><a href="#__codelineno-0-476">476</a></span>
<span class="normal"><a href="#__codelineno-0-477">477</a></span>
<span class="normal"><a href="#__codelineno-0-478">478</a></span>
<span class="normal"><a href="#__codelineno-0-479">479</a></span>
<span class="normal"><a href="#__codelineno-0-480">480</a></span>
<span class="normal"><a href="#__codelineno-0-481">481</a></span>
<span class="normal"><a href="#__codelineno-0-482">482</a></span>
<span class="normal"><a href="#__codelineno-0-483">483</a></span>
<span class="normal"><a href="#__codelineno-0-484">484</a></span>
<span class="normal"><a href="#__codelineno-0-485">485</a></span>
<span class="normal"><a href="#__codelineno-0-486">486</a></span>
<span class="normal"><a href="#__codelineno-0-487">487</a></span>
<span class="normal"><a href="#__codelineno-0-488">488</a></span>
<span class="normal"><a href="#__codelineno-0-489">489</a></span>
<span class="normal"><a href="#__codelineno-0-490">490</a></span>
<span class="normal"><a href="#__codelineno-0-491">491</a></span>
<span class="normal"><a href="#__codelineno-0-492">492</a></span>
<span class="normal"><a href="#__codelineno-0-493">493</a></span>
<span class="normal"><a href="#__codelineno-0-494">494</a></span>
<span class="normal"><a href="#__codelineno-0-495">495</a></span>
<span class="normal"><a href="#__codelineno-0-496">496</a></span>
<span class="normal"><a href="#__codelineno-0-497">497</a></span>
<span class="normal"><a href="#__codelineno-0-498">498</a></span>
<span class="normal"><a href="#__codelineno-0-499">499</a></span>
<span class="normal"><a href="#__codelineno-0-500">500</a></span>
<span class="normal"><a href="#__codelineno-0-501">501</a></span>
<span class="normal"><a href="#__codelineno-0-502">502</a></span>
<span class="normal"><a href="#__codelineno-0-503">503</a></span>
<span class="normal"><a href="#__codelineno-0-504">504</a></span>
<span class="normal"><a href="#__codelineno-0-505">505</a></span>
<span class="normal"><a href="#__codelineno-0-506">506</a></span>
<span class="normal"><a href="#__codelineno-0-507">507</a></span>
<span class="normal"><a href="#__codelineno-0-508">508</a></span>
<span class="normal"><a href="#__codelineno-0-509">509</a></span>
<span class="normal"><a href="#__codelineno-0-510">510</a></span>
<span class="normal"><a href="#__codelineno-0-511">511</a></span>
<span class="normal"><a href="#__codelineno-0-512">512</a></span>
<span class="normal"><a href="#__codelineno-0-513">513</a></span>
<span class="normal"><a href="#__codelineno-0-514">514</a></span>
<span class="normal"><a href="#__codelineno-0-515">515</a></span>
<span class="normal"><a href="#__codelineno-0-516">516</a></span>
<span class="normal"><a href="#__codelineno-0-517">517</a></span>
<span class="normal"><a href="#__codelineno-0-518">518</a></span>
<span class="normal"><a href="#__codelineno-0-519">519</a></span>
<span class="normal"><a href="#__codelineno-0-520">520</a></span>
<span class="normal"><a href="#__codelineno-0-521">521</a></span>
<span class="normal"><a href="#__codelineno-0-522">522</a></span>
<span class="normal"><a href="#__codelineno-0-523">523</a></span>
<span class="normal"><a href="#__codelineno-0-524">524</a></span>
<span class="normal"><a href="#__codelineno-0-525">525</a></span>
<span class="normal"><a href="#__codelineno-0-526">526</a></span>
<span class="normal"><a href="#__codelineno-0-527">527</a></span>
<span class="normal"><a href="#__codelineno-0-528">528</a></span>
<span class="normal"><a href="#__codelineno-0-529">529</a></span>
<span class="normal"><a href="#__codelineno-0-530">530</a></span>
<span class="normal"><a href="#__codelineno-0-531">531</a></span>
<span class="normal"><a href="#__codelineno-0-532">532</a></span>
<span class="normal"><a href="#__codelineno-0-533">533</a></span>
<span class="normal"><a href="#__codelineno-0-534">534</a></span>
<span class="normal"><a href="#__codelineno-0-535">535</a></span>
<span class="normal"><a href="#__codelineno-0-536">536</a></span>
<span class="normal"><a href="#__codelineno-0-537">537</a></span>
<span class="normal"><a href="#__codelineno-0-538">538</a></span>
<span class="normal"><a href="#__codelineno-0-539">539</a></span>
<span class="normal"><a href="#__codelineno-0-540">540</a></span>
<span class="normal"><a href="#__codelineno-0-541">541</a></span>
<span class="normal"><a href="#__codelineno-0-542">542</a></span>
<span class="normal"><a href="#__codelineno-0-543">543</a></span>
<span class="normal"><a href="#__codelineno-0-544">544</a></span>
<span class="normal"><a href="#__codelineno-0-545">545</a></span>
<span class="normal"><a href="#__codelineno-0-546">546</a></span>
<span class="normal"><a href="#__codelineno-0-547">547</a></span>
<span class="normal"><a href="#__codelineno-0-548">548</a></span>
<span class="normal"><a href="#__codelineno-0-549">549</a></span>
<span class="normal"><a href="#__codelineno-0-550">550</a></span>
<span class="normal"><a href="#__codelineno-0-551">551</a></span>
<span class="normal"><a href="#__codelineno-0-552">552</a></span>
<span class="normal"><a href="#__codelineno-0-553">553</a></span>
<span class="normal"><a href="#__codelineno-0-554">554</a></span>
<span class="normal"><a href="#__codelineno-0-555">555</a></span>
<span class="normal"><a href="#__codelineno-0-556">556</a></span>
<span class="normal"><a href="#__codelineno-0-557">557</a></span>
<span class="normal"><a href="#__codelineno-0-558">558</a></span>
<span class="normal"><a href="#__codelineno-0-559">559</a></span>
<span class="normal"><a href="#__codelineno-0-560">560</a></span>
<span class="normal"><a href="#__codelineno-0-561">561</a></span>
<span class="normal"><a href="#__codelineno-0-562">562</a></span>
<span class="normal"><a href="#__codelineno-0-563">563</a></span>
<span class="normal"><a href="#__codelineno-0-564">564</a></span>
<span class="normal"><a href="#__codelineno-0-565">565</a></span>
<span class="normal"><a href="#__codelineno-0-566">566</a></span>
<span class="normal"><a href="#__codelineno-0-567">567</a></span>
<span class="normal"><a href="#__codelineno-0-568">568</a></span>
<span class="normal"><a href="#__codelineno-0-569">569</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-448"><a id="__codelineno-0-448" name="__codelineno-0-448"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_list_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-449"><a id="__codelineno-0-449" name="__codelineno-0-449"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;list plugin&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-450"><a id="__codelineno-0-450" name="__codelineno-0-450"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;List plugin command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-451"><a id="__codelineno-0-451" name="__codelineno-0-451"></a>
</span><span id="__span-0-452"><a id="__codelineno-0-452" name="__codelineno-0-452"></a>    <span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="n">DEFAULT_CONFIG_FILE</span>
</span><span id="__span-0-453"><a id="__codelineno-0-453" name="__codelineno-0-453"></a>
</span><span id="__span-0-454"><a id="__codelineno-0-454" name="__codelineno-0-454"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">available</span><span class="p">:</span>
</span><span id="__span-0-455"><a id="__codelineno-0-455" name="__codelineno-0-455"></a>        <span class="c1"># Show all available plugins in the plugins directory</span>
</span><span id="__span-0-456"><a id="__codelineno-0-456" name="__codelineno-0-456"></a>        <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-457"><a id="__codelineno-0-457" name="__codelineno-0-457"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-458"><a id="__codelineno-0-458" name="__codelineno-0-458"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No plugins directory found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-459"><a id="__codelineno-0-459" name="__codelineno-0-459"></a>            <span class="k">return</span>
</span><span id="__span-0-460"><a id="__codelineno-0-460" name="__codelineno-0-460"></a>
</span><span id="__span-0-461"><a id="__codelineno-0-461" name="__codelineno-0-461"></a>        <span class="n">available_plugins</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-462"><a id="__codelineno-0-462" name="__codelineno-0-462"></a>        <span class="k">for</span> <span class="n">plugin_dir</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">():</span>
</span><span id="__span-0-463"><a id="__codelineno-0-463" name="__codelineno-0-463"></a>            <span class="k">if</span> <span class="p">(</span>
</span><span id="__span-0-464"><a id="__codelineno-0-464" name="__codelineno-0-464"></a>                <span class="n">plugin_dir</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
</span><span id="__span-0-465"><a id="__codelineno-0-465" name="__codelineno-0-465"></a>                <span class="ow">and</span> <span class="ow">not</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">)</span>
</span><span id="__span-0-466"><a id="__codelineno-0-466" name="__codelineno-0-466"></a>                <span class="ow">and</span> <span class="p">(</span><span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-467"><a id="__codelineno-0-467" name="__codelineno-0-467"></a>            <span class="p">):</span>
</span><span id="__span-0-468"><a id="__codelineno-0-468" name="__codelineno-0-468"></a>                <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-469"><a id="__codelineno-0-469" name="__codelineno-0-469"></a>                    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-470"><a id="__codelineno-0-470" name="__codelineno-0-470"></a>                        <span class="n">plugin_meta</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-471"><a id="__codelineno-0-471" name="__codelineno-0-471"></a>
</span><span id="__span-0-472"><a id="__codelineno-0-472" name="__codelineno-0-472"></a>                    <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
</span><span id="__span-0-473"><a id="__codelineno-0-473" name="__codelineno-0-473"></a>                    <span class="n">plugin_version</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;version&quot;</span><span class="p">,</span> <span class="s2">&quot;Unknown&quot;</span><span class="p">)</span>
</span><span id="__span-0-474"><a id="__codelineno-0-474" name="__codelineno-0-474"></a>                    <span class="n">plugin_description</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
</span><span id="__span-0-475"><a id="__codelineno-0-475" name="__codelineno-0-475"></a>                        <span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;No description&quot;</span>
</span><span id="__span-0-476"><a id="__codelineno-0-476" name="__codelineno-0-476"></a>                    <span class="p">)</span>
</span><span id="__span-0-477"><a id="__codelineno-0-477" name="__codelineno-0-477"></a>
</span><span id="__span-0-478"><a id="__codelineno-0-478" name="__codelineno-0-478"></a>                    <span class="n">available_plugins</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
</span><span id="__span-0-479"><a id="__codelineno-0-479" name="__codelineno-0-479"></a>                        <span class="p">{</span>
</span><span id="__span-0-480"><a id="__codelineno-0-480" name="__codelineno-0-480"></a>                            <span class="s2">&quot;dir_name&quot;</span><span class="p">:</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
</span><span id="__span-0-481"><a id="__codelineno-0-481" name="__codelineno-0-481"></a>                            <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">plugin_name</span><span class="p">,</span>
</span><span id="__span-0-482"><a id="__codelineno-0-482" name="__codelineno-0-482"></a>                            <span class="s2">&quot;version&quot;</span><span class="p">:</span> <span class="n">plugin_version</span><span class="p">,</span>
</span><span id="__span-0-483"><a id="__codelineno-0-483" name="__codelineno-0-483"></a>                            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">plugin_description</span><span class="p">,</span>
</span><span id="__span-0-484"><a id="__codelineno-0-484" name="__codelineno-0-484"></a>                        <span class="p">}</span>
</span><span id="__span-0-485"><a id="__codelineno-0-485" name="__codelineno-0-485"></a>                    <span class="p">)</span>
</span><span id="__span-0-486"><a id="__codelineno-0-486" name="__codelineno-0-486"></a>                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-487"><a id="__codelineno-0-487" name="__codelineno-0-487"></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span>
</span><span id="__span-0-488"><a id="__codelineno-0-488" name="__codelineno-0-488"></a>                        <span class="sa">f</span><span class="s2">&quot;Error reading plugin metadata for &#39;</span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="__span-0-489"><a id="__codelineno-0-489" name="__codelineno-0-489"></a>                    <span class="p">)</span>
</span><span id="__span-0-490"><a id="__codelineno-0-490" name="__codelineno-0-490"></a>                    <span class="n">available_plugins</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
</span><span id="__span-0-491"><a id="__codelineno-0-491" name="__codelineno-0-491"></a>                        <span class="p">{</span>
</span><span id="__span-0-492"><a id="__codelineno-0-492" name="__codelineno-0-492"></a>                            <span class="s2">&quot;dir_name&quot;</span><span class="p">:</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
</span><span id="__span-0-493"><a id="__codelineno-0-493" name="__codelineno-0-493"></a>                            <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
</span><span id="__span-0-494"><a id="__codelineno-0-494" name="__codelineno-0-494"></a>                            <span class="s2">&quot;version&quot;</span><span class="p">:</span> <span class="s2">&quot;Unknown&quot;</span><span class="p">,</span>
</span><span id="__span-0-495"><a id="__codelineno-0-495" name="__codelineno-0-495"></a>                            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Error reading metadata&quot;</span><span class="p">,</span>
</span><span id="__span-0-496"><a id="__codelineno-0-496" name="__codelineno-0-496"></a>                        <span class="p">}</span>
</span><span id="__span-0-497"><a id="__codelineno-0-497" name="__codelineno-0-497"></a>                    <span class="p">)</span>
</span><span id="__span-0-498"><a id="__codelineno-0-498" name="__codelineno-0-498"></a>
</span><span id="__span-0-499"><a id="__codelineno-0-499" name="__codelineno-0-499"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-500"><a id="__codelineno-0-500" name="__codelineno-0-500"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No plugins found in the plugins directory.&quot;</span><span class="p">)</span>
</span><span id="__span-0-501"><a id="__codelineno-0-501" name="__codelineno-0-501"></a>            <span class="k">return</span>
</span><span id="__span-0-502"><a id="__codelineno-0-502" name="__codelineno-0-502"></a>
</span><span id="__span-0-503"><a id="__codelineno-0-503" name="__codelineno-0-503"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Available plugins (</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">available_plugins</span><span class="p">)</span><span class="si">}</span><span class="s2">):&quot;</span><span class="p">)</span>
</span><span id="__span-0-504"><a id="__codelineno-0-504" name="__codelineno-0-504"></a>        <span class="k">for</span> <span class="n">plugin</span> <span class="ow">in</span> <span class="n">available_plugins</span><span class="p">:</span>
</span><span id="__span-0-505"><a id="__codelineno-0-505" name="__codelineno-0-505"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  • </span><span class="si">{</span><span class="n">plugin</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2"> (v</span><span class="si">{</span><span class="n">plugin</span><span class="p">[</span><span class="s1">&#39;version&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">) [</span><span class="si">{</span><span class="n">plugin</span><span class="p">[</span><span class="s1">&#39;dir_name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">]&quot;</span><span class="p">)</span>
</span><span id="__span-0-506"><a id="__codelineno-0-506" name="__codelineno-0-506"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;    </span><span class="si">{</span><span class="n">plugin</span><span class="p">[</span><span class="s1">&#39;description&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-507"><a id="__codelineno-0-507" name="__codelineno-0-507"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-508"><a id="__codelineno-0-508" name="__codelineno-0-508"></a>        <span class="c1"># Show enabled plugins from config</span>
</span><span id="__span-0-509"><a id="__codelineno-0-509" name="__codelineno-0-509"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-510"><a id="__codelineno-0-510" name="__codelineno-0-510"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Configuration file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39; not found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-511"><a id="__codelineno-0-511" name="__codelineno-0-511"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Run &#39;serv app init&#39; to create a configuration file.&quot;</span><span class="p">)</span>
</span><span id="__span-0-512"><a id="__codelineno-0-512" name="__codelineno-0-512"></a>            <span class="k">return</span>
</span><span id="__span-0-513"><a id="__codelineno-0-513" name="__codelineno-0-513"></a>
</span><span id="__span-0-514"><a id="__codelineno-0-514" name="__codelineno-0-514"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-515"><a id="__codelineno-0-515" name="__codelineno-0-515"></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-516"><a id="__codelineno-0-516" name="__codelineno-0-516"></a>                <span class="n">config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-517"><a id="__codelineno-0-517" name="__codelineno-0-517"></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-518"><a id="__codelineno-0-518" name="__codelineno-0-518"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error reading config file &#39;</span><span class="si">{</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&#39;: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-519"><a id="__codelineno-0-519" name="__codelineno-0-519"></a>            <span class="k">return</span>
</span><span id="__span-0-520"><a id="__codelineno-0-520" name="__codelineno-0-520"></a>
</span><span id="__span-0-521"><a id="__codelineno-0-521" name="__codelineno-0-521"></a>        <span class="n">plugins</span> <span class="o">=</span> <span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugins&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="__span-0-522"><a id="__codelineno-0-522" name="__codelineno-0-522"></a>
</span><span id="__span-0-523"><a id="__codelineno-0-523" name="__codelineno-0-523"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">plugins</span><span class="p">:</span>
</span><span id="__span-0-524"><a id="__codelineno-0-524" name="__codelineno-0-524"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No plugins are currently enabled.&quot;</span><span class="p">)</span>
</span><span id="__span-0-525"><a id="__codelineno-0-525" name="__codelineno-0-525"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Use &#39;serv plugin enable &lt;plugin&gt;&#39; to enable a plugin.&quot;</span><span class="p">)</span>
</span><span id="__span-0-526"><a id="__codelineno-0-526" name="__codelineno-0-526"></a>            <span class="k">return</span>
</span><span id="__span-0-527"><a id="__codelineno-0-527" name="__codelineno-0-527"></a>
</span><span id="__span-0-528"><a id="__codelineno-0-528" name="__codelineno-0-528"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Enabled plugins (</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">plugins</span><span class="p">)</span><span class="si">}</span><span class="s2">):&quot;</span><span class="p">)</span>
</span><span id="__span-0-529"><a id="__codelineno-0-529" name="__codelineno-0-529"></a>        <span class="k">for</span> <span class="n">plugin_entry</span> <span class="ow">in</span> <span class="n">plugins</span><span class="p">:</span>
</span><span id="__span-0-530"><a id="__codelineno-0-530" name="__codelineno-0-530"></a>            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plugin_entry</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
</span><span id="__span-0-531"><a id="__codelineno-0-531" name="__codelineno-0-531"></a>                <span class="n">plugin_id</span> <span class="o">=</span> <span class="n">plugin_entry</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugin&quot;</span><span class="p">,</span> <span class="s2">&quot;Unknown&quot;</span><span class="p">)</span>
</span><span id="__span-0-532"><a id="__codelineno-0-532" name="__codelineno-0-532"></a>                <span class="n">plugin_config</span> <span class="o">=</span> <span class="n">plugin_entry</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;config&quot;</span><span class="p">,</span> <span class="p">{})</span>
</span><span id="__span-0-533"><a id="__codelineno-0-533" name="__codelineno-0-533"></a>                <span class="n">config_info</span> <span class="o">=</span> <span class="s2">&quot; (with config)&quot;</span> <span class="k">if</span> <span class="n">plugin_config</span> <span class="k">else</span> <span class="s2">&quot;&quot;</span>
</span><span id="__span-0-534"><a id="__codelineno-0-534" name="__codelineno-0-534"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-535"><a id="__codelineno-0-535" name="__codelineno-0-535"></a>                <span class="n">plugin_id</span> <span class="o">=</span> <span class="n">plugin_entry</span>
</span><span id="__span-0-536"><a id="__codelineno-0-536" name="__codelineno-0-536"></a>                <span class="n">config_info</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>
</span><span id="__span-0-537"><a id="__codelineno-0-537" name="__codelineno-0-537"></a>
</span><span id="__span-0-538"><a id="__codelineno-0-538" name="__codelineno-0-538"></a>            <span class="c1"># Try to get human-readable name from plugin metadata</span>
</span><span id="__span-0-539"><a id="__codelineno-0-539" name="__codelineno-0-539"></a>            <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_id</span>
</span><span id="__span-0-540"><a id="__codelineno-0-540" name="__codelineno-0-540"></a>            <span class="n">plugin_version</span> <span class="o">=</span> <span class="s2">&quot;Unknown&quot;</span>
</span><span id="__span-0-541"><a id="__codelineno-0-541" name="__codelineno-0-541"></a>
</span><span id="__span-0-542"><a id="__codelineno-0-542" name="__codelineno-0-542"></a>            <span class="c1"># Check if this is a directory-based plugin</span>
</span><span id="__span-0-543"><a id="__codelineno-0-543" name="__codelineno-0-543"></a>            <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-544"><a id="__codelineno-0-544" name="__codelineno-0-544"></a>            <span class="k">if</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-545"><a id="__codelineno-0-545" name="__codelineno-0-545"></a>                <span class="c1"># Extract directory name from plugin_id (handle both simple names and module paths)</span>
</span><span id="__span-0-546"><a id="__codelineno-0-546" name="__codelineno-0-546"></a>                <span class="k">if</span> <span class="s2">&quot;:&quot;</span> <span class="ow">in</span> <span class="n">plugin_id</span><span class="p">:</span>
</span><span id="__span-0-547"><a id="__codelineno-0-547" name="__codelineno-0-547"></a>                    <span class="c1"># Full module path like &quot;test_plugin.test_plugin:TestPlugin&quot;</span>
</span><span id="__span-0-548"><a id="__codelineno-0-548" name="__codelineno-0-548"></a>                    <span class="n">module_path</span> <span class="o">=</span> <span class="n">plugin_id</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;:&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="__span-0-549"><a id="__codelineno-0-549" name="__codelineno-0-549"></a>                    <span class="n">dir_name</span> <span class="o">=</span> <span class="n">module_path</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;.&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="__span-0-550"><a id="__codelineno-0-550" name="__codelineno-0-550"></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-551"><a id="__codelineno-0-551" name="__codelineno-0-551"></a>                    <span class="c1"># Simple name or just module path</span>
</span><span id="__span-0-552"><a id="__codelineno-0-552" name="__codelineno-0-552"></a>                    <span class="n">dir_name</span> <span class="o">=</span> <span class="n">plugin_id</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;.&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="__span-0-553"><a id="__codelineno-0-553" name="__codelineno-0-553"></a>
</span><span id="__span-0-554"><a id="__codelineno-0-554" name="__codelineno-0-554"></a>                <span class="c1"># Try to find the plugin directory</span>
</span><span id="__span-0-555"><a id="__codelineno-0-555" name="__codelineno-0-555"></a>                <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">dir_name</span>
</span><span id="__span-0-556"><a id="__codelineno-0-556" name="__codelineno-0-556"></a>                <span class="k">if</span> <span class="p">(</span>
</span><span id="__span-0-557"><a id="__codelineno-0-557" name="__codelineno-0-557"></a>                    <span class="n">plugin_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-558"><a id="__codelineno-0-558" name="__codelineno-0-558"></a>                    <span class="ow">and</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
</span><span id="__span-0-559"><a id="__codelineno-0-559" name="__codelineno-0-559"></a>                    <span class="ow">and</span> <span class="p">(</span><span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
</span><span id="__span-0-560"><a id="__codelineno-0-560" name="__codelineno-0-560"></a>                <span class="p">):</span>
</span><span id="__span-0-561"><a id="__codelineno-0-561" name="__codelineno-0-561"></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-562"><a id="__codelineno-0-562" name="__codelineno-0-562"></a>                        <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-563"><a id="__codelineno-0-563" name="__codelineno-0-563"></a>                            <span class="n">plugin_meta</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="ow">or</span> <span class="p">{}</span>
</span><span id="__span-0-564"><a id="__codelineno-0-564" name="__codelineno-0-564"></a>                        <span class="n">plugin_name</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="n">plugin_id</span><span class="p">)</span>
</span><span id="__span-0-565"><a id="__codelineno-0-565" name="__codelineno-0-565"></a>                        <span class="n">plugin_version</span> <span class="o">=</span> <span class="n">plugin_meta</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;version&quot;</span><span class="p">,</span> <span class="s2">&quot;Unknown&quot;</span><span class="p">)</span>
</span><span id="__span-0-566"><a id="__codelineno-0-566" name="__codelineno-0-566"></a>                    <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
</span><span id="__span-0-567"><a id="__codelineno-0-567" name="__codelineno-0-567"></a>                        <span class="k">pass</span>
</span><span id="__span-0-568"><a id="__codelineno-0-568" name="__codelineno-0-568"></a>
</span><span id="__span-0-569"><a id="__codelineno-0-569" name="__codelineno-0-569"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  • </span><span class="si">{</span><span class="n">plugin_name</span><span class="si">}</span><span class="s2"> (v</span><span class="si">{</span><span class="n">plugin_version</span><span class="si">}</span><span class="s2">) [</span><span class="si">{</span><span class="n">plugin_id</span><span class="si">}</span><span class="s2">]</span><span class="si">{</span><span class="n">config_info</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_shell_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_shell_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_shell_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'shell' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1283">1283</a></span>
<span class="normal"><a href="#__codelineno-0-1284">1284</a></span>
<span class="normal"><a href="#__codelineno-0-1285">1285</a></span>
<span class="normal"><a href="#__codelineno-0-1286">1286</a></span>
<span class="normal"><a href="#__codelineno-0-1287">1287</a></span>
<span class="normal"><a href="#__codelineno-0-1288">1288</a></span>
<span class="normal"><a href="#__codelineno-0-1289">1289</a></span>
<span class="normal"><a href="#__codelineno-0-1290">1290</a></span>
<span class="normal"><a href="#__codelineno-0-1291">1291</a></span>
<span class="normal"><a href="#__codelineno-0-1292">1292</a></span>
<span class="normal"><a href="#__codelineno-0-1293">1293</a></span>
<span class="normal"><a href="#__codelineno-0-1294">1294</a></span>
<span class="normal"><a href="#__codelineno-0-1295">1295</a></span>
<span class="normal"><a href="#__codelineno-0-1296">1296</a></span>
<span class="normal"><a href="#__codelineno-0-1297">1297</a></span>
<span class="normal"><a href="#__codelineno-0-1298">1298</a></span>
<span class="normal"><a href="#__codelineno-0-1299">1299</a></span>
<span class="normal"><a href="#__codelineno-0-1300">1300</a></span>
<span class="normal"><a href="#__codelineno-0-1301">1301</a></span>
<span class="normal"><a href="#__codelineno-0-1302">1302</a></span>
<span class="normal"><a href="#__codelineno-0-1303">1303</a></span>
<span class="normal"><a href="#__codelineno-0-1304">1304</a></span>
<span class="normal"><a href="#__codelineno-0-1305">1305</a></span>
<span class="normal"><a href="#__codelineno-0-1306">1306</a></span>
<span class="normal"><a href="#__codelineno-0-1307">1307</a></span>
<span class="normal"><a href="#__codelineno-0-1308">1308</a></span>
<span class="normal"><a href="#__codelineno-0-1309">1309</a></span>
<span class="normal"><a href="#__codelineno-0-1310">1310</a></span>
<span class="normal"><a href="#__codelineno-0-1311">1311</a></span>
<span class="normal"><a href="#__codelineno-0-1312">1312</a></span>
<span class="normal"><a href="#__codelineno-0-1313">1313</a></span>
<span class="normal"><a href="#__codelineno-0-1314">1314</a></span>
<span class="normal"><a href="#__codelineno-0-1315">1315</a></span>
<span class="normal"><a href="#__codelineno-0-1316">1316</a></span>
<span class="normal"><a href="#__codelineno-0-1317">1317</a></span>
<span class="normal"><a href="#__codelineno-0-1318">1318</a></span>
<span class="normal"><a href="#__codelineno-0-1319">1319</a></span>
<span class="normal"><a href="#__codelineno-0-1320">1320</a></span>
<span class="normal"><a href="#__codelineno-0-1321">1321</a></span>
<span class="normal"><a href="#__codelineno-0-1322">1322</a></span>
<span class="normal"><a href="#__codelineno-0-1323">1323</a></span>
<span class="normal"><a href="#__codelineno-0-1324">1324</a></span>
<span class="normal"><a href="#__codelineno-0-1325">1325</a></span>
<span class="normal"><a href="#__codelineno-0-1326">1326</a></span>
<span class="normal"><a href="#__codelineno-0-1327">1327</a></span>
<span class="normal"><a href="#__codelineno-0-1328">1328</a></span>
<span class="normal"><a href="#__codelineno-0-1329">1329</a></span>
<span class="normal"><a href="#__codelineno-0-1330">1330</a></span>
<span class="normal"><a href="#__codelineno-0-1331">1331</a></span>
<span class="normal"><a href="#__codelineno-0-1332">1332</a></span>
<span class="normal"><a href="#__codelineno-0-1333">1333</a></span>
<span class="normal"><a href="#__codelineno-0-1334">1334</a></span>
<span class="normal"><a href="#__codelineno-0-1335">1335</a></span>
<span class="normal"><a href="#__codelineno-0-1336">1336</a></span>
<span class="normal"><a href="#__codelineno-0-1337">1337</a></span>
<span class="normal"><a href="#__codelineno-0-1338">1338</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1283"><a id="__codelineno-0-1283" name="__codelineno-0-1283"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_shell_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1284"><a id="__codelineno-0-1284" name="__codelineno-0-1284"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;shell&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1285"><a id="__codelineno-0-1285" name="__codelineno-0-1285"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Shell command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1286"><a id="__codelineno-0-1286" name="__codelineno-0-1286"></a>
</span><span id="__span-0-1287"><a id="__codelineno-0-1287" name="__codelineno-0-1287"></a>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🐍 Starting interactive Python shell...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1288"><a id="__codelineno-0-1288" name="__codelineno-0-1288"></a>
</span><span id="__span-0-1289"><a id="__codelineno-0-1289" name="__codelineno-0-1289"></a>    <span class="c1"># Prepare the shell environment</span>
</span><span id="__span-0-1290"><a id="__codelineno-0-1290" name="__codelineno-0-1290"></a>    <span class="n">shell_locals</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;__name__&quot;</span><span class="p">:</span> <span class="s2">&quot;__console__&quot;</span><span class="p">,</span> <span class="s2">&quot;__doc__&quot;</span><span class="p">:</span> <span class="kc">None</span><span class="p">}</span>
</span><span id="__span-0-1291"><a id="__codelineno-0-1291" name="__codelineno-0-1291"></a>
</span><span id="__span-0-1292"><a id="__codelineno-0-1292" name="__codelineno-0-1292"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">no_startup</span><span class="p">:</span>
</span><span id="__span-0-1293"><a id="__codelineno-0-1293" name="__codelineno-0-1293"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1294"><a id="__codelineno-0-1294" name="__codelineno-0-1294"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;📦 Loading Serv app context...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1295"><a id="__codelineno-0-1295" name="__codelineno-0-1295"></a>            <span class="n">app</span> <span class="o">=</span> <span class="n">_get_configured_app</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">app</span><span class="p">,</span> <span class="n">args_ns</span><span class="p">)</span>
</span><span id="__span-0-1296"><a id="__codelineno-0-1296" name="__codelineno-0-1296"></a>            <span class="n">shell_locals</span><span class="o">.</span><span class="n">update</span><span class="p">(</span>
</span><span id="__span-0-1297"><a id="__codelineno-0-1297" name="__codelineno-0-1297"></a>                <span class="p">{</span>
</span><span id="__span-0-1298"><a id="__codelineno-0-1298" name="__codelineno-0-1298"></a>                    <span class="s2">&quot;app&quot;</span><span class="p">:</span> <span class="n">app</span><span class="p">,</span>
</span><span id="__span-0-1299"><a id="__codelineno-0-1299" name="__codelineno-0-1299"></a>                    <span class="s2">&quot;serv&quot;</span><span class="p">:</span> <span class="n">importlib</span><span class="o">.</span><span class="n">import_module</span><span class="p">(</span><span class="s2">&quot;serv&quot;</span><span class="p">),</span>
</span><span id="__span-0-1300"><a id="__codelineno-0-1300" name="__codelineno-0-1300"></a>                    <span class="s2">&quot;Path&quot;</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span>
</span><span id="__span-0-1301"><a id="__codelineno-0-1301" name="__codelineno-0-1301"></a>                    <span class="s2">&quot;yaml&quot;</span><span class="p">:</span> <span class="n">yaml</span><span class="p">,</span>
</span><span id="__span-0-1302"><a id="__codelineno-0-1302" name="__codelineno-0-1302"></a>                <span class="p">}</span>
</span><span id="__span-0-1303"><a id="__codelineno-0-1303" name="__codelineno-0-1303"></a>            <span class="p">)</span>
</span><span id="__span-0-1304"><a id="__codelineno-0-1304" name="__codelineno-0-1304"></a>
</span><span id="__span-0-1305"><a id="__codelineno-0-1305" name="__codelineno-0-1305"></a>            <span class="c1"># Add plugins to shell context</span>
</span><span id="__span-0-1306"><a id="__codelineno-0-1306" name="__codelineno-0-1306"></a>            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">app</span><span class="p">,</span> <span class="s2">&quot;_plugins&quot;</span><span class="p">):</span>
</span><span id="__span-0-1307"><a id="__codelineno-0-1307" name="__codelineno-0-1307"></a>                <span class="n">all_plugins</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-1308"><a id="__codelineno-0-1308" name="__codelineno-0-1308"></a>                <span class="k">for</span> <span class="n">plugin_list</span> <span class="ow">in</span> <span class="n">app</span><span class="o">.</span><span class="n">_plugins</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
</span><span id="__span-0-1309"><a id="__codelineno-0-1309" name="__codelineno-0-1309"></a>                    <span class="n">all_plugins</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">plugin_list</span><span class="p">)</span>
</span><span id="__span-0-1310"><a id="__codelineno-0-1310" name="__codelineno-0-1310"></a>                <span class="n">shell_locals</span><span class="p">[</span><span class="s2">&quot;plugins&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">all_plugins</span>
</span><span id="__span-0-1311"><a id="__codelineno-0-1311" name="__codelineno-0-1311"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;🔌 Loaded </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">all_plugins</span><span class="p">)</span><span class="si">}</span><span class="s2"> plugins into context&quot;</span><span class="p">)</span>
</span><span id="__span-0-1312"><a id="__codelineno-0-1312" name="__codelineno-0-1312"></a>
</span><span id="__span-0-1313"><a id="__codelineno-0-1313" name="__codelineno-0-1313"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;✅ App context loaded successfully&quot;</span><span class="p">)</span>
</span><span id="__span-0-1314"><a id="__codelineno-0-1314" name="__codelineno-0-1314"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Available objects: app, serv, plugins, Path, yaml&quot;</span><span class="p">)</span>
</span><span id="__span-0-1315"><a id="__codelineno-0-1315" name="__codelineno-0-1315"></a>
</span><span id="__span-0-1316"><a id="__codelineno-0-1316" name="__codelineno-0-1316"></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1317"><a id="__codelineno-0-1317" name="__codelineno-0-1317"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Could not load app context: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1318"><a id="__codelineno-0-1318" name="__codelineno-0-1318"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  App context not available, starting basic shell&quot;</span><span class="p">)</span>
</span><span id="__span-0-1319"><a id="__codelineno-0-1319" name="__codelineno-0-1319"></a>
</span><span id="__span-0-1320"><a id="__codelineno-0-1320" name="__codelineno-0-1320"></a>    <span class="c1"># Try to use IPython if available and requested</span>
</span><span id="__span-0-1321"><a id="__codelineno-0-1321" name="__codelineno-0-1321"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">ipython</span><span class="p">:</span>
</span><span id="__span-0-1322"><a id="__codelineno-0-1322" name="__codelineno-0-1322"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1323"><a id="__codelineno-0-1323" name="__codelineno-0-1323"></a>            <span class="kn">from</span><span class="w"> </span><span class="nn">IPython</span><span class="w"> </span><span class="kn">import</span> <span class="n">start_ipython</span>
</span><span id="__span-0-1324"><a id="__codelineno-0-1324" name="__codelineno-0-1324"></a>
</span><span id="__span-0-1325"><a id="__codelineno-0-1325" name="__codelineno-0-1325"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🎨 Starting IPython shell...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1326"><a id="__codelineno-0-1326" name="__codelineno-0-1326"></a>            <span class="n">start_ipython</span><span class="p">(</span><span class="n">argv</span><span class="o">=</span><span class="p">[],</span> <span class="n">user_ns</span><span class="o">=</span><span class="n">shell_locals</span><span class="p">)</span>
</span><span id="__span-0-1327"><a id="__codelineno-0-1327" name="__codelineno-0-1327"></a>            <span class="k">return</span>
</span><span id="__span-0-1328"><a id="__codelineno-0-1328" name="__codelineno-0-1328"></a>        <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
</span><span id="__span-0-1329"><a id="__codelineno-0-1329" name="__codelineno-0-1329"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  IPython not available, falling back to standard shell&quot;</span><span class="p">)</span>
</span><span id="__span-0-1330"><a id="__codelineno-0-1330" name="__codelineno-0-1330"></a>
</span><span id="__span-0-1331"><a id="__codelineno-0-1331" name="__codelineno-0-1331"></a>    <span class="c1"># Use standard Python shell</span>
</span><span id="__span-0-1332"><a id="__codelineno-0-1332" name="__codelineno-0-1332"></a>    <span class="kn">import</span><span class="w"> </span><span class="nn">code</span>
</span><span id="__span-0-1333"><a id="__codelineno-0-1333" name="__codelineno-0-1333"></a>
</span><span id="__span-0-1334"><a id="__codelineno-0-1334" name="__codelineno-0-1334"></a>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🐍 Starting Python shell...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1335"><a id="__codelineno-0-1335" name="__codelineno-0-1335"></a>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Type &#39;exit()&#39; or Ctrl+D to exit&quot;</span><span class="p">)</span>
</span><span id="__span-0-1336"><a id="__codelineno-0-1336" name="__codelineno-0-1336"></a>
</span><span id="__span-0-1337"><a id="__codelineno-0-1337" name="__codelineno-0-1337"></a>    <span class="n">shell</span> <span class="o">=</span> <span class="n">code</span><span class="o">.</span><span class="n">InteractiveConsole</span><span class="p">(</span><span class="nb">locals</span><span class="o">=</span><span class="n">shell_locals</span><span class="p">)</span>
</span><span id="__span-0-1338"><a id="__codelineno-0-1338" name="__codelineno-0-1338"></a>    <span class="n">shell</span><span class="o">.</span><span class="n">interact</span><span class="p">(</span><span class="n">banner</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">)</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_test_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_test_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_test_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'test' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-1185">1185</a></span>
<span class="normal"><a href="#__codelineno-0-1186">1186</a></span>
<span class="normal"><a href="#__codelineno-0-1187">1187</a></span>
<span class="normal"><a href="#__codelineno-0-1188">1188</a></span>
<span class="normal"><a href="#__codelineno-0-1189">1189</a></span>
<span class="normal"><a href="#__codelineno-0-1190">1190</a></span>
<span class="normal"><a href="#__codelineno-0-1191">1191</a></span>
<span class="normal"><a href="#__codelineno-0-1192">1192</a></span>
<span class="normal"><a href="#__codelineno-0-1193">1193</a></span>
<span class="normal"><a href="#__codelineno-0-1194">1194</a></span>
<span class="normal"><a href="#__codelineno-0-1195">1195</a></span>
<span class="normal"><a href="#__codelineno-0-1196">1196</a></span>
<span class="normal"><a href="#__codelineno-0-1197">1197</a></span>
<span class="normal"><a href="#__codelineno-0-1198">1198</a></span>
<span class="normal"><a href="#__codelineno-0-1199">1199</a></span>
<span class="normal"><a href="#__codelineno-0-1200">1200</a></span>
<span class="normal"><a href="#__codelineno-0-1201">1201</a></span>
<span class="normal"><a href="#__codelineno-0-1202">1202</a></span>
<span class="normal"><a href="#__codelineno-0-1203">1203</a></span>
<span class="normal"><a href="#__codelineno-0-1204">1204</a></span>
<span class="normal"><a href="#__codelineno-0-1205">1205</a></span>
<span class="normal"><a href="#__codelineno-0-1206">1206</a></span>
<span class="normal"><a href="#__codelineno-0-1207">1207</a></span>
<span class="normal"><a href="#__codelineno-0-1208">1208</a></span>
<span class="normal"><a href="#__codelineno-0-1209">1209</a></span>
<span class="normal"><a href="#__codelineno-0-1210">1210</a></span>
<span class="normal"><a href="#__codelineno-0-1211">1211</a></span>
<span class="normal"><a href="#__codelineno-0-1212">1212</a></span>
<span class="normal"><a href="#__codelineno-0-1213">1213</a></span>
<span class="normal"><a href="#__codelineno-0-1214">1214</a></span>
<span class="normal"><a href="#__codelineno-0-1215">1215</a></span>
<span class="normal"><a href="#__codelineno-0-1216">1216</a></span>
<span class="normal"><a href="#__codelineno-0-1217">1217</a></span>
<span class="normal"><a href="#__codelineno-0-1218">1218</a></span>
<span class="normal"><a href="#__codelineno-0-1219">1219</a></span>
<span class="normal"><a href="#__codelineno-0-1220">1220</a></span>
<span class="normal"><a href="#__codelineno-0-1221">1221</a></span>
<span class="normal"><a href="#__codelineno-0-1222">1222</a></span>
<span class="normal"><a href="#__codelineno-0-1223">1223</a></span>
<span class="normal"><a href="#__codelineno-0-1224">1224</a></span>
<span class="normal"><a href="#__codelineno-0-1225">1225</a></span>
<span class="normal"><a href="#__codelineno-0-1226">1226</a></span>
<span class="normal"><a href="#__codelineno-0-1227">1227</a></span>
<span class="normal"><a href="#__codelineno-0-1228">1228</a></span>
<span class="normal"><a href="#__codelineno-0-1229">1229</a></span>
<span class="normal"><a href="#__codelineno-0-1230">1230</a></span>
<span class="normal"><a href="#__codelineno-0-1231">1231</a></span>
<span class="normal"><a href="#__codelineno-0-1232">1232</a></span>
<span class="normal"><a href="#__codelineno-0-1233">1233</a></span>
<span class="normal"><a href="#__codelineno-0-1234">1234</a></span>
<span class="normal"><a href="#__codelineno-0-1235">1235</a></span>
<span class="normal"><a href="#__codelineno-0-1236">1236</a></span>
<span class="normal"><a href="#__codelineno-0-1237">1237</a></span>
<span class="normal"><a href="#__codelineno-0-1238">1238</a></span>
<span class="normal"><a href="#__codelineno-0-1239">1239</a></span>
<span class="normal"><a href="#__codelineno-0-1240">1240</a></span>
<span class="normal"><a href="#__codelineno-0-1241">1241</a></span>
<span class="normal"><a href="#__codelineno-0-1242">1242</a></span>
<span class="normal"><a href="#__codelineno-0-1243">1243</a></span>
<span class="normal"><a href="#__codelineno-0-1244">1244</a></span>
<span class="normal"><a href="#__codelineno-0-1245">1245</a></span>
<span class="normal"><a href="#__codelineno-0-1246">1246</a></span>
<span class="normal"><a href="#__codelineno-0-1247">1247</a></span>
<span class="normal"><a href="#__codelineno-0-1248">1248</a></span>
<span class="normal"><a href="#__codelineno-0-1249">1249</a></span>
<span class="normal"><a href="#__codelineno-0-1250">1250</a></span>
<span class="normal"><a href="#__codelineno-0-1251">1251</a></span>
<span class="normal"><a href="#__codelineno-0-1252">1252</a></span>
<span class="normal"><a href="#__codelineno-0-1253">1253</a></span>
<span class="normal"><a href="#__codelineno-0-1254">1254</a></span>
<span class="normal"><a href="#__codelineno-0-1255">1255</a></span>
<span class="normal"><a href="#__codelineno-0-1256">1256</a></span>
<span class="normal"><a href="#__codelineno-0-1257">1257</a></span>
<span class="normal"><a href="#__codelineno-0-1258">1258</a></span>
<span class="normal"><a href="#__codelineno-0-1259">1259</a></span>
<span class="normal"><a href="#__codelineno-0-1260">1260</a></span>
<span class="normal"><a href="#__codelineno-0-1261">1261</a></span>
<span class="normal"><a href="#__codelineno-0-1262">1262</a></span>
<span class="normal"><a href="#__codelineno-0-1263">1263</a></span>
<span class="normal"><a href="#__codelineno-0-1264">1264</a></span>
<span class="normal"><a href="#__codelineno-0-1265">1265</a></span>
<span class="normal"><a href="#__codelineno-0-1266">1266</a></span>
<span class="normal"><a href="#__codelineno-0-1267">1267</a></span>
<span class="normal"><a href="#__codelineno-0-1268">1268</a></span>
<span class="normal"><a href="#__codelineno-0-1269">1269</a></span>
<span class="normal"><a href="#__codelineno-0-1270">1270</a></span>
<span class="normal"><a href="#__codelineno-0-1271">1271</a></span>
<span class="normal"><a href="#__codelineno-0-1272">1272</a></span>
<span class="normal"><a href="#__codelineno-0-1273">1273</a></span>
<span class="normal"><a href="#__codelineno-0-1274">1274</a></span>
<span class="normal"><a href="#__codelineno-0-1275">1275</a></span>
<span class="normal"><a href="#__codelineno-0-1276">1276</a></span>
<span class="normal"><a href="#__codelineno-0-1277">1277</a></span>
<span class="normal"><a href="#__codelineno-0-1278">1278</a></span>
<span class="normal"><a href="#__codelineno-0-1279">1279</a></span>
<span class="normal"><a href="#__codelineno-0-1280">1280</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-1185"><a id="__codelineno-0-1185" name="__codelineno-0-1185"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_test_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-1186"><a id="__codelineno-0-1186" name="__codelineno-0-1186"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;test&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-1187"><a id="__codelineno-0-1187" name="__codelineno-0-1187"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Test command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-1188"><a id="__codelineno-0-1188" name="__codelineno-0-1188"></a>
</span><span id="__span-0-1189"><a id="__codelineno-0-1189" name="__codelineno-0-1189"></a>    <span class="c1"># Check if pytest is available</span>
</span><span id="__span-0-1190"><a id="__codelineno-0-1190" name="__codelineno-0-1190"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1191"><a id="__codelineno-0-1191" name="__codelineno-0-1191"></a>        <span class="kn">import</span><span class="w"> </span><span class="nn">pytest</span>
</span><span id="__span-0-1192"><a id="__codelineno-0-1192" name="__codelineno-0-1192"></a>    <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
</span><span id="__span-0-1193"><a id="__codelineno-0-1193" name="__codelineno-0-1193"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ pytest is not installed. Install it with: pip install pytest&quot;</span><span class="p">)</span>
</span><span id="__span-0-1194"><a id="__codelineno-0-1194" name="__codelineno-0-1194"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-1195"><a id="__codelineno-0-1195" name="__codelineno-0-1195"></a>
</span><span id="__span-0-1196"><a id="__codelineno-0-1196" name="__codelineno-0-1196"></a>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🧪 Running tests...&quot;</span><span class="p">)</span>
</span><span id="__span-0-1197"><a id="__codelineno-0-1197" name="__codelineno-0-1197"></a>
</span><span id="__span-0-1198"><a id="__codelineno-0-1198" name="__codelineno-0-1198"></a>    <span class="c1"># Build pytest command</span>
</span><span id="__span-0-1199"><a id="__codelineno-0-1199" name="__codelineno-0-1199"></a>    <span class="n">pytest_args</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-1200"><a id="__codelineno-0-1200" name="__codelineno-0-1200"></a>
</span><span id="__span-0-1201"><a id="__codelineno-0-1201" name="__codelineno-0-1201"></a>    <span class="c1"># Determine what to test</span>
</span><span id="__span-0-1202"><a id="__codelineno-0-1202" name="__codelineno-0-1202"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">test_path</span><span class="p">:</span>
</span><span id="__span-0-1203"><a id="__codelineno-0-1203" name="__codelineno-0-1203"></a>        <span class="n">pytest_args</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">args_ns</span><span class="o">.</span><span class="n">test_path</span><span class="p">)</span>
</span><span id="__span-0-1204"><a id="__codelineno-0-1204" name="__codelineno-0-1204"></a>    <span class="k">elif</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugins</span><span class="p">:</span>
</span><span id="__span-0-1205"><a id="__codelineno-0-1205" name="__codelineno-0-1205"></a>        <span class="c1"># Look for plugin tests</span>
</span><span id="__span-0-1206"><a id="__codelineno-0-1206" name="__codelineno-0-1206"></a>        <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-1207"><a id="__codelineno-0-1207" name="__codelineno-0-1207"></a>        <span class="k">if</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1208"><a id="__codelineno-0-1208" name="__codelineno-0-1208"></a>            <span class="n">plugin_test_paths</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-1209"><a id="__codelineno-0-1209" name="__codelineno-0-1209"></a>            <span class="k">for</span> <span class="n">plugin_dir</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">():</span>
</span><span id="__span-0-1210"><a id="__codelineno-0-1210" name="__codelineno-0-1210"></a>                <span class="k">if</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">):</span>
</span><span id="__span-0-1211"><a id="__codelineno-0-1211" name="__codelineno-0-1211"></a>                    <span class="n">test_files</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;test_*.py&quot;</span><span class="p">))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span>
</span><span id="__span-0-1212"><a id="__codelineno-0-1212" name="__codelineno-0-1212"></a>                        <span class="n">plugin_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*_test.py&quot;</span><span class="p">)</span>
</span><span id="__span-0-1213"><a id="__codelineno-0-1213" name="__codelineno-0-1213"></a>                    <span class="p">)</span>
</span><span id="__span-0-1214"><a id="__codelineno-0-1214" name="__codelineno-0-1214"></a>                    <span class="k">if</span> <span class="n">test_files</span><span class="p">:</span>
</span><span id="__span-0-1215"><a id="__codelineno-0-1215" name="__codelineno-0-1215"></a>                        <span class="n">plugin_test_paths</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">test_files</span><span class="p">)</span>
</span><span id="__span-0-1216"><a id="__codelineno-0-1216" name="__codelineno-0-1216"></a>
</span><span id="__span-0-1217"><a id="__codelineno-0-1217" name="__codelineno-0-1217"></a>            <span class="k">if</span> <span class="n">plugin_test_paths</span><span class="p">:</span>
</span><span id="__span-0-1218"><a id="__codelineno-0-1218" name="__codelineno-0-1218"></a>                <span class="n">pytest_args</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">plugin_test_paths</span><span class="p">)</span>
</span><span id="__span-0-1219"><a id="__codelineno-0-1219" name="__codelineno-0-1219"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;📦 Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">plugin_test_paths</span><span class="p">)</span><span class="si">}</span><span class="s2"> plugin test files&quot;</span><span class="p">)</span>
</span><span id="__span-0-1220"><a id="__codelineno-0-1220" name="__codelineno-0-1220"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1221"><a id="__codelineno-0-1221" name="__codelineno-0-1221"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ℹ️  No plugin tests found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1222"><a id="__codelineno-0-1222" name="__codelineno-0-1222"></a>                <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1223"><a id="__codelineno-0-1223" name="__codelineno-0-1223"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1224"><a id="__codelineno-0-1224" name="__codelineno-0-1224"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  No plugins directory found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1225"><a id="__codelineno-0-1225" name="__codelineno-0-1225"></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1226"><a id="__codelineno-0-1226" name="__codelineno-0-1226"></a>    <span class="k">elif</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">e2e</span><span class="p">:</span>
</span><span id="__span-0-1227"><a id="__codelineno-0-1227" name="__codelineno-0-1227"></a>        <span class="c1"># Run e2e tests</span>
</span><span id="__span-0-1228"><a id="__codelineno-0-1228" name="__codelineno-0-1228"></a>        <span class="n">e2e_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;tests&quot;</span> <span class="o">/</span> <span class="s2">&quot;e2e&quot;</span>
</span><span id="__span-0-1229"><a id="__codelineno-0-1229" name="__codelineno-0-1229"></a>        <span class="k">if</span> <span class="n">e2e_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1230"><a id="__codelineno-0-1230" name="__codelineno-0-1230"></a>            <span class="n">pytest_args</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">e2e_dir</span><span class="p">))</span>
</span><span id="__span-0-1231"><a id="__codelineno-0-1231" name="__codelineno-0-1231"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🌐 Running end-to-end tests&quot;</span><span class="p">)</span>
</span><span id="__span-0-1232"><a id="__codelineno-0-1232" name="__codelineno-0-1232"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1233"><a id="__codelineno-0-1233" name="__codelineno-0-1233"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  No e2e tests directory found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1234"><a id="__codelineno-0-1234" name="__codelineno-0-1234"></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1235"><a id="__codelineno-0-1235" name="__codelineno-0-1235"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1236"><a id="__codelineno-0-1236" name="__codelineno-0-1236"></a>        <span class="c1"># Run all tests</span>
</span><span id="__span-0-1237"><a id="__codelineno-0-1237" name="__codelineno-0-1237"></a>        <span class="n">test_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;tests&quot;</span>
</span><span id="__span-0-1238"><a id="__codelineno-0-1238" name="__codelineno-0-1238"></a>        <span class="k">if</span> <span class="n">test_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-1239"><a id="__codelineno-0-1239" name="__codelineno-0-1239"></a>            <span class="n">pytest_args</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">test_dir</span><span class="p">))</span>
</span><span id="__span-0-1240"><a id="__codelineno-0-1240" name="__codelineno-0-1240"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🔍 Running all tests&quot;</span><span class="p">)</span>
</span><span id="__span-0-1241"><a id="__codelineno-0-1241" name="__codelineno-0-1241"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1242"><a id="__codelineno-0-1242" name="__codelineno-0-1242"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  No tests directory found&quot;</span><span class="p">)</span>
</span><span id="__span-0-1243"><a id="__codelineno-0-1243" name="__codelineno-0-1243"></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-1244"><a id="__codelineno-0-1244" name="__codelineno-0-1244"></a>
</span><span id="__span-0-1245"><a id="__codelineno-0-1245" name="__codelineno-0-1245"></a>    <span class="c1"># Add coverage if requested</span>
</span><span id="__span-0-1246"><a id="__codelineno-0-1246" name="__codelineno-0-1246"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">coverage</span><span class="p">:</span>
</span><span id="__span-0-1247"><a id="__codelineno-0-1247" name="__codelineno-0-1247"></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1248"><a id="__codelineno-0-1248" name="__codelineno-0-1248"></a>            <span class="kn">import</span><span class="w"> </span><span class="nn">importlib.util</span>
</span><span id="__span-0-1249"><a id="__codelineno-0-1249" name="__codelineno-0-1249"></a>
</span><span id="__span-0-1250"><a id="__codelineno-0-1250" name="__codelineno-0-1250"></a>            <span class="k">if</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="s2">&quot;pytest_cov&quot;</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="__span-0-1251"><a id="__codelineno-0-1251" name="__codelineno-0-1251"></a>                <span class="n">pytest_args</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span>
</span><span id="__span-0-1252"><a id="__codelineno-0-1252" name="__codelineno-0-1252"></a>                    <span class="p">[</span><span class="s2">&quot;--cov=.&quot;</span><span class="p">,</span> <span class="s2">&quot;--cov-report=html&quot;</span><span class="p">,</span> <span class="s2">&quot;--cov-report=term&quot;</span><span class="p">]</span>
</span><span id="__span-0-1253"><a id="__codelineno-0-1253" name="__codelineno-0-1253"></a>                <span class="p">)</span>
</span><span id="__span-0-1254"><a id="__codelineno-0-1254" name="__codelineno-0-1254"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;📊 Coverage reporting enabled&quot;</span><span class="p">)</span>
</span><span id="__span-0-1255"><a id="__codelineno-0-1255" name="__codelineno-0-1255"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1256"><a id="__codelineno-0-1256" name="__codelineno-0-1256"></a>                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  pytest-cov not installed, skipping coverage reporting&quot;</span><span class="p">)</span>
</span><span id="__span-0-1257"><a id="__codelineno-0-1257" name="__codelineno-0-1257"></a>        <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
</span><span id="__span-0-1258"><a id="__codelineno-0-1258" name="__codelineno-0-1258"></a>            <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-1259"><a id="__codelineno-0-1259" name="__codelineno-0-1259"></a>                <span class="s2">&quot;⚠️  pytest-cov not installed, skipping coverage. Install with: pip install pytest-cov&quot;</span>
</span><span id="__span-0-1260"><a id="__codelineno-0-1260" name="__codelineno-0-1260"></a>            <span class="p">)</span>
</span><span id="__span-0-1261"><a id="__codelineno-0-1261" name="__codelineno-0-1261"></a>
</span><span id="__span-0-1262"><a id="__codelineno-0-1262" name="__codelineno-0-1262"></a>    <span class="c1"># Add verbose if requested</span>
</span><span id="__span-0-1263"><a id="__codelineno-0-1263" name="__codelineno-0-1263"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">verbose</span><span class="p">:</span>
</span><span id="__span-0-1264"><a id="__codelineno-0-1264" name="__codelineno-0-1264"></a>        <span class="n">pytest_args</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">)</span>
</span><span id="__span-0-1265"><a id="__codelineno-0-1265" name="__codelineno-0-1265"></a>
</span><span id="__span-0-1266"><a id="__codelineno-0-1266" name="__codelineno-0-1266"></a>    <span class="c1"># Run pytest</span>
</span><span id="__span-0-1267"><a id="__codelineno-0-1267" name="__codelineno-0-1267"></a>    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-1268"><a id="__codelineno-0-1268" name="__codelineno-0-1268"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Running: pytest </span><span class="si">{</span><span class="s1">&#39; &#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">pytest_args</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1269"><a id="__codelineno-0-1269" name="__codelineno-0-1269"></a>        <span class="n">exit_code</span> <span class="o">=</span> <span class="n">pytest</span><span class="o">.</span><span class="n">main</span><span class="p">(</span><span class="n">pytest_args</span><span class="p">)</span>
</span><span id="__span-0-1270"><a id="__codelineno-0-1270" name="__codelineno-0-1270"></a>
</span><span id="__span-0-1271"><a id="__codelineno-0-1271" name="__codelineno-0-1271"></a>        <span class="k">if</span> <span class="n">exit_code</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="__span-0-1272"><a id="__codelineno-0-1272" name="__codelineno-0-1272"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;✅ All tests passed!&quot;</span><span class="p">)</span>
</span><span id="__span-0-1273"><a id="__codelineno-0-1273" name="__codelineno-0-1273"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-1274"><a id="__codelineno-0-1274" name="__codelineno-0-1274"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Tests failed with exit code </span><span class="si">{</span><span class="n">exit_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1275"><a id="__codelineno-0-1275" name="__codelineno-0-1275"></a>
</span><span id="__span-0-1276"><a id="__codelineno-0-1276" name="__codelineno-0-1276"></a>        <span class="k">return</span> <span class="n">exit_code</span> <span class="o">==</span> <span class="mi">0</span>
</span><span id="__span-0-1277"><a id="__codelineno-0-1277" name="__codelineno-0-1277"></a>
</span><span id="__span-0-1278"><a id="__codelineno-0-1278" name="__codelineno-0-1278"></a>    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-1279"><a id="__codelineno-0-1279" name="__codelineno-0-1279"></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error running tests: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-1280"><a id="__codelineno-0-1280" name="__codelineno-0-1280"></a>        <span class="k">return</span> <span class="kc">False</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>

<div class="doc doc-object doc-function">


<h3 id="serv.cli.commands.handle_validate_plugin_command" class="doc doc-heading">
            <span class="doc doc-object-name doc-function-name">handle_validate_plugin_command</span>


</h3>
<div class="language-python doc-signature highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a><span class="nf">handle_validate_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">)</span>
</span></code></pre></div>

    <div class="doc doc-contents ">

        <p>Handles the 'plugin validate' command.</p>


            <details class="quote">
              <summary>Source code in <code>serv/cli/commands.py</code></summary>
              <div class="language-python highlight"><table class="highlighttable"><tr><td class="linenos"><div class="linenodiv"><pre><span></span><span class="normal"><a href="#__codelineno-0-572">572</a></span>
<span class="normal"><a href="#__codelineno-0-573">573</a></span>
<span class="normal"><a href="#__codelineno-0-574">574</a></span>
<span class="normal"><a href="#__codelineno-0-575">575</a></span>
<span class="normal"><a href="#__codelineno-0-576">576</a></span>
<span class="normal"><a href="#__codelineno-0-577">577</a></span>
<span class="normal"><a href="#__codelineno-0-578">578</a></span>
<span class="normal"><a href="#__codelineno-0-579">579</a></span>
<span class="normal"><a href="#__codelineno-0-580">580</a></span>
<span class="normal"><a href="#__codelineno-0-581">581</a></span>
<span class="normal"><a href="#__codelineno-0-582">582</a></span>
<span class="normal"><a href="#__codelineno-0-583">583</a></span>
<span class="normal"><a href="#__codelineno-0-584">584</a></span>
<span class="normal"><a href="#__codelineno-0-585">585</a></span>
<span class="normal"><a href="#__codelineno-0-586">586</a></span>
<span class="normal"><a href="#__codelineno-0-587">587</a></span>
<span class="normal"><a href="#__codelineno-0-588">588</a></span>
<span class="normal"><a href="#__codelineno-0-589">589</a></span>
<span class="normal"><a href="#__codelineno-0-590">590</a></span>
<span class="normal"><a href="#__codelineno-0-591">591</a></span>
<span class="normal"><a href="#__codelineno-0-592">592</a></span>
<span class="normal"><a href="#__codelineno-0-593">593</a></span>
<span class="normal"><a href="#__codelineno-0-594">594</a></span>
<span class="normal"><a href="#__codelineno-0-595">595</a></span>
<span class="normal"><a href="#__codelineno-0-596">596</a></span>
<span class="normal"><a href="#__codelineno-0-597">597</a></span>
<span class="normal"><a href="#__codelineno-0-598">598</a></span>
<span class="normal"><a href="#__codelineno-0-599">599</a></span>
<span class="normal"><a href="#__codelineno-0-600">600</a></span>
<span class="normal"><a href="#__codelineno-0-601">601</a></span>
<span class="normal"><a href="#__codelineno-0-602">602</a></span>
<span class="normal"><a href="#__codelineno-0-603">603</a></span>
<span class="normal"><a href="#__codelineno-0-604">604</a></span>
<span class="normal"><a href="#__codelineno-0-605">605</a></span>
<span class="normal"><a href="#__codelineno-0-606">606</a></span>
<span class="normal"><a href="#__codelineno-0-607">607</a></span>
<span class="normal"><a href="#__codelineno-0-608">608</a></span>
<span class="normal"><a href="#__codelineno-0-609">609</a></span>
<span class="normal"><a href="#__codelineno-0-610">610</a></span>
<span class="normal"><a href="#__codelineno-0-611">611</a></span>
<span class="normal"><a href="#__codelineno-0-612">612</a></span>
<span class="normal"><a href="#__codelineno-0-613">613</a></span>
<span class="normal"><a href="#__codelineno-0-614">614</a></span>
<span class="normal"><a href="#__codelineno-0-615">615</a></span>
<span class="normal"><a href="#__codelineno-0-616">616</a></span>
<span class="normal"><a href="#__codelineno-0-617">617</a></span>
<span class="normal"><a href="#__codelineno-0-618">618</a></span>
<span class="normal"><a href="#__codelineno-0-619">619</a></span>
<span class="normal"><a href="#__codelineno-0-620">620</a></span>
<span class="normal"><a href="#__codelineno-0-621">621</a></span>
<span class="normal"><a href="#__codelineno-0-622">622</a></span>
<span class="normal"><a href="#__codelineno-0-623">623</a></span>
<span class="normal"><a href="#__codelineno-0-624">624</a></span>
<span class="normal"><a href="#__codelineno-0-625">625</a></span>
<span class="normal"><a href="#__codelineno-0-626">626</a></span>
<span class="normal"><a href="#__codelineno-0-627">627</a></span>
<span class="normal"><a href="#__codelineno-0-628">628</a></span>
<span class="normal"><a href="#__codelineno-0-629">629</a></span>
<span class="normal"><a href="#__codelineno-0-630">630</a></span>
<span class="normal"><a href="#__codelineno-0-631">631</a></span>
<span class="normal"><a href="#__codelineno-0-632">632</a></span>
<span class="normal"><a href="#__codelineno-0-633">633</a></span>
<span class="normal"><a href="#__codelineno-0-634">634</a></span>
<span class="normal"><a href="#__codelineno-0-635">635</a></span>
<span class="normal"><a href="#__codelineno-0-636">636</a></span>
<span class="normal"><a href="#__codelineno-0-637">637</a></span>
<span class="normal"><a href="#__codelineno-0-638">638</a></span>
<span class="normal"><a href="#__codelineno-0-639">639</a></span>
<span class="normal"><a href="#__codelineno-0-640">640</a></span>
<span class="normal"><a href="#__codelineno-0-641">641</a></span>
<span class="normal"><a href="#__codelineno-0-642">642</a></span>
<span class="normal"><a href="#__codelineno-0-643">643</a></span>
<span class="normal"><a href="#__codelineno-0-644">644</a></span>
<span class="normal"><a href="#__codelineno-0-645">645</a></span>
<span class="normal"><a href="#__codelineno-0-646">646</a></span>
<span class="normal"><a href="#__codelineno-0-647">647</a></span>
<span class="normal"><a href="#__codelineno-0-648">648</a></span>
<span class="normal"><a href="#__codelineno-0-649">649</a></span>
<span class="normal"><a href="#__codelineno-0-650">650</a></span>
<span class="normal"><a href="#__codelineno-0-651">651</a></span>
<span class="normal"><a href="#__codelineno-0-652">652</a></span>
<span class="normal"><a href="#__codelineno-0-653">653</a></span>
<span class="normal"><a href="#__codelineno-0-654">654</a></span>
<span class="normal"><a href="#__codelineno-0-655">655</a></span>
<span class="normal"><a href="#__codelineno-0-656">656</a></span>
<span class="normal"><a href="#__codelineno-0-657">657</a></span>
<span class="normal"><a href="#__codelineno-0-658">658</a></span>
<span class="normal"><a href="#__codelineno-0-659">659</a></span>
<span class="normal"><a href="#__codelineno-0-660">660</a></span>
<span class="normal"><a href="#__codelineno-0-661">661</a></span>
<span class="normal"><a href="#__codelineno-0-662">662</a></span>
<span class="normal"><a href="#__codelineno-0-663">663</a></span>
<span class="normal"><a href="#__codelineno-0-664">664</a></span>
<span class="normal"><a href="#__codelineno-0-665">665</a></span>
<span class="normal"><a href="#__codelineno-0-666">666</a></span>
<span class="normal"><a href="#__codelineno-0-667">667</a></span>
<span class="normal"><a href="#__codelineno-0-668">668</a></span>
<span class="normal"><a href="#__codelineno-0-669">669</a></span>
<span class="normal"><a href="#__codelineno-0-670">670</a></span>
<span class="normal"><a href="#__codelineno-0-671">671</a></span>
<span class="normal"><a href="#__codelineno-0-672">672</a></span>
<span class="normal"><a href="#__codelineno-0-673">673</a></span>
<span class="normal"><a href="#__codelineno-0-674">674</a></span>
<span class="normal"><a href="#__codelineno-0-675">675</a></span>
<span class="normal"><a href="#__codelineno-0-676">676</a></span>
<span class="normal"><a href="#__codelineno-0-677">677</a></span>
<span class="normal"><a href="#__codelineno-0-678">678</a></span>
<span class="normal"><a href="#__codelineno-0-679">679</a></span>
<span class="normal"><a href="#__codelineno-0-680">680</a></span>
<span class="normal"><a href="#__codelineno-0-681">681</a></span>
<span class="normal"><a href="#__codelineno-0-682">682</a></span>
<span class="normal"><a href="#__codelineno-0-683">683</a></span>
<span class="normal"><a href="#__codelineno-0-684">684</a></span>
<span class="normal"><a href="#__codelineno-0-685">685</a></span>
<span class="normal"><a href="#__codelineno-0-686">686</a></span>
<span class="normal"><a href="#__codelineno-0-687">687</a></span>
<span class="normal"><a href="#__codelineno-0-688">688</a></span>
<span class="normal"><a href="#__codelineno-0-689">689</a></span>
<span class="normal"><a href="#__codelineno-0-690">690</a></span>
<span class="normal"><a href="#__codelineno-0-691">691</a></span>
<span class="normal"><a href="#__codelineno-0-692">692</a></span>
<span class="normal"><a href="#__codelineno-0-693">693</a></span>
<span class="normal"><a href="#__codelineno-0-694">694</a></span>
<span class="normal"><a href="#__codelineno-0-695">695</a></span>
<span class="normal"><a href="#__codelineno-0-696">696</a></span>
<span class="normal"><a href="#__codelineno-0-697">697</a></span>
<span class="normal"><a href="#__codelineno-0-698">698</a></span>
<span class="normal"><a href="#__codelineno-0-699">699</a></span>
<span class="normal"><a href="#__codelineno-0-700">700</a></span>
<span class="normal"><a href="#__codelineno-0-701">701</a></span>
<span class="normal"><a href="#__codelineno-0-702">702</a></span>
<span class="normal"><a href="#__codelineno-0-703">703</a></span>
<span class="normal"><a href="#__codelineno-0-704">704</a></span>
<span class="normal"><a href="#__codelineno-0-705">705</a></span>
<span class="normal"><a href="#__codelineno-0-706">706</a></span>
<span class="normal"><a href="#__codelineno-0-707">707</a></span>
<span class="normal"><a href="#__codelineno-0-708">708</a></span>
<span class="normal"><a href="#__codelineno-0-709">709</a></span>
<span class="normal"><a href="#__codelineno-0-710">710</a></span>
<span class="normal"><a href="#__codelineno-0-711">711</a></span>
<span class="normal"><a href="#__codelineno-0-712">712</a></span>
<span class="normal"><a href="#__codelineno-0-713">713</a></span>
<span class="normal"><a href="#__codelineno-0-714">714</a></span>
<span class="normal"><a href="#__codelineno-0-715">715</a></span>
<span class="normal"><a href="#__codelineno-0-716">716</a></span>
<span class="normal"><a href="#__codelineno-0-717">717</a></span>
<span class="normal"><a href="#__codelineno-0-718">718</a></span>
<span class="normal"><a href="#__codelineno-0-719">719</a></span>
<span class="normal"><a href="#__codelineno-0-720">720</a></span>
<span class="normal"><a href="#__codelineno-0-721">721</a></span>
<span class="normal"><a href="#__codelineno-0-722">722</a></span></pre></div></td><td class="code"><div><pre><span></span><code><span id="__span-0-572"><a id="__codelineno-0-572" name="__codelineno-0-572"></a><span class="k">def</span><span class="w"> </span><span class="nf">handle_validate_plugin_command</span><span class="p">(</span><span class="n">args_ns</span><span class="p">):</span>
</span><span id="__span-0-573"><a id="__codelineno-0-573" name="__codelineno-0-573"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;Handles the &#39;plugin validate&#39; command.&quot;&quot;&quot;</span>
</span><span id="__span-0-574"><a id="__codelineno-0-574" name="__codelineno-0-574"></a>    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Plugin validate command started.&quot;</span><span class="p">)</span>
</span><span id="__span-0-575"><a id="__codelineno-0-575" name="__codelineno-0-575"></a>
</span><span id="__span-0-576"><a id="__codelineno-0-576" name="__codelineno-0-576"></a>    <span class="n">plugins_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;plugins&quot;</span>
</span><span id="__span-0-577"><a id="__codelineno-0-577" name="__codelineno-0-577"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-578"><a id="__codelineno-0-578" name="__codelineno-0-578"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ No plugins directory found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-579"><a id="__codelineno-0-579" name="__codelineno-0-579"></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-580"><a id="__codelineno-0-580" name="__codelineno-0-580"></a>
</span><span id="__span-0-581"><a id="__codelineno-0-581" name="__codelineno-0-581"></a>    <span class="c1"># Determine which plugins to validate</span>
</span><span id="__span-0-582"><a id="__codelineno-0-582" name="__codelineno-0-582"></a>    <span class="k">if</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin_identifier</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">all</span><span class="p">:</span>
</span><span id="__span-0-583"><a id="__codelineno-0-583" name="__codelineno-0-583"></a>        <span class="c1"># Validate specific plugin</span>
</span><span id="__span-0-584"><a id="__codelineno-0-584" name="__codelineno-0-584"></a>        <span class="n">plugin_dirs</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="__span-0-585"><a id="__codelineno-0-585" name="__codelineno-0-585"></a>        <span class="n">plugin_dir</span> <span class="o">=</span> <span class="n">plugins_dir</span> <span class="o">/</span> <span class="n">args_ns</span><span class="o">.</span><span class="n">plugin_identifier</span>
</span><span id="__span-0-586"><a id="__codelineno-0-586" name="__codelineno-0-586"></a>        <span class="k">if</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="n">plugin_dir</span><span class="o">.</span><span class="n">is_dir</span><span class="p">():</span>
</span><span id="__span-0-587"><a id="__codelineno-0-587" name="__codelineno-0-587"></a>            <span class="n">plugin_dirs</span> <span class="o">=</span> <span class="p">[</span><span class="n">plugin_dir</span><span class="p">]</span>
</span><span id="__span-0-588"><a id="__codelineno-0-588" name="__codelineno-0-588"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-589"><a id="__codelineno-0-589" name="__codelineno-0-589"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Plugin &#39;</span><span class="si">{</span><span class="n">args_ns</span><span class="o">.</span><span class="n">plugin_identifier</span><span class="si">}</span><span class="s2">&#39; not found.&quot;</span><span class="p">)</span>
</span><span id="__span-0-590"><a id="__codelineno-0-590" name="__codelineno-0-590"></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="__span-0-591"><a id="__codelineno-0-591" name="__codelineno-0-591"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-592"><a id="__codelineno-0-592" name="__codelineno-0-592"></a>        <span class="c1"># Validate all plugins</span>
</span><span id="__span-0-593"><a id="__codelineno-0-593" name="__codelineno-0-593"></a>        <span class="n">plugin_dirs</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="__span-0-594"><a id="__codelineno-0-594" name="__codelineno-0-594"></a>            <span class="n">d</span>
</span><span id="__span-0-595"><a id="__codelineno-0-595" name="__codelineno-0-595"></a>            <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">plugins_dir</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span>
</span><span id="__span-0-596"><a id="__codelineno-0-596" name="__codelineno-0-596"></a>            <span class="k">if</span> <span class="n">d</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">d</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">)</span>
</span><span id="__span-0-597"><a id="__codelineno-0-597" name="__codelineno-0-597"></a>        <span class="p">]</span>
</span><span id="__span-0-598"><a id="__codelineno-0-598" name="__codelineno-0-598"></a>
</span><span id="__span-0-599"><a id="__codelineno-0-599" name="__codelineno-0-599"></a>    <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_dirs</span><span class="p">:</span>
</span><span id="__span-0-600"><a id="__codelineno-0-600" name="__codelineno-0-600"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ℹ️  No plugins found to validate.&quot;</span><span class="p">)</span>
</span><span id="__span-0-601"><a id="__codelineno-0-601" name="__codelineno-0-601"></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="__span-0-602"><a id="__codelineno-0-602" name="__codelineno-0-602"></a>
</span><span id="__span-0-603"><a id="__codelineno-0-603" name="__codelineno-0-603"></a>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;=== Validating </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">plugin_dirs</span><span class="p">)</span><span class="si">}</span><span class="s2"> Plugin(s) ===&quot;</span><span class="p">)</span>
</span><span id="__span-0-604"><a id="__codelineno-0-604" name="__codelineno-0-604"></a>
</span><span id="__span-0-605"><a id="__codelineno-0-605" name="__codelineno-0-605"></a>    <span class="n">total_issues</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="__span-0-606"><a id="__codelineno-0-606" name="__codelineno-0-606"></a>
</span><span id="__span-0-607"><a id="__codelineno-0-607" name="__codelineno-0-607"></a>    <span class="k">for</span> <span class="n">plugin_dir</span> <span class="ow">in</span> <span class="n">plugin_dirs</span><span class="p">:</span>
</span><span id="__span-0-608"><a id="__codelineno-0-608" name="__codelineno-0-608"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">🔍 Validating plugin: </span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-609"><a id="__codelineno-0-609" name="__codelineno-0-609"></a>        <span class="n">issues</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="__span-0-610"><a id="__codelineno-0-610" name="__codelineno-0-610"></a>
</span><span id="__span-0-611"><a id="__codelineno-0-611" name="__codelineno-0-611"></a>        <span class="c1"># Check for plugin.yaml</span>
</span><span id="__span-0-612"><a id="__codelineno-0-612" name="__codelineno-0-612"></a>        <span class="n">plugin_yaml</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;plugin.yaml&quot;</span>
</span><span id="__span-0-613"><a id="__codelineno-0-613" name="__codelineno-0-613"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_yaml</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-614"><a id="__codelineno-0-614" name="__codelineno-0-614"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ Missing plugin.yaml&quot;</span><span class="p">)</span>
</span><span id="__span-0-615"><a id="__codelineno-0-615" name="__codelineno-0-615"></a>            <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-616"><a id="__codelineno-0-616" name="__codelineno-0-616"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-617"><a id="__codelineno-0-617" name="__codelineno-0-617"></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-618"><a id="__codelineno-0-618" name="__codelineno-0-618"></a>                <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">plugin_yaml</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-619"><a id="__codelineno-0-619" name="__codelineno-0-619"></a>                    <span class="n">plugin_config</span> <span class="o">=</span> <span class="n">yaml</span><span class="o">.</span><span class="n">safe_load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="__span-0-620"><a id="__codelineno-0-620" name="__codelineno-0-620"></a>
</span><span id="__span-0-621"><a id="__codelineno-0-621" name="__codelineno-0-621"></a>                <span class="k">if</span> <span class="ow">not</span> <span class="n">plugin_config</span><span class="p">:</span>
</span><span id="__span-0-622"><a id="__codelineno-0-622" name="__codelineno-0-622"></a>                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ plugin.yaml is empty&quot;</span><span class="p">)</span>
</span><span id="__span-0-623"><a id="__codelineno-0-623" name="__codelineno-0-623"></a>                    <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-624"><a id="__codelineno-0-624" name="__codelineno-0-624"></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-625"><a id="__codelineno-0-625" name="__codelineno-0-625"></a>                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;✅ plugin.yaml is valid YAML&quot;</span><span class="p">)</span>
</span><span id="__span-0-626"><a id="__codelineno-0-626" name="__codelineno-0-626"></a>
</span><span id="__span-0-627"><a id="__codelineno-0-627" name="__codelineno-0-627"></a>                    <span class="c1"># Check required fields</span>
</span><span id="__span-0-628"><a id="__codelineno-0-628" name="__codelineno-0-628"></a>                    <span class="n">required_fields</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="s2">&quot;version&quot;</span><span class="p">]</span>
</span><span id="__span-0-629"><a id="__codelineno-0-629" name="__codelineno-0-629"></a>                    <span class="k">for</span> <span class="n">field</span> <span class="ow">in</span> <span class="n">required_fields</span><span class="p">:</span>
</span><span id="__span-0-630"><a id="__codelineno-0-630" name="__codelineno-0-630"></a>                        <span class="k">if</span> <span class="n">field</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">plugin_config</span><span class="p">:</span>
</span><span id="__span-0-631"><a id="__codelineno-0-631" name="__codelineno-0-631"></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Missing required field: </span><span class="si">{</span><span class="n">field</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-632"><a id="__codelineno-0-632" name="__codelineno-0-632"></a>                            <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-633"><a id="__codelineno-0-633" name="__codelineno-0-633"></a>                        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-634"><a id="__codelineno-0-634" name="__codelineno-0-634"></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ Has required field: </span><span class="si">{</span><span class="n">field</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-635"><a id="__codelineno-0-635" name="__codelineno-0-635"></a>
</span><span id="__span-0-636"><a id="__codelineno-0-636" name="__codelineno-0-636"></a>                    <span class="c1"># Check optional but recommended fields</span>
</span><span id="__span-0-637"><a id="__codelineno-0-637" name="__codelineno-0-637"></a>                    <span class="n">recommended_fields</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;author&quot;</span><span class="p">]</span>
</span><span id="__span-0-638"><a id="__codelineno-0-638" name="__codelineno-0-638"></a>                    <span class="k">for</span> <span class="n">field</span> <span class="ow">in</span> <span class="n">recommended_fields</span><span class="p">:</span>
</span><span id="__span-0-639"><a id="__codelineno-0-639" name="__codelineno-0-639"></a>                        <span class="k">if</span> <span class="n">field</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">plugin_config</span><span class="p">:</span>
</span><span id="__span-0-640"><a id="__codelineno-0-640" name="__codelineno-0-640"></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Missing recommended field: </span><span class="si">{</span><span class="n">field</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-641"><a id="__codelineno-0-641" name="__codelineno-0-641"></a>                        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-642"><a id="__codelineno-0-642" name="__codelineno-0-642"></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ Has recommended field: </span><span class="si">{</span><span class="n">field</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-643"><a id="__codelineno-0-643" name="__codelineno-0-643"></a>
</span><span id="__span-0-644"><a id="__codelineno-0-644" name="__codelineno-0-644"></a>                    <span class="c1"># Validate version format</span>
</span><span id="__span-0-645"><a id="__codelineno-0-645" name="__codelineno-0-645"></a>                    <span class="n">version</span> <span class="o">=</span> <span class="n">plugin_config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;version&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
</span><span id="__span-0-646"><a id="__codelineno-0-646" name="__codelineno-0-646"></a>                    <span class="k">if</span> <span class="p">(</span>
</span><span id="__span-0-647"><a id="__codelineno-0-647" name="__codelineno-0-647"></a>                        <span class="n">version</span>
</span><span id="__span-0-648"><a id="__codelineno-0-648" name="__codelineno-0-648"></a>                        <span class="ow">and</span> <span class="ow">not</span> <span class="n">version</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;.&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
</span><span id="__span-0-649"><a id="__codelineno-0-649" name="__codelineno-0-649"></a>                        <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;-&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
</span><span id="__span-0-650"><a id="__codelineno-0-650" name="__codelineno-0-650"></a>                        <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;_&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
</span><span id="__span-0-651"><a id="__codelineno-0-651" name="__codelineno-0-651"></a>                        <span class="o">.</span><span class="n">isalnum</span><span class="p">()</span>
</span><span id="__span-0-652"><a id="__codelineno-0-652" name="__codelineno-0-652"></a>                    <span class="p">):</span>
</span><span id="__span-0-653"><a id="__codelineno-0-653" name="__codelineno-0-653"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Version format may be invalid: </span><span class="si">{</span><span class="n">version</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-654"><a id="__codelineno-0-654" name="__codelineno-0-654"></a>
</span><span id="__span-0-655"><a id="__codelineno-0-655" name="__codelineno-0-655"></a>            <span class="k">except</span> <span class="n">yaml</span><span class="o">.</span><span class="n">YAMLError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-656"><a id="__codelineno-0-656" name="__codelineno-0-656"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ plugin.yaml contains invalid YAML: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-657"><a id="__codelineno-0-657" name="__codelineno-0-657"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-658"><a id="__codelineno-0-658" name="__codelineno-0-658"></a>            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-659"><a id="__codelineno-0-659" name="__codelineno-0-659"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ Error reading plugin.yaml: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-660"><a id="__codelineno-0-660" name="__codelineno-0-660"></a>                <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-661"><a id="__codelineno-0-661" name="__codelineno-0-661"></a>
</span><span id="__span-0-662"><a id="__codelineno-0-662" name="__codelineno-0-662"></a>        <span class="c1"># Check for __init__.py</span>
</span><span id="__span-0-663"><a id="__codelineno-0-663" name="__codelineno-0-663"></a>        <span class="n">init_file</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;__init__.py&quot;</span>
</span><span id="__span-0-664"><a id="__codelineno-0-664" name="__codelineno-0-664"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">init_file</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-665"><a id="__codelineno-0-665" name="__codelineno-0-665"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️  Missing __init__.py (recommended for Python packages)&quot;</span><span class="p">)</span>
</span><span id="__span-0-666"><a id="__codelineno-0-666" name="__codelineno-0-666"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-667"><a id="__codelineno-0-667" name="__codelineno-0-667"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;✅ Has __init__.py&quot;</span><span class="p">)</span>
</span><span id="__span-0-668"><a id="__codelineno-0-668" name="__codelineno-0-668"></a>
</span><span id="__span-0-669"><a id="__codelineno-0-669" name="__codelineno-0-669"></a>        <span class="c1"># Check for Python files</span>
</span><span id="__span-0-670"><a id="__codelineno-0-670" name="__codelineno-0-670"></a>        <span class="n">py_files</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*.py&quot;</span><span class="p">))</span>
</span><span id="__span-0-671"><a id="__codelineno-0-671" name="__codelineno-0-671"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">py_files</span><span class="p">:</span>
</span><span id="__span-0-672"><a id="__codelineno-0-672" name="__codelineno-0-672"></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;❌ No Python files found&quot;</span><span class="p">)</span>
</span><span id="__span-0-673"><a id="__codelineno-0-673" name="__codelineno-0-673"></a>            <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-674"><a id="__codelineno-0-674" name="__codelineno-0-674"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-675"><a id="__codelineno-0-675" name="__codelineno-0-675"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">py_files</span><span class="p">)</span><span class="si">}</span><span class="s2"> Python file(s)&quot;</span><span class="p">)</span>
</span><span id="__span-0-676"><a id="__codelineno-0-676" name="__codelineno-0-676"></a>
</span><span id="__span-0-677"><a id="__codelineno-0-677" name="__codelineno-0-677"></a>            <span class="c1"># Check for main plugin file (matching directory name)</span>
</span><span id="__span-0-678"><a id="__codelineno-0-678" name="__codelineno-0-678"></a>            <span class="n">expected_main_file</span> <span class="o">=</span> <span class="n">plugin_dir</span> <span class="o">/</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">.py&quot;</span>
</span><span id="__span-0-679"><a id="__codelineno-0-679" name="__codelineno-0-679"></a>            <span class="k">if</span> <span class="n">expected_main_file</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-680"><a id="__codelineno-0-680" name="__codelineno-0-680"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ Has main plugin file: </span><span class="si">{</span><span class="n">expected_main_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-681"><a id="__codelineno-0-681" name="__codelineno-0-681"></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-682"><a id="__codelineno-0-682" name="__codelineno-0-682"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  No main plugin file found (expected: </span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">.py)&quot;</span><span class="p">)</span>
</span><span id="__span-0-683"><a id="__codelineno-0-683" name="__codelineno-0-683"></a>
</span><span id="__span-0-684"><a id="__codelineno-0-684" name="__codelineno-0-684"></a>        <span class="c1"># Check for common issues</span>
</span><span id="__span-0-685"><a id="__codelineno-0-685" name="__codelineno-0-685"></a>        <span class="k">if</span> <span class="p">(</span><span class="n">plugin_dir</span> <span class="o">/</span> <span class="s2">&quot;main.py&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">expected_main_file</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="__span-0-686"><a id="__codelineno-0-686" name="__codelineno-0-686"></a>            <span class="nb">print</span><span class="p">(</span>
</span><span id="__span-0-687"><a id="__codelineno-0-687" name="__codelineno-0-687"></a>                <span class="sa">f</span><span class="s2">&quot;⚠️  Found main.py but expected </span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">.py (consider renaming)&quot;</span>
</span><span id="__span-0-688"><a id="__codelineno-0-688" name="__codelineno-0-688"></a>            <span class="p">)</span>
</span><span id="__span-0-689"><a id="__codelineno-0-689" name="__codelineno-0-689"></a>
</span><span id="__span-0-690"><a id="__codelineno-0-690" name="__codelineno-0-690"></a>        <span class="c1"># Try to import the plugin (basic syntax check)</span>
</span><span id="__span-0-691"><a id="__codelineno-0-691" name="__codelineno-0-691"></a>        <span class="k">if</span> <span class="n">py_files</span><span class="p">:</span>
</span><span id="__span-0-692"><a id="__codelineno-0-692" name="__codelineno-0-692"></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-693"><a id="__codelineno-0-693" name="__codelineno-0-693"></a>                <span class="c1"># This is a basic check - we&#39;re not actually importing to avoid side effects</span>
</span><span id="__span-0-694"><a id="__codelineno-0-694" name="__codelineno-0-694"></a>                <span class="k">for</span> <span class="n">py_file</span> <span class="ow">in</span> <span class="n">py_files</span><span class="p">:</span>
</span><span id="__span-0-695"><a id="__codelineno-0-695" name="__codelineno-0-695"></a>                    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">py_file</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="__span-0-696"><a id="__codelineno-0-696" name="__codelineno-0-696"></a>                        <span class="n">content</span> <span class="o">=</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
</span><span id="__span-0-697"><a id="__codelineno-0-697" name="__codelineno-0-697"></a>
</span><span id="__span-0-698"><a id="__codelineno-0-698" name="__codelineno-0-698"></a>                    <span class="c1"># Basic syntax check</span>
</span><span id="__span-0-699"><a id="__codelineno-0-699" name="__codelineno-0-699"></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="__span-0-700"><a id="__codelineno-0-700" name="__codelineno-0-700"></a>                        <span class="nb">compile</span><span class="p">(</span><span class="n">content</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">py_file</span><span class="p">),</span> <span class="s2">&quot;exec&quot;</span><span class="p">)</span>
</span><span id="__span-0-701"><a id="__codelineno-0-701" name="__codelineno-0-701"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;✅ </span><span class="si">{</span><span class="n">py_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2"> has valid Python syntax&quot;</span><span class="p">)</span>
</span><span id="__span-0-702"><a id="__codelineno-0-702" name="__codelineno-0-702"></a>                    <span class="k">except</span> <span class="ne">SyntaxError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-703"><a id="__codelineno-0-703" name="__codelineno-0-703"></a>                        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;❌ </span><span class="si">{</span><span class="n">py_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2"> has syntax error: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-704"><a id="__codelineno-0-704" name="__codelineno-0-704"></a>                        <span class="n">issues</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="__span-0-705"><a id="__codelineno-0-705" name="__codelineno-0-705"></a>
</span><span id="__span-0-706"><a id="__codelineno-0-706" name="__codelineno-0-706"></a>            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="__span-0-707"><a id="__codelineno-0-707" name="__codelineno-0-707"></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Could not perform syntax check: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="__span-0-708"><a id="__codelineno-0-708" name="__codelineno-0-708"></a>
</span><span id="__span-0-709"><a id="__codelineno-0-709" name="__codelineno-0-709"></a>        <span class="k">if</span> <span class="n">issues</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="__span-0-710"><a id="__codelineno-0-710" name="__codelineno-0-710"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;🎉 Plugin &#39;</span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39; validation passed!&quot;</span><span class="p">)</span>
</span><span id="__span-0-711"><a id="__codelineno-0-711" name="__codelineno-0-711"></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-712"><a id="__codelineno-0-712" name="__codelineno-0-712"></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Plugin &#39;</span><span class="si">{</span><span class="n">plugin_dir</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39; has </span><span class="si">{</span><span class="n">issues</span><span class="si">}</span><span class="s2"> issue(s)&quot;</span><span class="p">)</span>
</span><span id="__span-0-713"><a id="__codelineno-0-713" name="__codelineno-0-713"></a>
</span><span id="__span-0-714"><a id="__codelineno-0-714" name="__codelineno-0-714"></a>        <span class="n">total_issues</span> <span class="o">+=</span> <span class="n">issues</span>
</span><span id="__span-0-715"><a id="__codelineno-0-715" name="__codelineno-0-715"></a>
</span><span id="__span-0-716"><a id="__codelineno-0-716" name="__codelineno-0-716"></a>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">=== Validation Summary ===&quot;</span><span class="p">)</span>
</span><span id="__span-0-717"><a id="__codelineno-0-717" name="__codelineno-0-717"></a>    <span class="k">if</span> <span class="n">total_issues</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="__span-0-718"><a id="__codelineno-0-718" name="__codelineno-0-718"></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🎉 All plugins passed validation!&quot;</span><span class="p">)</span>
</span><span id="__span-0-719"><a id="__codelineno-0-719" name="__codelineno-0-719"></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="__span-0-720"><a id="__codelineno-0-720" name="__codelineno-0-720"></a>        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;⚠️  Found </span><span class="si">{</span><span class="n">total_issues</span><span class="si">}</span><span class="s2"> total issue(s) across all plugins&quot;</span><span class="p">)</span>
</span><span id="__span-0-721"><a id="__codelineno-0-721" name="__codelineno-0-721"></a>
</span><span id="__span-0-722"><a id="__codelineno-0-722" name="__codelineno-0-722"></a>    <span class="k">return</span> <span class="n">total_issues</span> <span class="o">==</span> <span class="mi">0</span>
</span></code></pre></div></td></tr></table></div>
            </details>
    </div>

</div>



  </div>

    </div>

</div>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2024 Serv Contributors
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/your-org/serv" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
    
    
    
    
      
      
    
    <a href="https://pypi.org/project/getserving/" target="_blank" rel="noopener" title="pypi.org" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.8 200.5c-7.7-30.9-22.3-54.2-53.4-54.2h-40.1v47.4c0 36.8-31.2 67.8-66.8 67.8H172.7c-29.2 0-53.4 25-53.4 54.3v101.8c0 29 25.2 46 53.4 54.3 33.8 9.9 66.3 11.7 106.8 0 26.9-7.8 53.4-23.5 53.4-54.3v-40.7H226.2v-13.6h160.2c31.1 0 42.6-21.7 53.4-54.2 11.2-33.5 10.7-65.7 0-108.6M286.2 404c11.1 0 20.1 9.1 20.1 20.3 0 11.3-9 20.4-20.1 20.4-11 0-20.1-9.2-20.1-20.4.1-11.3 9.1-20.3 20.1-20.3M167.8 248.1h106.8c29.7 0 53.4-24.5 53.4-54.3V91.9c0-29-24.4-50.7-53.4-55.6-35.8-5.9-74.7-5.6-106.8.1-45.2 8-53.4 24.7-53.4 55.6v40.7h106.9v13.6h-147c-31.1 0-58.3 18.7-66.8 54.2-9.8 40.7-10.2 66.1 0 108.6 7.6 31.6 25.7 54.2 56.8 54.2H101v-48.8c0-35.3 30.5-66.4 66.8-66.4m-6.7-142.6c-11.1 0-20.1-9.1-20.1-20.3.1-11.3 9-20.4 20.1-20.4 11 0 20.1 9.2 20.1 20.4s-9 20.3-20.1 20.3"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../../../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>
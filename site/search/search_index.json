{"config": {"lang": ["en"], "separator": "[\\s\\-]+", "pipeline": ["stop<PERSON>ordFilter"]}, "docs": [{"location": "", "title": "Serv: Your Next-Generation ASGI Web Framework 🚀", "text": "<p>Pre-Release Software</p> <p>Serv is currently in a pre-release state and is NOT recommended for production use at this time. APIs are subject to change.</p> <p>Tired of boilerplate? Craving flexibility? Say hello to Serv!</p> <p>Serv is a powerful and intuitive ASGI web framework for Python, designed for ultimate extensibility while being opinionated only when necessary. It aims to make building web applications and APIs a breeze, even allowing you to construct entire sites with out-of-the-box plugins, minimizing the need to write custom code.</p>"}, {"location": "#key-features", "title": "✨ Key Features", "text": "<ul> <li>🚀 ASGI Native: Built from the ground up for asynchronous Python</li> <li>🔧 Extensible &amp; Minimally Opinionated: Designed for flexibility, providing guidance where it counts</li> <li>⚡ Codeless Site Building: Includes out-of-the-box plugins to get sites up and running quickly</li> <li>💉 Dependency Injection: Leverages <code>bevy</code> for clean, testable code</li> <li>🔌 Plugin Architecture: Easily extend and customize framework behavior beyond the defaults</li> <li>🛡️ Middleware Support: Integrate custom processing steps into the request/response lifecycle</li> <li>🗺️ Flexible Routing: Define routes with ease using functions or classes</li> <li>🚨 Comprehensive Error Handling: Robust mechanisms for managing exceptions</li> <li>📡 Event System: Emit and listen to events throughout the application lifecycle</li> </ul>"}, {"location": "#quick-example", "title": "Quick Example", "text": "<p>Here's a simple \"Hello World\" application in Serv:</p> <pre><code>from serv import App\nfrom serv.responses import ResponseBuilder\nfrom serv.plugins import Plugin\nfrom serv.plugins.routing import Router\nfrom bevy import dependency\n\nasync def hello_world(response: ResponseBuilder = dependency()):\n    response.content_type(\"text/plain\")\n    response.body(\"Hello, World!\")\n\nclass HelloPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/\", hello_world)\n\napp = App()\napp.add_plugin(HelloPlugin())\n\nif __name__ == \"__main__\":\n    import uvicorn\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n</code></pre>"}, {"location": "#architecture-overview", "title": "Architecture Overview", "text": "<p>Serv is built around several core concepts:</p>"}, {"location": "#plugin-system", "title": "🏗️ Plugin System", "text": "<p>Everything in Serv is a plugin. Routes, middleware, and even core functionality are implemented as plugins, making the framework incredibly modular and extensible.</p>"}, {"location": "#dependency-injection", "title": "💉 Dependency Injection", "text": "<p>Using the <code>bevy</code> library, Serv provides powerful dependency injection capabilities that make your code clean, testable, and maintainable.</p>"}, {"location": "#event-driven", "title": "🔄 Event-Driven", "text": "<p>Serv uses an event system that allows plugins to respond to application lifecycle events, enabling loose coupling between components.</p>"}, {"location": "#flexible-routing", "title": "🛣️ Flexible Routing", "text": "<p>Support for both functional and class-based routing, with automatic parameter injection and multiple response types.</p>"}, {"location": "#getting-started", "title": "Getting Started", "text": "<p>Ready to dive in? Check out our comprehensive guides:</p> <ul> <li>Installation - Get Serv installed and ready to go</li> <li>Quick Start - Build your first app in minutes</li> <li>Your First App - A detailed walkthrough of creating a complete application</li> <li>Configuration - Learn how to configure Serv for your needs</li> </ul>"}, {"location": "#community-support", "title": "Community &amp; Support", "text": "<ul> <li>📦 PyPI Package: <code>getserving</code></li> <li>🐛 Issues: Report bugs and request features on GitHub</li> <li>💬 Discussions: Join the community discussions</li> </ul>"}, {"location": "#license", "title": "License", "text": "<p>Serv is licensed under the MIT License. </p>"}, {"location": "cli-reference/", "title": "CLI Reference", "text": "<p>The Serv CLI provides a comprehensive set of commands for managing your web applications, plugins, and development workflow. This reference covers all available commands with detailed examples and usage patterns.</p>"}, {"location": "cli-reference/#installation-and-setup", "title": "Installation and Setup", "text": "<p>The Serv CLI is available when you install the Serv framework:</p> <pre><code>pip install getserving\n</code></pre> <p>Verify the installation:</p> <pre><code>serv --version\n</code></pre>"}, {"location": "cli-reference/#global-options", "title": "Global Options", "text": "<p>All Serv commands support these global options:</p> Option Description Example <code>--version</code> Show version information <code>serv --version</code> <code>--debug</code> Enable debug logging <code>serv --debug launch</code> <code>--app</code>, <code>-a</code> Custom application class <code>serv -a myapp.core:CustomApp launch</code> <code>--config</code>, <code>-c</code> Path to config file <code>serv -c config/prod.yaml launch</code> <code>--plugin-dirs</code> Plugin directory path <code>serv --plugin-dirs ./custom-plugins launch</code>"}, {"location": "cli-reference/#application-management", "title": "Application Management", "text": ""}, {"location": "cli-reference/#development-server", "title": "Development Server", "text": ""}, {"location": "cli-reference/#serv-launch", "title": "<code>serv launch</code>", "text": "<p>Launch the production Serv application server.</p> <p>Usage: <pre><code>serv launch [--host HOST] [--port PORT] [--reload] [--workers N] [--factory] [--dry-run] [--dev]\n</code></pre></p> <p>Options: - <code>--host</code>: Bind socket to this host (default: 127.0.0.1) - <code>--port</code>, <code>-p</code>: Bind socket to this port (default: 8000) - <code>--reload</code>: Enable auto-reload - <code>--workers</code>, <code>-w</code>: Number of worker processes (default: 1) - <code>--factory</code>: Treat app as factory function - <code>--dry-run</code>: Load app but don't start server - <code>--dev</code>: Enable development mode</p> <p>Examples:</p> <pre><code># Basic launch\nserv launch\n\n# Custom host and port\nserv launch --host 0.0.0.0 --port 3000\n\n# Production with multiple workers\nserv launch --workers 4 --host 0.0.0.0 --port 8000\n\n# Development mode with auto-reload\nserv launch --dev --reload\n\n# Dry run to test configuration\nserv launch --dry-run\n</code></pre>"}, {"location": "cli-reference/#serv-dev", "title": "<code>serv dev</code>", "text": "<p>Start an enhanced development server with debugging features.</p> <p>Usage: <pre><code>serv dev [--host HOST] [--port PORT] [--no-reload] [--workers N]\n</code></pre></p> <p>Options: - <code>--host</code>: Bind socket to this host (default: 127.0.0.1) - <code>--port</code>, <code>-p</code>: Bind socket to this port (default: 8000) - <code>--no-reload</code>: Disable auto-reload (enabled by default) - <code>--workers</code>, <code>-w</code>: Number of worker processes (default: 1)</p> <p>Examples:</p> <pre><code># Start development server\nserv dev\n\n# Custom port with auto-reload\nserv dev --port 3000\n\n# Disable auto-reload\nserv dev --no-reload\n\n# Development server on all interfaces\nserv dev --host 0.0.0.0\n</code></pre> <p>Development features: - 🔄 Auto-reload enabled by default - 📝 Enhanced error reporting - 🐛 Debug logging - ⚡ Fast restart on file changes</p>"}, {"location": "cli-reference/#testing", "title": "Testing", "text": ""}, {"location": "cli-reference/#serv-test", "title": "<code>serv test</code>", "text": "<p>Run tests for your application and plugins.</p> <p>Usage: <pre><code>serv test [--plugins] [--e2e] [--coverage] [--verbose] [test_path]\n</code></pre></p> <p>Options: - <code>--plugins</code>: Run plugin tests only - <code>--e2e</code>: Run end-to-end tests only - <code>--coverage</code>: Generate coverage report - <code>--verbose</code>, <code>-v</code>: Verbose test output - <code>test_path</code>: Specific test file or directory</p> <p>Examples:</p> <pre><code># Run all tests\nserv test\n\n# Run only plugin tests\nserv test --plugins\n\n# Run only e2e tests\nserv test --e2e\n\n# Run with coverage report\nserv test --coverage\n\n# Run specific test file\nserv test tests/test_auth.py\n\n# Verbose output with coverage\nserv test --verbose --coverage\n</code></pre> <p>Example output: <pre><code>🧪 Running tests...\n🔍 Running all tests\nRunning: pytest tests/\n📊 Coverage reporting enabled\n✅ All tests passed!\n\nCoverage Report:\nName                 Stmts   Miss  Cover\n----------------------------------------\nserv/app.py            45      2    96%\nplugins/auth.py        23      0   100%\n----------------------------------------\nTOTAL                  68      2    97%\n</code></pre></p>"}, {"location": "cli-reference/#serv-shell", "title": "<code>serv shell</code>", "text": "<p>Start an interactive Python shell with your application context loaded.</p> <p>Usage: <pre><code>serv shell [--ipython] [--no-startup]\n</code></pre></p> <p>Options: - <code>--ipython</code>: Use IPython if available - <code>--no-startup</code>: Skip loading app context</p> <p>Examples:</p> <pre><code># Start shell with app context\nserv shell\n\n# Use IPython interface\nserv shell --ipython\n\n# Basic shell without app context\nserv shell --no-startup\n</code></pre> <p>Available objects in shell: - <code>app</code>: Your Serv application instance - <code>serv</code>: The Serv module - <code>plugins</code>: List of loaded plugins - <code>Path</code>: pathlib.Path class - <code>yaml</code>: PyYAML module</p> <p>Example session: <pre><code>🐍 Starting interactive Python shell...\n📦 Loading Serv app context...\n🔌 Loaded 3 plugins into context\n✅ App context loaded successfully\nAvailable objects: app, serv, plugins, Path, yaml\n\n&gt;&gt;&gt; app.site_info\n{'name': 'My Awesome Website', 'description': 'A modern web application'}\n&gt;&gt;&gt; len(plugins)\n3\n&gt;&gt;&gt; plugins[0].name\n'User Management'\n</code></pre></p>"}, {"location": "cli-reference/#configuration-management", "title": "Configuration Management", "text": ""}, {"location": "cli-reference/#serv-config-show", "title": "<code>serv config show</code>", "text": "<p>Display your current configuration.</p> <p>Usage: <pre><code>serv config show [--format FORMAT]\n</code></pre></p> <p>Options: - <code>--format</code>: Output format (yaml, json)</p> <p>Examples:</p> <pre><code># Show config in YAML format (default)\nserv config show\n\n# Show config in JSON format\nserv config show --format json\n</code></pre> <p>Example output: <pre><code>📄 Configuration from 'serv.config.yaml':\n==================================================\nsite_info:\n  name: My Awesome Website\n  description: A modern web application\nplugins:\n- plugin: user_management\n- plugin: api_router\nmiddleware:\n- entry: cors_middleware\n</code></pre></p>"}, {"location": "cli-reference/#serv-config-validate", "title": "<code>serv config validate</code>", "text": "<p>Validate your configuration file syntax and structure.</p> <p>Usage: <pre><code>serv config validate\n</code></pre></p> <p>Example output: <pre><code>✅ Configuration file is valid YAML\n✅ Has required field: site_info\n✅ Has required field: plugins\n🎉 Configuration validation passed!\n</code></pre></p>"}, {"location": "cli-reference/#serv-config-get", "title": "<code>serv config get</code>", "text": "<p>Get specific configuration values using dot notation.</p> <p>Usage: <pre><code>serv config get &lt;key&gt;\n</code></pre></p> <p>Examples:</p> <pre><code># Get site name\nserv config get site_info.name\n\n# Get first plugin\nserv config get plugins.0.plugin\n\n# Get nested values\nserv config get database.connection.host\n</code></pre> <p>Example output: <pre><code>🔑 site_info.name: My Awesome Website\n</code></pre></p>"}, {"location": "cli-reference/#serv-config-set", "title": "<code>serv config set</code>", "text": "<p>Set configuration values with automatic type conversion.</p> <p>Usage: <pre><code>serv config set &lt;key&gt; &lt;value&gt; [--type TYPE]\n</code></pre></p> <p>Options: - <code>--type</code>: Value type (string, int, float, bool, list)</p> <p>Examples:</p> <pre><code># Set string value (default)\nserv config set site_info.name \"New Site Name\"\n\n# Set integer value\nserv config set server.port 3000 --type int\n\n# Set boolean value\nserv config set debug.enabled true --type bool\n\n# Set list value\nserv config set allowed_hosts \"localhost,127.0.0.1,example.com\" --type list\n\n# Set nested configuration\nserv config set database.connection.timeout 30 --type int\n</code></pre>"}, {"location": "cli-reference/#plugin-management", "title": "Plugin Management", "text": ""}, {"location": "cli-reference/#serv-plugin-list", "title": "<code>serv plugin list</code>", "text": "<p>List available and enabled plugins.</p> <p>Usage: <pre><code>serv plugin list [--available]\n</code></pre></p> <p>Options: - <code>--available</code>: Show all available plugins (default shows enabled)</p> <p>Examples:</p> <pre><code># List enabled plugins\nserv plugin list\n\n# List all available plugins\nserv plugin list --available\n</code></pre> <p>Example output: <pre><code>Enabled plugins (2):\n  • User Management (v1.0.0) [user_management]\n  • API Router (v2.1.0) [api_router] (with config)\n\nAvailable plugins (4):\n  • User Management (v1.0.0) [user_management]\n    User authentication and management system\n  • API Router (v2.1.0) [api_router]\n    RESTful API routing and middleware\n  • Blog Engine (v1.5.0) [blog_engine]\n    Simple blog functionality\n  • Admin Panel (v0.9.0) [admin_panel]\n    Administrative interface\n</code></pre></p>"}, {"location": "cli-reference/#serv-plugin-enable", "title": "<code>serv plugin enable</code>", "text": "<p>Enable a plugin in your application.</p> <p>Usage: <pre><code>serv plugin enable &lt;plugin_identifier&gt;\n</code></pre></p> <p>Examples:</p> <pre><code># Enable by directory name\nserv plugin enable user_management\n\n# Enable plugin with different name\nserv plugin enable blog_engine\n</code></pre> <p>Example output: <pre><code>Plugin 'user_management' enabled successfully.\nHuman name: User Management\n</code></pre></p>"}, {"location": "cli-reference/#serv-plugin-disable", "title": "<code>serv plugin disable</code>", "text": "<p>Disable a plugin in your application.</p> <p>Usage: <pre><code>serv plugin disable &lt;plugin_identifier&gt;\n</code></pre></p> <p>Examples:</p> <pre><code># Disable by directory name\nserv plugin disable user_management\n\n# Disable plugin with different name\nserv plugin disable blog_engine\n</code></pre>"}, {"location": "cli-reference/#serv-plugin-validate", "title": "<code>serv plugin validate</code>", "text": "<p>Validate plugin structure and configuration.</p> <p>Usage: <pre><code>serv plugin validate [plugin_identifier] [--all]\n</code></pre></p> <p>Options: - <code>--all</code>: Validate all plugins</p> <p>Examples:</p> <pre><code># Validate all plugins\nserv plugin validate\n\n# Validate specific plugin\nserv plugin validate user_management\n\n# Explicitly validate all\nserv plugin validate --all\n</code></pre> <p>Example output: <pre><code>=== Validating 2 Plugin(s) ===\n\n🔍 Validating plugin: user_management\n✅ plugin.yaml is valid YAML\n✅ Has required field: name\n✅ Has required field: version\n✅ Has recommended field: description\n✅ Has recommended field: author\n✅ Has __init__.py\n✅ Found 3 Python file(s)\n✅ Has main plugin file: user_management.py\n✅ user_management.py has valid Python syntax\n🎉 Plugin 'user_management' validation passed!\n\n=== Validation Summary ===\n🎉 All plugins passed validation!\n</code></pre></p>"}, {"location": "cli-reference/#project-and-plugin-development", "title": "Project and Plugin Development", "text": ""}, {"location": "cli-reference/#serv-create-app", "title": "<code>serv create app</code>", "text": "<p>Initialize a new Serv project with configuration files.</p> <p>Usage: <pre><code>serv create app [--force] [--non-interactive]\n</code></pre></p> <p>Options: - <code>--force</code>: Overwrite existing configuration files - <code>--non-interactive</code>: Use default values without prompts</p> <p>Examples:</p> <pre><code># Interactive initialization\nserv create app\n\n# Force overwrite existing config\nserv create app --force\n\n# Non-interactive with defaults (useful for scripts)\nserv create app --non-interactive --force\n</code></pre> <p>Interactive prompts: <pre><code>Enter site name [My Serv Site]: My Awesome Website\nEnter site description [A new website powered by Serv]: A modern web application\n</code></pre></p> <p>Generated files: - <code>serv.config.yaml</code> - Main configuration file</p>"}, {"location": "cli-reference/#serv-create-plugin", "title": "<code>serv create plugin</code>", "text": "<p>Create a new plugin with proper structure.</p> <p>Usage: <pre><code>serv create plugin --name NAME [--force] [--non-interactive]\n</code></pre></p> <p>Options: - <code>--name</code>: Name of the plugin (required) - <code>--force</code>: Overwrite existing plugin - <code>--non-interactive</code>: Use default values</p> <p>Examples:</p> <pre><code># Interactive plugin creation\nserv create plugin --name \"User Authentication\"\n\n# Non-interactive with defaults\nserv create plugin --name \"Blog Engine\" --non-interactive\n\n# Force overwrite existing\nserv create plugin --name \"API Router\" --force\n</code></pre> <p>Interactive prompts: <pre><code>Author [Your Name]: John <PERSON>\nDescription [A cool Serv plugin.]: User authentication and management\nVersion [0.1.0]: 1.0.0\n</code></pre></p> <p>Generated structure: <pre><code>plugins/\n└── user_authentication/\n    ├── __init__.py\n    ├── plugin.yaml\n    └── user_authentication.py\n</code></pre></p>"}, {"location": "cli-reference/#serv-create-route", "title": "<code>serv create route</code>", "text": "<p>Create a new route handler in a plugin.</p> <p>Usage: <pre><code>serv create route --name NAME [--path PATH] [--router ROUTER] [--plugin PLUGIN] [--force]\n</code></pre></p> <p>Options: - <code>--name</code>: Name of the route (required) - <code>--path</code>: URL path for the route - <code>--router</code>: Router name to add the route to - <code>--plugin</code>: Plugin to add the route to (auto-detected if not provided) - <code>--force</code>: Overwrite existing files</p> <p>Examples:</p> <pre><code># Basic route creation (interactive)\nserv create route --name user_profile\n\n# Specify everything explicitly\nserv create route --name user_profile \\\n  --path \"/users/{id}/profile\" \\\n  --router api_router \\\n  --plugin user_management\n\n# Create API endpoint\nserv create route --name create_post \\\n  --path \"/api/v1/posts\" \\\n  --router api_router\n\n# Admin route\nserv create route --name admin_dashboard \\\n  --path \"/admin/dashboard\" \\\n  --router admin_router\n</code></pre> <p>Interactive prompts: <pre><code>Route path [/user_profile]: /users/{id}/profile\nExisting routers:\n  1. api_router\n  2. admin_router\n  3. Create new router\nSelect router (name or number) [1]: 1\n</code></pre></p> <p>Generated plugin.yaml update: <pre><code>routers:\n- name: api_router\n  routes:\n  - path: /users/{id}/profile\n    handler: route_user_profile:UserProfile\n</code></pre></p>"}, {"location": "cli-reference/#serv-create-entrypoint", "title": "<code>serv create entrypoint</code>", "text": "<p>Create a new plugin entrypoint.</p> <p>Usage: <pre><code>serv create entrypoint --name NAME [--plugin PLUGIN] [--force]\n</code></pre></p> <p>Examples:</p> <pre><code># Create entrypoint\nserv create entrypoint --name admin_auth --plugin user_management\n\n# Auto-detect plugin\nserv create entrypoint --name email_sender\n</code></pre>"}, {"location": "cli-reference/#serv-create-middleware", "title": "<code>serv create middleware</code>", "text": "<p>Create a new middleware component.</p> <p>Usage: <pre><code>serv create middleware --name NAME [--plugin PLUGIN] [--force]\n</code></pre></p> <p>Examples:</p> <pre><code># Create middleware\nserv create middleware --name auth_check --plugin user_management\n\n# Rate limiting middleware\nserv create middleware --name rate_limiter --plugin security\n</code></pre>"}, {"location": "cli-reference/#advanced-usage-patterns", "title": "Advanced Usage Patterns", "text": ""}, {"location": "cli-reference/#multi-environment-configuration", "title": "Multi-Environment Configuration", "text": "<pre><code># Development\nserv -c config/dev.yaml dev\n\n# Staging\nserv -c config/staging.yaml launch --host 0.0.0.0\n\n# Production\nserv -c config/prod.yaml launch --workers 4 --host 0.0.0.0\n</code></pre>"}, {"location": "cli-reference/#custom-application-classes", "title": "Custom Application Classes", "text": "<pre><code># Use custom app class\nserv -a myproject.app:CustomApp launch\n\n# With custom config\nserv -a myproject.app:CustomApp -c custom.yaml dev\n</code></pre>"}, {"location": "cli-reference/#plugin-development-workflow", "title": "Plugin Development Workflow", "text": "<pre><code># 1. Create new project (if needed)\nserv create app\n\n# 2. Create plugin\nserv create plugin --name \"My Feature\"\n\n# 3. Add routes\nserv create route --name feature_api --path \"/api/feature\" --router api_router\n\n# 4. Add middleware\nserv create middleware --name feature_auth\n\n# 5. Validate plugin\nserv plugin validate my_feature\n\n# 6. Enable plugin\nserv plugin enable my_feature\n\n# 7. Test\nserv test --plugins\n\n# 8. Start development server\nserv dev\n</code></pre>"}, {"location": "cli-reference/#testing-workflow", "title": "Testing Workflow", "text": "<pre><code># Run tests during development\nserv test --verbose\n\n# Check coverage\nserv test --coverage\n\n# Test specific components\nserv test tests/test_auth.py --verbose\n\n# Run e2e tests before deployment\nserv test --e2e\n</code></pre>"}, {"location": "cli-reference/#configuration-management_1", "title": "Configuration Management", "text": "<pre><code># Check current config\nserv config show\n\n# Validate before deployment\nserv config validate\n\n# Update settings\nserv config set debug.enabled false --type bool\nserv config set server.workers 4 --type int\n\n# Verify changes\nserv config get debug.enabled\nserv config get server.workers\n</code></pre>"}, {"location": "cli-reference/#troubleshooting", "title": "Troubleshooting", "text": ""}, {"location": "cli-reference/#common-issues", "title": "Common Issues", "text": "<p>Configuration not found: <pre><code># Check if config exists\nserv config validate\n\n# Create new config\nserv create app\n</code></pre></p> <p>Plugin not loading: <pre><code># Validate plugin structure\nserv plugin validate my_plugin\n\n# Check if plugin is enabled\nserv plugin list\n\n# Enable plugin\nserv plugin enable my_plugin\n</code></pre></p> <p>Application health check: <pre><code># Check configuration\nserv config validate\n\n# Check plugins\nserv plugin validate\n\n# Check if app can be loaded\nserv launch --dry-run\n</code></pre></p>"}, {"location": "cli-reference/#debug-mode", "title": "Debug Mode", "text": "<p>Enable debug logging for detailed information:</p> <pre><code>serv --debug dev\nserv --debug config validate\nserv --debug plugin validate\n</code></pre>"}, {"location": "cli-reference/#getting-help", "title": "Getting Help", "text": "<pre><code># General help\nserv --help\n\n# Command-specific help\nserv dev --help\nserv create route --help\nserv config set --help\n</code></pre>"}, {"location": "cli-reference/#environment-variables", "title": "Environment Variables", "text": "<p>Serv CLI respects these environment variables:</p> Variable Description Default <code>SERV_CONFIG</code> Default config file path <code>serv.config.yaml</code> <code>SERV_PLUGIN_DIRS</code> Default plugin directories <code>./plugins</code> <code>SERV_DEBUG</code> Enable debug mode <code>false</code> <p>Example: <pre><code>export SERV_CONFIG=config/production.yaml\nexport SERV_DEBUG=true\nserv launch\n</code></pre></p>"}, {"location": "cli-reference/#integration-with-cicd", "title": "Integration with CI/CD", "text": ""}, {"location": "cli-reference/#github-actions-example", "title": "GitHub Actions Example", "text": "<pre><code>name: Serv Application CI\non: [push, pull_request]\n\njobs:\n  test:\n    runs-on: ubuntu-latest\n    steps:\n    - uses: actions/checkout@v2\n    - name: Set up Python\n      uses: actions/setup-python@v2\n      with:\n        python-version: 3.9\n\n    - name: Install dependencies\n      run: |\n        pip install serv\n        pip install -r requirements.txt\n\n    - name: Validate configuration\n      run: serv config validate\n\n    - name: Check application health\n      run: serv app check\n\n    - name: Validate plugins\n      run: serv plugin validate\n\n    - name: Run tests with coverage\n      run: serv test --coverage\n\n    - name: Test application startup\n      run: serv launch --dry-run\n</code></pre>"}, {"location": "cli-reference/#docker-integration", "title": "Docker Integration", "text": "<pre><code>FROM python:3.9-slim\n\nWORKDIR /app\nCOPY . .\n\nRUN pip install serv\nRUN pip install -r requirements.txt\n\n# Validate configuration during build\nRUN serv config validate\nRUN serv app check\n\nEXPOSE 8000\nCMD [\"serv\", \"launch\", \"--host\", \"0.0.0.0\", \"--workers\", \"4\"]\n</code></pre> <p>This comprehensive CLI reference provides everything you need to effectively use Serv's command-line interface for development, testing, and deployment of your web applications. </p>"}, {"location": "getting-started/configuration/", "title": "Configuration", "text": "<p>Serv uses a flexible configuration system based on YAML files. This guide covers how to configure your Serv applications effectively.</p>"}, {"location": "getting-started/configuration/#configuration-files", "title": "Configuration Files", "text": ""}, {"location": "getting-started/configuration/#main-configuration-file", "title": "Main Configuration File", "text": "<p>The main configuration file is typically named <code>serv.config.yaml</code> and placed in your project root:</p> <pre><code># serv.config.yaml\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: \"your-secret-key-here\"\n      token_expiry: 3600\n  - plugin: blog\n    settings:\n      posts_per_page: 10\n      allow_comments: true\n  - entry: external_package.plugin:ExternalPlugin\n    config:\n      api_url: \"https://api.example.com\"\n\nmiddleware:\n  - entry: my_middleware:LoggingMiddleware\n    config:\n      log_level: \"INFO\"\n\nsettings:\n  debug: false\n  host: \"0.0.0.0\"\n  port: 8000\n</code></pre>"}, {"location": "getting-started/configuration/#loading-configuration", "title": "Loading Configuration", "text": "<p>Load configuration when creating your app:</p> <pre><code>from serv import App\n\n# Load from default location (./serv.config.yaml)\napp = App()\n\n# Load from custom location\napp = App(config=\"./config/production.yaml\")\n\n# Load from multiple files (later files override earlier ones)\napp = App(config=[\"./base.yaml\", \"./environment.yaml\"])\n</code></pre>"}, {"location": "getting-started/configuration/#plugin-configuration", "title": "Plugin Configuration", "text": ""}, {"location": "getting-started/configuration/#plugin-settings", "title": "Plugin <PERSON>s", "text": "<p>Configure plugins in the <code>plugins</code> section:</p> <pre><code>plugins:\n  - plugin: auth  # Plugin directory name\n    settings:\n      secret_key: \"super-secret-key\"\n      algorithm: \"HS256\"\n      token_expiry: 86400  # 24 hours\n\n  - plugin: database\n    settings:\n      url: \"postgresql://user:pass@localhost/db\"\n      pool_size: 10\n      echo: false\n</code></pre>"}, {"location": "getting-started/configuration/#external-plugin-configuration", "title": "External Plugin Configuration", "text": "<p>Load plugins from external packages:</p> <pre><code>plugins:\n  - entry: \"my_package.auth:AuthPlugin\"\n    config:\n      provider: \"oauth2\"\n      client_id: \"your-client-id\"\n\n  - entry: \"third_party_plugin:MainPlugin\"\n    config:\n      api_key: \"your-api-key\"\n</code></pre>"}, {"location": "getting-started/configuration/#plugin-specific-configuration-files", "title": "Plugin-Specific Configuration Files", "text": "<p>Plugins can have their own <code>plugin.yaml</code> files with default settings:</p> <pre><code># plugins/auth/plugin.yaml\nname: Authentication Plugin\ndescription: Provides user authentication\nversion: 1.0.0\nauthor: Your Name\nentry: auth.main:AuthPlugin\n\nsettings:\n  secret_key: \"default-secret\"\n  algorithm: \"HS256\"\n  token_expiry: 3600\n  require_email_verification: true\n</code></pre> <p>Application configuration can override these defaults:</p> <pre><code># serv.config.yaml\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: \"production-secret\"  # Overrides default\n      token_expiry: 7200               # Overrides default\n      # require_email_verification uses default (true)\n</code></pre>"}, {"location": "getting-started/configuration/#environment-specific-configuration", "title": "Environment-Specific Configuration", "text": ""}, {"location": "getting-started/configuration/#environment-variables", "title": "Environment Variables", "text": "<p>Use environment variables for sensitive or environment-specific settings:</p> <pre><code># serv.config.yaml\nplugins:\n  - plugin: database\n    settings:\n      url: ${DATABASE_URL}\n\n  - plugin: auth\n    settings:\n      secret_key: ${JWT_SECRET_KEY}\n\nsettings:\n  debug: ${DEBUG:false}  # Default to false if not set\n  port: ${PORT:8000}     # Default to 8000 if not set\n</code></pre>"}, {"location": "getting-started/configuration/#multiple-configuration-files", "title": "Multiple Configuration Files", "text": "<p>Organize configuration by environment:</p> <pre><code># base.yaml - Common settings\nplugins:\n  - plugin: auth\n    settings:\n      algorithm: \"HS256\"\n      token_expiry: 3600\n\nsettings:\n  host: \"0.0.0.0\"\n</code></pre> <pre><code># development.yaml - Development overrides\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: \"dev-secret\"\n\nsettings:\n  debug: true\n  port: 8000\n</code></pre> <pre><code># production.yaml - Production overrides\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: ${JWT_SECRET_KEY}\n\nsettings:\n  debug: false\n  port: ${PORT:80}\n</code></pre> <p>Load configuration based on environment:</p> <pre><code>import os\nfrom serv import App\n\nenv = os.getenv(\"ENVIRONMENT\", \"development\")\nconfig_files = [\"base.yaml\", f\"{env}.yaml\"]\n\napp = App(config=config_files)\n</code></pre>"}, {"location": "getting-started/configuration/#application-settings", "title": "Application Settings", "text": ""}, {"location": "getting-started/configuration/#core-settings", "title": "Core Settings", "text": "<p>Configure core application behavior:</p> <pre><code>settings:\n  # Server settings\n  host: \"0.0.0.0\"\n  port: 8000\n\n  # Development settings\n  debug: true\n  reload: true\n\n  # Security settings\n  allowed_hosts: [\"localhost\", \"127.0.0.1\", \"myapp.com\"]\n  cors_origins: [\"http://localhost:3000\"]\n\n  # Logging settings\n  log_level: \"INFO\"\n  log_format: \"%(asctime)s - %(name)s - %(levelname)s - %(message)s\"\n</code></pre>"}, {"location": "getting-started/configuration/#custom-settings", "title": "Custom Settings", "text": "<p>Add your own application-wide settings:</p> <pre><code>settings:\n  # Custom application settings\n  app_name: \"My Awesome App\"\n  version: \"1.0.0\"\n  max_upload_size: 10485760  # 10MB\n  cache_timeout: 300\n\n  # Feature flags\n  features:\n    user_registration: true\n    email_notifications: false\n    analytics: true\n</code></pre> <p>Access custom settings in your code:</p> <pre><code>from serv.config import get_config\n\nconfig = get_config()\napp_name = config.get('app_name', 'Default App')\nmax_upload = config.get('max_upload_size', 1048576)\n</code></pre>"}, {"location": "getting-started/configuration/#middleware-configuration", "title": "Middleware Configuration", "text": ""}, {"location": "getting-started/configuration/#global-middleware", "title": "Global Middleware", "text": "<p>Configure middleware that applies to all requests:</p> <pre><code>middleware:\n  - entry: \"serv.middleware.cors:CORSMiddleware\"\n    config:\n      allow_origins: [\"*\"]\n      allow_methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\"]\n      allow_headers: [\"*\"]\n\n  - entry: \"my_middleware:LoggingMiddleware\"\n    config:\n      log_requests: true\n      log_responses: false\n\n  - entry: \"my_middleware:RateLimitMiddleware\"\n    config:\n      requests_per_minute: 60\n      burst_size: 10\n</code></pre>"}, {"location": "getting-started/configuration/#plugin-provided-middleware", "title": "Plugin-Provided Middleware", "text": "<p>Plugins can register their own middleware:</p> <pre><code># In plugin.yaml\nmiddleware:\n  - entry: \"auth.middleware:AuthMiddleware\"\n    config:\n      exempt_paths: [\"/health\", \"/metrics\"]\n\n  - entry: \"auth.middleware:SessionMiddleware\"\n    config:\n      session_timeout: 1800\n</code></pre>"}, {"location": "getting-started/configuration/#configuration-validation", "title": "Configuration Validation", "text": ""}, {"location": "getting-started/configuration/#schema-validation", "title": "Schema Validation", "text": "<p>Define schemas to validate your configuration:</p> <pre><code>from serv.config import ConfigSchema\nfrom typing import Optional\n\nclass AuthPluginConfig(ConfigSchema):\n    secret_key: str\n    algorithm: str = \"HS256\"\n    token_expiry: int = 3600\n    require_email_verification: bool = True\n\nclass AuthPlugin(Plugin):\n    def __init__(self):\n        # Validate configuration against schema\n        self.config = AuthPluginConfig.from_config(self.get_config())\n\n    async def on_app_startup(self):\n        print(f\"Auth plugin starting with algorithm: {self.config.algorithm}\")\n</code></pre>"}, {"location": "getting-started/configuration/#required-settings", "title": "Required <PERSON><PERSON><PERSON>", "text": "<p>Mark settings as required:</p> <pre><code># plugin.yaml\nsettings:\n  secret_key: !required  # Must be provided\n  algorithm: \"HS256\"     # Has default\n  token_expiry: 3600     # Has default\n</code></pre>"}, {"location": "getting-started/configuration/#configuration-best-practices", "title": "Configuration Best Practices", "text": ""}, {"location": "getting-started/configuration/#1-use-environment-variables-for-secrets", "title": "1. Use Environment Variables for Secrets", "text": "<p>Never commit secrets to version control:</p> <pre><code># Good\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: ${JWT_SECRET_KEY}\n\n# Bad - secret in config file\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: \"super-secret-key-123\"\n</code></pre>"}, {"location": "getting-started/configuration/#2-provide-sensible-defaults", "title": "2. Provide Sensible Defaults", "text": "<p>Make your plugins work out of the box:</p> <pre><code># plugin.yaml\nsettings:\n  debug: false\n  timeout: 30\n  retries: 3\n  cache_enabled: true\n</code></pre>"}, {"location": "getting-started/configuration/#3-document-configuration-options", "title": "3. Document Configuration Options", "text": "<p>Document all configuration options:</p> <pre><code># plugin.yaml\nname: My Plugin\ndescription: Does awesome things\n\nsettings:\n  # Required: API key for external service\n  api_key: !required\n\n  # Optional: Request timeout in seconds (default: 30)\n  timeout: 30\n\n  # Optional: Number of retries on failure (default: 3)\n  retries: 3\n\n  # Optional: Enable caching (default: true)\n  cache_enabled: true\n</code></pre>"}, {"location": "getting-started/configuration/#4-validate-configuration-early", "title": "4. Validate Configuration Early", "text": "<p>Validate configuration at startup:</p> <pre><code>class MyPlugin(Plugin):\n    def __init__(self):\n        config = self.get_config()\n\n        # Validate required settings\n        if not config.get('api_key'):\n            raise ValueError(\"api_key is required\")\n\n        # Validate setting types and ranges\n        timeout = config.get('timeout', 30)\n        if not isinstance(timeout, int) or timeout &lt;= 0:\n            raise ValueError(\"timeout must be a positive integer\")\n\n        self.api_key = config['api_key']\n        self.timeout = timeout\n</code></pre>"}, {"location": "getting-started/configuration/#5-use-configuration-layers", "title": "5. Use Configuration Layers", "text": "<p>Organize configuration in layers:</p> <ol> <li>Plugin defaults (in <code>plugin.yaml</code>)</li> <li>Application config (in <code>serv.config.yaml</code>)</li> <li>Environment variables (for deployment-specific values)</li> <li>Command-line arguments (for runtime overrides)</li> </ol> <pre><code># Example of configuration precedence\nimport os\nfrom serv import App\n\n# 1. Start with plugin defaults\n# 2. Override with application config\napp = App(config=\"serv.config.yaml\")\n\n# 3. Override with environment variables (handled automatically)\n# 4. Override with command-line arguments\nif os.getenv(\"DEBUG\"):\n    app.config['debug'] = True\n</code></pre>"}, {"location": "getting-started/configuration/#dynamic-configuration", "title": "Dynamic Configuration", "text": ""}, {"location": "getting-started/configuration/#runtime-configuration-changes", "title": "Runtime Configuration Changes", "text": "<p>Some settings can be changed at runtime:</p> <pre><code>class ConfigurablePlugin(Plugin):\n    def __init__(self):\n        self.config = self.get_config()\n        self.debug = self.config.get('debug', False)\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        # Add debug routes only if debug is enabled\n        if self.debug:\n            router.add_route(\"/debug/config\", self.show_config)\n\n    async def show_config(self, response: ResponseBuilder = dependency()):\n        response.content_type(\"application/json\")\n        response.body(json.dumps(self.config, indent=2))\n\n    def update_config(self, new_config: dict):\n        \"\"\"Update configuration at runtime\"\"\"\n        self.config.update(new_config)\n        self.debug = self.config.get('debug', False)\n</code></pre>"}, {"location": "getting-started/configuration/#configuration-reloading", "title": "Configuration Reloading", "text": "<p>Implement configuration reloading for development:</p> <pre><code>import asyncio\nfrom watchdog.observers import Observer\nfrom watchdog.events import FileSystemEventHandler\n\nclass ConfigReloader(FileSystemEventHandler):\n    def __init__(self, app: App):\n        self.app = app\n\n    def on_modified(self, event):\n        if event.src_path.endswith('.yaml'):\n            print(\"Configuration changed, reloading...\")\n            self.app.reload_config()\n\n# In development mode\nif app.config.get('debug'):\n    observer = Observer()\n    observer.schedule(ConfigReloader(app), path='.', recursive=False)\n    observer.start()\n</code></pre>"}, {"location": "getting-started/configuration/#configuration-examples", "title": "Configuration Examples", "text": ""}, {"location": "getting-started/configuration/#complete-application-configuration", "title": "Complete Application Configuration", "text": "<pre><code># serv.config.yaml\nsettings:\n  app_name: \"My Blog App\"\n  debug: ${DEBUG:false}\n  host: ${HOST:0.0.0.0}\n  port: ${PORT:8000}\n\n  # Database settings\n  database_url: ${DATABASE_URL:sqlite:///app.db}\n\n  # Security settings\n  secret_key: ${SECRET_KEY}\n  allowed_hosts: \n    - \"localhost\"\n    - \"127.0.0.1\"\n    - ${DOMAIN:myapp.com}\n\n  # Feature flags\n  features:\n    user_registration: true\n    email_verification: ${EMAIL_VERIFICATION:false}\n    analytics: ${ANALYTICS:false}\n\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: ${JWT_SECRET_KEY}\n      token_expiry: ${TOKEN_EXPIRY:3600}\n\n  - plugin: blog\n    settings:\n      posts_per_page: ${POSTS_PER_PAGE:10}\n      allow_comments: ${ALLOW_COMMENTS:true}\n\n  - plugin: email\n    settings:\n      smtp_host: ${SMTP_HOST:localhost}\n      smtp_port: ${SMTP_PORT:587}\n      smtp_user: ${SMTP_USER}\n      smtp_password: ${SMTP_PASSWORD}\n\nmiddleware:\n  - entry: \"serv.middleware.cors:CORSMiddleware\"\n    config:\n      allow_origins: ${CORS_ORIGINS:[\"http://localhost:3000\"]}\n\n  - entry: \"my_middleware:RateLimitMiddleware\"\n    config:\n      requests_per_minute: ${RATE_LIMIT:60}\n</code></pre>"}, {"location": "getting-started/configuration/#plugin-configuration-template", "title": "Plugin Configuration Template", "text": "<pre><code># plugins/my_plugin/plugin.yaml\nname: My Plugin\ndescription: A sample plugin demonstrating configuration\nversion: 1.0.0\nauthor: Your Name\nentry: my_plugin.main:MyPlugin\n\n# Default settings (can be overridden in serv.config.yaml)\nsettings:\n  # Required settings (must be provided by user)\n  api_key: !required\n\n  # Optional settings with defaults\n  timeout: 30\n  retries: 3\n  debug: false\n\n  # Complex settings\n  cache:\n    enabled: true\n    ttl: 300\n    max_size: 1000\n\n  # List settings\n  allowed_ips:\n    - \"127.0.0.1\"\n    - \"::1\"\n\n# Additional entry points\nentry_points:\n  - entry: my_plugin.admin:AdminPlugin\n    config:\n      admin_path: \"/admin\"\n\n# Middleware provided by this plugin\nmiddleware:\n  - entry: my_plugin.middleware:SecurityMiddleware\n    config:\n      check_csrf: true\n      check_origin: true\n</code></pre>"}, {"location": "getting-started/configuration/#next-steps", "title": "Next Steps", "text": "<ul> <li>Plugins - Learn how to create and configure plugins</li> <li>Middleware - Understand middleware configuration</li> <li>Deployment - Configure for production deployment</li> <li>Testing - Test your configuration </li> </ul>"}, {"location": "getting-started/first-app/", "title": "Your First App", "text": "<p>In this tutorial, we'll build a complete blog application using Serv. You'll learn about routing, templates, forms, plugins, and more!</p>"}, {"location": "getting-started/first-app/#what-well-build", "title": "What We'll Build", "text": "<p>We're going to create a simple blog with the following features:</p> <ul> <li>Homepage listing all blog posts</li> <li>Individual post pages</li> <li>Admin interface to create new posts</li> <li>Form handling and validation</li> <li>Template rendering with Jinja2</li> <li>Plugin-based architecture</li> </ul>"}, {"location": "getting-started/first-app/#project-setup", "title": "Project Setup", "text": ""}, {"location": "getting-started/first-app/#1-create-the-project-structure", "title": "1. Create the Project Structure", "text": "<p>First, let's create our project directory:</p> <pre><code>mkdir serv-blog\ncd serv-blog\n</code></pre> <p>Create the following directory structure:</p> <pre><code>serv-blog/\n├── app.py\n├── serv.config.yaml\n├── templates/\n│   ├── base.html\n│   ├── index.html\n│   ├── post.html\n│   └── admin/\n│       └── create_post.html\n└── plugins/\n    └── blog/\n        ├── __init__.py\n        ├── main.py\n        ├── models.py\n        └── plugin.yaml\n</code></pre>"}, {"location": "getting-started/first-app/#2-install-dependencies", "title": "2. Install Dependencies", "text": "<pre><code>pip install getserving uvicorn\n</code></pre>"}, {"location": "getting-started/first-app/#building-the-application", "title": "Building the Application", "text": ""}, {"location": "getting-started/first-app/#1-create-the-data-models", "title": "1. Create the Data Models", "text": "<p>First, let's create simple data models for our blog posts. Create <code>plugins/blog/models.py</code>:</p> <pre><code>from dataclasses import dataclass\nfrom datetime import datetime\nfrom typing import List\n\n@dataclass\nclass BlogPost:\n    id: int\n    title: str\n    content: str\n    author: str\n    created_at: datetime\n\n    def __post_init__(self):\n        if isinstance(self.created_at, str):\n            self.created_at = datetime.fromisoformat(self.created_at)\n\nclass BlogStorage:\n    \"\"\"Simple in-memory storage for blog posts\"\"\"\n\n    def __init__(self):\n        self.posts: List[BlogPost] = []\n        self.next_id = 1\n\n        # Add some sample data\n        self.add_post(\"Welcome to Serv Blog\", \n                     \"This is your first blog post using Serv!\", \n                     \"Admin\")\n        self.add_post(\"Getting Started\", \n                     \"Learn how to build amazing web apps with Serv.\", \n                     \"Admin\")\n\n    def add_post(self, title: str, content: str, author: str) -&gt; BlogPost:\n        post = BlogPost(\n            id=self.next_id,\n            title=title,\n            content=content,\n            author=author,\n            created_at=datetime.now()\n        )\n        self.posts.append(post)\n        self.next_id += 1\n        return post\n\n    def get_all_posts(self) -&gt; List[BlogPost]:\n        return sorted(self.posts, key=lambda p: p.created_at, reverse=True)\n\n    def get_post_by_id(self, post_id: int) -&gt; BlogPost | None:\n        for post in self.posts:\n            if post.id == post_id:\n                return post\n        return None\n</code></pre>"}, {"location": "getting-started/first-app/#2-create-the-blog-plugin", "title": "2. <PERSON><PERSON> the Blog Plugin", "text": "<p>Now let's create the main blog plugin. Create <code>plugins/blog/main.py</code>:</p> <pre><code>from typing import Annotated\nfrom serv.plugins import Plugin\nfrom serv.plugins.routing import Router\nfrom serv.requests import Request\nfrom serv.responses import ResponseBuilder\nfrom serv.routes import Route, Form\nfrom bevy import dependency\nimport json\n\nfrom .models import BlogStorage, BlogPost\n\nclass BlogPlugin(Plugin):\n    def __init__(self):\n        self.storage = BlogStorage()\n\n    async def on_app_startup(self):\n        \"\"\"Initialize the blog plugin\"\"\"\n        print(\"Blog plugin started!\")\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        \"\"\"Register routes for each request\"\"\"\n        # Register our route handlers\n        router.add_route(\"/\", self.homepage)\n        router.add_route(\"/post/{post_id}\", self.view_post)\n        router.add_route(\"/admin\", AdminRoute(self.storage))\n        router.add_route(\"/api/posts\", self.api_posts)\n\n    async def homepage(self, response: ResponseBuilder = dependency()):\n        \"\"\"Homepage showing all blog posts\"\"\"\n        posts = self.storage.get_all_posts()\n\n        html = self._render_template(\"index.html\", {\n            \"title\": \"Serv Blog\",\n            \"posts\": posts\n        })\n\n        response.content_type(\"text/html\")\n        response.body(html)\n\n    async def view_post(self, post_id: str, response: ResponseBuilder = dependency()):\n        \"\"\"View a single blog post\"\"\"\n        try:\n            post_id_int = int(post_id)\n            post = self.storage.get_post_by_id(post_id_int)\n\n            if not post:\n                response.set_status(404)\n                response.content_type(\"text/html\")\n                response.body(\"&lt;h1&gt;Post Not Found&lt;/h1&gt;\")\n                return\n\n            html = self._render_template(\"post.html\", {\n                \"title\": post.title,\n                \"post\": post\n            })\n\n            response.content_type(\"text/html\")\n            response.body(html)\n\n        except ValueError:\n            response.set_status(400)\n            response.content_type(\"text/html\")\n            response.body(\"&lt;h1&gt;Invalid Post ID&lt;/h1&gt;\")\n\n    async def api_posts(self, response: ResponseBuilder = dependency()):\n        \"\"\"API endpoint returning posts as JSON\"\"\"\n        posts = self.storage.get_all_posts()\n        posts_data = [\n            {\n                \"id\": post.id,\n                \"title\": post.title,\n                \"content\": post.content,\n                \"author\": post.author,\n                \"created_at\": post.created_at.isoformat()\n            }\n            for post in posts\n        ]\n\n        response.content_type(\"application/json\")\n        response.body(json.dumps(posts_data, indent=2))\n\n    def _render_template(self, template_name: str, context: dict) -&gt; str:\n        \"\"\"Simple template rendering\"\"\"\n        # In a real app, you'd use Jinja2 or another template engine\n        # For now, we'll use simple string formatting\n\n        if template_name == \"index.html\":\n            posts_html = \"\"\n            for post in context[\"posts\"]:\n                posts_html += f\"\"\"\n                &lt;article class=\"post-preview\"&gt;\n                    &lt;h2&gt;&lt;a href=\"/post/{post.id}\"&gt;{post.title}&lt;/a&gt;&lt;/h2&gt;\n                    &lt;p class=\"meta\"&gt;By {post.author} on {post.created_at.strftime('%B %d, %Y')}&lt;/p&gt;\n                    &lt;p&gt;{post.content[:200]}...&lt;/p&gt;\n                &lt;/article&gt;\n                \"\"\"\n\n            return f\"\"\"\n            &lt;!DOCTYPE html&gt;\n            &lt;html&gt;\n            &lt;head&gt;\n                &lt;title&gt;{context['title']}&lt;/title&gt;\n                &lt;style&gt;\n                    body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}\n                    .post-preview {{ border-bottom: 1px solid #eee; padding: 20px 0; }}\n                    .meta {{ color: #666; font-size: 0.9em; }}\n                    a {{ color: #007bff; text-decoration: none; }}\n                    a:hover {{ text-decoration: underline; }}\n                    .nav {{ margin-bottom: 30px; }}\n                &lt;/style&gt;\n            &lt;/head&gt;\n            &lt;body&gt;\n                &lt;nav class=\"nav\"&gt;\n                    &lt;a href=\"/\"&gt;Home&lt;/a&gt; | \n                    &lt;a href=\"/admin\"&gt;Admin&lt;/a&gt; | \n                    &lt;a href=\"/api/posts\"&gt;API&lt;/a&gt;\n                &lt;/nav&gt;\n                &lt;h1&gt;{context['title']}&lt;/h1&gt;\n                {posts_html}\n            &lt;/body&gt;\n            &lt;/html&gt;\n            \"\"\"\n\n        elif template_name == \"post.html\":\n            post = context[\"post\"]\n            return f\"\"\"\n            &lt;!DOCTYPE html&gt;\n            &lt;html&gt;\n            &lt;head&gt;\n                &lt;title&gt;{context['title']}&lt;/title&gt;\n                &lt;style&gt;\n                    body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}\n                    .meta {{ color: #666; font-size: 0.9em; margin-bottom: 20px; }}\n                    .content {{ line-height: 1.6; }}\n                    a {{ color: #007bff; text-decoration: none; }}\n                    a:hover {{ text-decoration: underline; }}\n                    .nav {{ margin-bottom: 30px; }}\n                &lt;/style&gt;\n            &lt;/head&gt;\n            &lt;body&gt;\n                &lt;nav class=\"nav\"&gt;\n                    &lt;a href=\"/\"&gt;← Back to Home&lt;/a&gt;\n                &lt;/nav&gt;\n                &lt;article&gt;\n                    &lt;h1&gt;{post.title}&lt;/h1&gt;\n                    &lt;p class=\"meta\"&gt;By {post.author} on {post.created_at.strftime('%B %d, %Y')}&lt;/p&gt;\n                    &lt;div class=\"content\"&gt;\n                        {post.content.replace(chr(10), '&lt;br&gt;')}\n                    &lt;/div&gt;\n                &lt;/article&gt;\n            &lt;/body&gt;\n            &lt;/html&gt;\n            \"\"\"\n\n        return \"&lt;h1&gt;Template not found&lt;/h1&gt;\"\n\nclass CreatePostForm(Form):\n    title: str\n    content: str\n    author: str\n\nclass AdminRoute(Route):\n    def __init__(self, storage: BlogStorage):\n        self.storage = storage\n\n    async def show_admin_page(self, request: Request, response: ResponseBuilder = dependency()):\n        \"\"\"Show the admin page with create post form\"\"\"\n        html = \"\"\"\n        &lt;!DOCTYPE html&gt;\n        &lt;html&gt;\n        &lt;head&gt;\n            &lt;title&gt;Admin - Create Post&lt;/title&gt;\n            &lt;style&gt;\n                body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }\n                .form-group { margin-bottom: 15px; }\n                label { display: block; margin-bottom: 5px; font-weight: bold; }\n                input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }\n                textarea { height: 200px; resize: vertical; }\n                button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }\n                button:hover { background: #0056b3; }\n                .nav { margin-bottom: 30px; }\n                a { color: #007bff; text-decoration: none; }\n                a:hover { text-decoration: underline; }\n            &lt;/style&gt;\n        &lt;/head&gt;\n        &lt;body&gt;\n            &lt;nav class=\"nav\"&gt;\n                &lt;a href=\"/\"&gt;← Back to Home&lt;/a&gt;\n            &lt;/nav&gt;\n            &lt;h1&gt;Create New Post&lt;/h1&gt;\n            &lt;form method=\"POST\" action=\"/admin\"&gt;\n                &lt;div class=\"form-group\"&gt;\n                    &lt;label for=\"title\"&gt;Title:&lt;/label&gt;\n                    &lt;input type=\"text\" id=\"title\" name=\"title\" required&gt;\n                &lt;/div&gt;\n                &lt;div class=\"form-group\"&gt;\n                    &lt;label for=\"author\"&gt;Author:&lt;/label&gt;\n                    &lt;input type=\"text\" id=\"author\" name=\"author\" required&gt;\n                &lt;/div&gt;\n                &lt;div class=\"form-group\"&gt;\n                    &lt;label for=\"content\"&gt;Content:&lt;/label&gt;\n                    &lt;textarea id=\"content\" name=\"content\" required&gt;&lt;/textarea&gt;\n                &lt;/div&gt;\n                &lt;button type=\"submit\"&gt;Create Post&lt;/button&gt;\n            &lt;/form&gt;\n        &lt;/body&gt;\n        &lt;/html&gt;\n        \"\"\"\n\n        response.content_type(\"text/html\")\n        response.body(html)\n\n    async def create_post(self, form: CreatePostForm, response: ResponseBuilder = dependency()):\n        \"\"\"Handle post creation\"\"\"\n        # Create the new post\n        post = self.storage.add_post(form.title, form.content, form.author)\n\n        # Redirect to the new post\n        response.set_status(302)\n        response.add_header(\"Location\", f\"/post/{post.id}\")\n        response.body(\"\")\n</code></pre>"}, {"location": "getting-started/first-app/#3-create-the-plugin-configuration", "title": "3. Create the Plugin Configuration", "text": "<p>Create <code>plugins/blog/plugin.yaml</code>:</p> <pre><code>name: Blog Plugin\ndescription: A simple blog plugin for Serv\nversion: 1.0.0\nauthor: Your Name\nentry: blog.main:BlogPlugin\n\nsettings:\n  posts_per_page: 10\n  allow_comments: false\n</code></pre>"}, {"location": "getting-started/first-app/#4-create-the-main-application", "title": "4. Create the Main Application", "text": "<p>Create <code>app.py</code>:</p> <pre><code>from serv import App\n\n# Create the app with plugin directory\napp = App(\n    config=\"./serv.config.yaml\",\n    plugin_dir=\"./plugins\"\n)\n\nif __name__ == \"__main__\":\n    import uvicorn\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000, reload=True)\n</code></pre>"}, {"location": "getting-started/first-app/#5-create-the-configuration-file", "title": "5. Create the Configuration File", "text": "<p>Create <code>serv.config.yaml</code>:</p> <pre><code>plugins:\n  - plugin: blog\n    settings:\n      posts_per_page: 5\n      allow_comments: true\n</code></pre>"}, {"location": "getting-started/first-app/#6-initialize-the-plugin-package", "title": "6. Initialize the Plugin Package", "text": "<p>Create <code>plugins/blog/__init__.py</code>:</p> <pre><code># Blog plugin package\n</code></pre>"}, {"location": "getting-started/first-app/#running-the-application", "title": "Running the Application", "text": "<p>Now let's run our blog application:</p> <pre><code>python app.py\n</code></pre> <p>Visit the following URLs to test your application:</p> <ul> <li><code>http://localhost:8000/</code> - Homepage with blog posts</li> <li><code>http://localhost:8000/post/1</code> - View individual post</li> <li><code>http://localhost:8000/admin</code> - Admin interface to create posts</li> <li><code>http://localhost:8000/api/posts</code> - JSON API endpoint</li> </ul>"}, {"location": "getting-started/first-app/#understanding-the-code", "title": "Understanding the Code", "text": ""}, {"location": "getting-started/first-app/#plugin-architecture", "title": "Plugin Architecture", "text": "<p>Our blog is implemented as a plugin, which makes it:</p> <ul> <li>Modular: Easy to enable/disable</li> <li>Reusable: Can be used in multiple applications</li> <li>Configurable: Settings can be overridden via configuration</li> </ul>"}, {"location": "getting-started/first-app/#route-handling", "title": "Route Handling", "text": "<p>We used two different routing approaches:</p> <ol> <li>Function-based routes: Simple handlers like <code>homepage()</code> and <code>view_post()</code></li> <li>Class-based routes: The <code>AdminRoute</code> class for more complex logic</li> </ol>"}, {"location": "getting-started/first-app/#form-handling", "title": "Form Handling", "text": "<p>The <code>CreatePostForm</code> class automatically handles form data parsing and validation:</p> <pre><code>class CreatePostForm(Form):\n    title: str\n    content: str\n    author: str\n</code></pre>"}, {"location": "getting-started/first-app/#dependency-injection", "title": "Dependency Injection", "text": "<p>Notice how we inject dependencies throughout the application:</p> <pre><code>async def homepage(self, response: ResponseBuilder = dependency()):\n    # ResponseBuilder is automatically injected\n</code></pre>"}, {"location": "getting-started/first-app/#extending-the-application", "title": "Extending the Application", "text": "<p>Here are some ideas for extending this blog:</p>"}, {"location": "getting-started/first-app/#add-database-support", "title": "Add Database Support", "text": "<p>Replace the in-memory storage with a real database:</p> <pre><code>import sqlite3\nfrom contextlib import asynccontextmanager\n\nclass DatabaseStorage:\n    def __init__(self, db_path: str = \"blog.db\"):\n        self.db_path = db_path\n        self._init_db()\n\n    def _init_db(self):\n        with sqlite3.connect(self.db_path) as conn:\n            conn.execute(\"\"\"\n                CREATE TABLE IF NOT EXISTS posts (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,\n                    title TEXT NOT NULL,\n                    content TEXT NOT NULL,\n                    author TEXT NOT NULL,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n                )\n            \"\"\")\n</code></pre>"}, {"location": "getting-started/first-app/#add-authentication", "title": "Add Authentication", "text": "<p>Create an authentication plugin:</p> <pre><code>class AuthPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/login\", self.login_page)\n        router.add_route(\"/logout\", self.logout)\n\n    async def login_page(self, response: ResponseBuilder = dependency()):\n        # Implement login logic\n        pass\n</code></pre>"}, {"location": "getting-started/first-app/#add-comments", "title": "Add Comments", "text": "<p>Extend the models to support comments:</p> <pre><code>@dataclass\nclass Comment:\n    id: int\n    post_id: int\n    author: str\n    content: str\n    created_at: datetime\n</code></pre>"}, {"location": "getting-started/first-app/#add-rich-templates", "title": "Add Rich Templates", "text": "<p>Use Jinja2 for proper template rendering:</p> <pre><code>from jinja2 import Environment, FileSystemLoader\n\nclass BlogPlugin(Plugin):\n    def __init__(self):\n        self.storage = BlogStorage()\n        self.jinja_env = Environment(\n            loader=FileSystemLoader('templates')\n        )\n\n    def _render_template(self, template_name: str, context: dict) -&gt; str:\n        template = self.jinja_env.get_template(template_name)\n        return template.render(**context)\n</code></pre>"}, {"location": "getting-started/first-app/#next-steps", "title": "Next Steps", "text": "<p>Congratulations! You've built a complete blog application with Serv. You've learned about:</p> <ul> <li>✅ Plugin architecture</li> <li>✅ Routing (both function and class-based)</li> <li>✅ Form handling</li> <li>✅ Template rendering</li> <li>✅ Configuration management</li> <li>✅ API endpoints</li> </ul>"}, {"location": "getting-started/first-app/#continue-learning", "title": "Continue Learning", "text": "<ul> <li>Configuration - Learn about advanced configuration options</li> <li>Routing Guide - Master advanced routing techniques</li> <li>Plugin Development - Build more sophisticated plugins</li> <li>Middleware - Add cross-cutting concerns to your app</li> </ul>"}, {"location": "getting-started/first-app/#explore-more-examples", "title": "Explore More Examples", "text": "<ul> <li>Authentication Example - Add user authentication</li> <li>Database Integration - Connect to real databases</li> <li>API Development - Build REST APIs with Serv </li> </ul>"}, {"location": "getting-started/installation/", "title": "Installation", "text": "<p>This guide will help you install Serv and get your development environment set up.</p>"}, {"location": "getting-started/installation/#requirements", "title": "Requirements", "text": "<p>Serv requires Python 3.13 or higher. Make sure you have a compatible Python version installed:</p> <pre><code>python --version\n</code></pre>"}, {"location": "getting-started/installation/#installing-serv", "title": "Installing Serv", "text": ""}, {"location": "getting-started/installation/#using-pip", "title": "Using pip", "text": "<p>The easiest way to install Serv is using pip:</p> <pre><code>pip install getserving\n</code></pre> <p>Package Name</p> <p>The PyPI package is named <code>getserving</code> because <code>serv</code> was already taken. However, you'll still import it as <code>serv</code> in your Python code.</p>"}, {"location": "getting-started/installation/#using-uv-recommended", "title": "Using uv (Recommended)", "text": "<p>If you're using uv (which we highly recommend for Python project management):</p> <pre><code>uv add getserving\n</code></pre>"}, {"location": "getting-started/installation/#development-installation", "title": "Development Installation", "text": "<p>If you want to contribute to Serv or install the latest development version:</p> <pre><code>git clone https://github.com/your-org/serv.git\ncd serv\nuv sync --group dev\n</code></pre>"}, {"location": "getting-started/installation/#verifying-installation", "title": "Verifying Installation", "text": "<p>To verify that Serv is installed correctly, you can run:</p> <pre><code>python -c \"import serv; print(serv.__version__)\"\n</code></pre> <p>Or use the CLI:</p> <pre><code>serv --version\n</code></pre>"}, {"location": "getting-started/installation/#optional-dependencies", "title": "Optional Dependencies", "text": "<p>Serv has several optional dependencies that you might want to install depending on your use case:</p>"}, {"location": "getting-started/installation/#template-rendering", "title": "Template Rendering", "text": "<p>For Jinja2 template support (already included by default):</p> <pre><code>pip install getserving[jinja]\n</code></pre>"}, {"location": "getting-started/installation/#file-upload-support", "title": "File Upload Support", "text": "<p>For multipart form data and file upload support (already included by default):</p> <pre><code>pip install getserving[multipart]\n</code></pre>"}, {"location": "getting-started/installation/#development-tools", "title": "Development Tools", "text": "<p>For development and testing:</p> <pre><code>pip install getserving[dev]\n</code></pre> <p>This includes: - <code>pytest</code> and <code>pytest-asyncio</code> for testing - <code>uvicorn</code> for running the development server - <code>httpx</code> for making HTTP requests in tests - <code>ruff</code> for linting and formatting</p>"}, {"location": "getting-started/installation/#asgi-server", "title": "ASGI Server", "text": "<p>Serv is an ASGI framework, so you'll need an ASGI server to run your applications. We recommend Uvicorn:</p> <pre><code>pip install uvicorn\n</code></pre> <p>Or with uv:</p> <pre><code>uv add uvicorn\n</code></pre>"}, {"location": "getting-started/installation/#ide-setup", "title": "IDE Setup", "text": ""}, {"location": "getting-started/installation/#vs-code", "title": "VS Code", "text": "<p>For the best development experience with VS Code, install the Python extension and configure it to use your virtual environment.</p>"}, {"location": "getting-started/installation/#pycharm", "title": "PyCharm", "text": "<p>PyCharm has excellent support for Python and ASGI applications. Make sure to configure your interpreter to use the virtual environment where Serv is installed.</p>"}, {"location": "getting-started/installation/#next-steps", "title": "Next Steps", "text": "<p>Now that you have Serv installed, you're ready to:</p> <ol> <li>Quick Start - Create your first Serv application</li> <li>Your First App - Build a complete application step by step</li> <li>Configuration - Learn about configuring Serv</li> </ol>"}, {"location": "getting-started/installation/#troubleshooting", "title": "Troubleshooting", "text": ""}, {"location": "getting-started/installation/#python-version-issues", "title": "Python Version Issues", "text": "<p>If you're getting errors about Python version compatibility, make sure you're using Python 3.13 or higher:</p> <pre><code>python --version\n</code></pre>"}, {"location": "getting-started/installation/#import-errors", "title": "Import Errors", "text": "<p>If you're getting import errors, make sure you're importing from <code>serv</code>, not <code>getserving</code>:</p> <pre><code># ✅ Correct\nfrom serv import App\n\n# ❌ Incorrect\nfrom getserving import App\n</code></pre>"}, {"location": "getting-started/installation/#virtual-environment-issues", "title": "Virtual Environment Issues", "text": "<p>If you're having dependency conflicts, try creating a fresh virtual environment:</p> <pre><code>python -m venv venv\nsource venv/bin/activate  # On Windows: venv\\Scripts\\activate\npip install getserving\n</code></pre>"}, {"location": "getting-started/quick-start/", "title": "Quick Start", "text": "<p>Get up and running with Serv in just a few minutes! This guide will walk you through creating your first Serv application.</p>"}, {"location": "getting-started/quick-start/#prerequisites", "title": "Prerequisites", "text": "<p>Make sure you have Serv installed. If not, check out the Installation guide.</p>"}, {"location": "getting-started/quick-start/#your-first-serv-app", "title": "Your First Serv App", "text": "<p>Let's create a simple \"Hello World\" application:</p>"}, {"location": "getting-started/quick-start/#1-create-the-application-file", "title": "1. Create the Application File", "text": "<p>Create a new file called <code>app.py</code>:</p> <pre><code>from serv import App\nfrom serv.responses import ResponseBuilder\nfrom serv.plugins import Plugin\nfrom serv.plugins.routing import Router\nfrom bevy import dependency\n\nasync def hello_world(response: ResponseBuilder = dependency()):\n    response.content_type(\"text/plain\")\n    response.body(\"Hello, World from Serv!\")\n\nasync def greet_user(name: str, response: ResponseBuilder = dependency()):\n    response.content_type(\"text/html\")\n    response.body(f\"&lt;h1&gt;Hello, {name}!&lt;/h1&gt;&lt;p&gt;Welcome to Serv!&lt;/p&gt;\")\n\nclass HelloPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/\", hello_world)\n        router.add_route(\"/greet/{name}\", greet_user)\n\n# Create the app and add our plugin\napp = App()\napp.add_plugin(HelloPlugin())\n\nif __name__ == \"__main__\":\n    import uvicorn\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n</code></pre>"}, {"location": "getting-started/quick-start/#2-run-the-application", "title": "2. Run the Application", "text": "<p>Run your application:</p> <pre><code>python app.py\n</code></pre> <p>Or using uvicorn directly:</p> <pre><code>uvicorn app:app --reload\n</code></pre>"}, {"location": "getting-started/quick-start/#3-test-your-application", "title": "3. Test Your Application", "text": "<p>Open your browser and visit:</p> <ul> <li><code>http://localhost:8000/</code> - See the hello world message</li> <li><code>http://localhost:8000/greet/YourName</code> - See a personalized greeting</li> </ul>"}, {"location": "getting-started/quick-start/#understanding-the-code", "title": "Understanding the Code", "text": "<p>Let's break down what's happening in this simple application:</p>"}, {"location": "getting-started/quick-start/#the-plugin-system", "title": "The Plugin System", "text": "<pre><code>class HelloPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/\", hello_world)\n        router.add_route(\"/greet/{name}\", greet_user)\n</code></pre> <ul> <li>Plugins are the building blocks of Serv applications</li> <li>The <code>on_app_request_begin</code> event is called for every request</li> <li>We use dependency injection to get the <code>Router</code> instance</li> <li>Routes are added using <code>router.add_route()</code></li> </ul>"}, {"location": "getting-started/quick-start/#dependency-injection", "title": "Dependency Injection", "text": "<pre><code>async def hello_world(response: ResponseBuilder = dependency()):\n    response.content_type(\"text/plain\")\n    response.body(\"Hello, World from Serv!\")\n</code></pre> <ul> <li>Serv uses the <code>bevy</code> library for dependency injection</li> <li><code>ResponseBuilder</code> is automatically injected into your handler functions</li> <li>No need to manually pass objects around!</li> </ul>"}, {"location": "getting-started/quick-start/#path-parameters", "title": "Path Parameters", "text": "<pre><code>async def greet_user(name: str, response: ResponseBuilder = dependency()):\n    # The 'name' parameter is extracted from the URL path\n</code></pre> <ul> <li>Path parameters are defined using <code>{parameter_name}</code> in the route</li> <li>They're automatically passed to your handler function</li> </ul>"}, {"location": "getting-started/quick-start/#adding-json-responses", "title": "Adding JSON Responses", "text": "<p>Let's add an API endpoint that returns JSON:</p> <pre><code>import json\n\nasync def api_hello(response: ResponseBuilder = dependency()):\n    response.content_type(\"application/json\")\n    data = {\n        \"message\": \"Hello from Serv API!\",\n        \"framework\": \"Serv\",\n        \"version\": \"0.1.0\"\n    }\n    response.body(json.dumps(data))\n\n# Add this route in your plugin:\nrouter.add_route(\"/api/hello\", api_hello)\n</code></pre>"}, {"location": "getting-started/quick-start/#using-the-cli", "title": "Using the CLI", "text": "<p>Serv comes with a powerful CLI for scaffolding and managing projects:</p>"}, {"location": "getting-started/quick-start/#create-a-new-project", "title": "Create a New Project", "text": "<pre><code>serv project create my-awesome-app\ncd my-awesome-app\n</code></pre>"}, {"location": "getting-started/quick-start/#create-a-plugin", "title": "Create a Plugin", "text": "<pre><code>serv plugin create my-plugin\n</code></pre>"}, {"location": "getting-started/quick-start/#run-the-development-server", "title": "Run the Development Server", "text": "<pre><code>serv launch --dev\n</code></pre>"}, {"location": "getting-started/quick-start/#configuration-with-yaml", "title": "Configuration with YAML", "text": "<p>For more complex applications, you can use YAML configuration. Create a <code>serv.config.yaml</code> file:</p> <pre><code>plugins:\n  - plugin: my_plugin\n    settings:\n      debug: true\n      api_key: \"your-api-key\"\n</code></pre> <p>Then load it in your app:</p> <pre><code>app = App(config=\"./serv.config.yaml\")\n</code></pre>"}, {"location": "getting-started/quick-start/#next-steps", "title": "Next Steps", "text": "<p>Congratulations! You've created your first Serv application. Here's what to explore next:</p>"}, {"location": "getting-started/quick-start/#learn-more-about-core-concepts", "title": "Learn More About Core Concepts", "text": "<ul> <li>Routing - Advanced routing patterns and techniques</li> <li>Dependency Injection - Master the DI system</li> <li>Plugins - Build powerful, reusable plugins</li> </ul>"}, {"location": "getting-started/quick-start/#build-a-complete-application", "title": "Build a Complete Application", "text": "<ul> <li>Your First App - Step-by-step tutorial for a complete web application</li> <li>Configuration - Learn about advanced configuration options</li> </ul>"}, {"location": "getting-started/quick-start/#explore-examples", "title": "Explore Examples", "text": "<ul> <li>Basic App - More detailed basic application examples</li> <li>Plugin Development - Learn to build custom plugins</li> <li>Advanced Routing - Complex routing scenarios</li> </ul>"}, {"location": "getting-started/quick-start/#common-patterns", "title": "Common Patterns", "text": ""}, {"location": "getting-started/quick-start/#error-handling", "title": "Erro<PERSON>", "text": "<pre><code>from serv.exceptions import HTTPNotFoundException\n\nasync def not_found_handler(response: ResponseBuilder = dependency()):\n    response.set_status(404)\n    response.content_type(\"text/html\")\n    response.body(\"&lt;h1&gt;Page Not Found&lt;/h1&gt;\")\n\n# Add custom error handlers\napp.add_error_handler(HTTPNotFoundException, not_found_handler)\n</code></pre>"}, {"location": "getting-started/quick-start/#middleware", "title": "Middleware", "text": "<pre><code>from typing import AsyncIterator\n\nasync def logging_middleware() -&gt; AsyncIterator[None]:\n    print(\"Request started\")\n    yield\n    print(\"Request finished\")\n\napp.add_middleware(logging_middleware)\n</code></pre>"}, {"location": "getting-started/quick-start/#multiple-http-methods", "title": "Multiple HTTP Methods", "text": "<pre><code>async def handle_post(response: ResponseBuilder = dependency()):\n    response.body(\"POST request received\")\n\n# Add route with specific HTTP method\nrouter.add_route(\"/submit\", handle_post, methods=[\"POST\"])\n</code></pre> <p>Ready to dive deeper? Continue with Your First App for a comprehensive tutorial! </p>"}, {"location": "guides/dependency-injection/", "title": "Dependency Injection", "text": "<p>Serv uses the powerful <code>bevy</code> library for dependency injection, making your code clean, testable, and maintainable. This guide covers everything you need to know about dependency injection in Serv.</p>"}, {"location": "guides/dependency-injection/#what-is-dependency-injection", "title": "What is Dependency Injection?", "text": "<p>Dependency injection (DI) is a design pattern where objects receive their dependencies from external sources rather than creating them internally. This makes code more modular, testable, and flexible.</p>"}, {"location": "guides/dependency-injection/#without-di-tightly-coupled", "title": "Without DI (tightly coupled)", "text": "<pre><code>class UserService:\n    def __init__(self):\n        self.db = Database()  # Hard dependency\n\n    def get_user(self, user_id: int):\n        return self.db.query(f\"SELECT * FROM users WHERE id = {user_id}\")\n</code></pre>"}, {"location": "guides/dependency-injection/#with-di-loosely-coupled", "title": "With DI (loosely coupled)", "text": "<pre><code>class UserService:\n    def __init__(self, db: Database):\n        self.db = db  # Injected dependency\n\n    def get_user(self, user_id: int):\n        return self.db.query(f\"SELECT * FROM users WHERE id = {user_id}\")\n</code></pre>"}, {"location": "guides/dependency-injection/#basic-dependency-injection", "title": "Basic Dependency Injection", "text": ""}, {"location": "guides/dependency-injection/#using-dependency", "title": "Using <code>dependency()</code>", "text": "<p>The simplest way to inject dependencies in Serv is using the <code>dependency()</code> function:</p> <pre><code>from serv.responses import ResponseBuilder\nfrom serv.requests import Request\nfrom bevy import dependency\n\nasync def my_handler(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n):\n    response.content_type(\"text/plain\")\n    response.body(f\"Hello from {request.path}\")\n</code></pre>"}, {"location": "guides/dependency-injection/#built-in-dependencies", "title": "Built-in Dependencies", "text": "<p>Serv automatically provides several built-in dependencies:</p> <pre><code>from serv import App\nfrom serv.requests import Request\nfrom serv.responses import ResponseBuilder\nfrom serv.plugins.routing import Router\nfrom bevy import dependency, Container\n\nasync def handler_with_all_deps(\n    request: Request = dependency(),           # Current request\n    response: ResponseBuilder = dependency(),  # Response builder\n    router: Router = dependency(),            # Current router\n    container: Container = dependency(),      # DI container\n    app: App = dependency()                   # Application instance\n):\n    # Use all the dependencies\n    pass\n</code></pre>"}, {"location": "guides/dependency-injection/#custom-dependencies", "title": "Custom Dependencies", "text": ""}, {"location": "guides/dependency-injection/#registering-services", "title": "Registering Services", "text": "<p>Register your own services in the DI container:</p> <pre><code>from bevy import dependency\n\nclass DatabaseService:\n    def __init__(self, connection_string: str):\n        self.connection_string = connection_string\n\n    def query(self, sql: str):\n        # Database query implementation\n        pass\n\nclass MyPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        # Register the database service\n        db_service = DatabaseService(\"postgresql://localhost/mydb\")\n        container.instances[DatabaseService] = db_service\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/users\", self.list_users)\n\n    async def list_users(\n        self,\n        db: DatabaseService = dependency(),\n        response: ResponseBuilder = dependency()\n    ):\n        users = db.query(\"SELECT * FROM users\")\n        response.content_type(\"application/json\")\n        response.body(json.dumps(users))\n</code></pre>"}, {"location": "guides/dependency-injection/#factory-functions", "title": "Factory Functions", "text": "<p>Use factory functions for complex object creation:</p> <pre><code>from bevy import dependency\n\ndef create_email_service(config: dict) -&gt; EmailService:\n    return EmailService(\n        smtp_host=config.get('smtp_host'),\n        smtp_port=config.get('smtp_port'),\n        username=config.get('smtp_user'),\n        password=config.get('smtp_password')\n    )\n\nclass EmailPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        config = self.get_config()\n        email_service = create_email_service(config)\n        container.instances[EmailService] = email_service\n</code></pre>"}, {"location": "guides/dependency-injection/#advanced-dependency-injection", "title": "Advanced Dependency Injection", "text": ""}, {"location": "guides/dependency-injection/#interface-based-dependencies", "title": "Interface-Based Dependencies", "text": "<p>Use abstract base classes for better testability:</p> <pre><code>from abc import ABC, abstractmethod\n\nclass UserRepository(ABC):\n    @abstractmethod\n    async def get_user(self, user_id: int) -&gt; User:\n        pass\n\n    @abstractmethod\n    async def create_user(self, user_data: dict) -&gt; User:\n        pass\n\nclass DatabaseUserRepository(UserRepository):\n    def __init__(self, db: Database):\n        self.db = db\n\n    async def get_user(self, user_id: int) -&gt; User:\n        # Database implementation\n        pass\n\n    async def create_user(self, user_data: dict) -&gt; User:\n        # Database implementation\n        pass\n\nclass InMemoryUserRepository(UserRepository):\n    def __init__(self):\n        self.users = {}\n\n    async def get_user(self, user_id: int) -&gt; User:\n        # In-memory implementation\n        pass\n\n    async def create_user(self, user_data: dict) -&gt; User:\n        # In-memory implementation\n        pass\n\n# Register the implementation\nclass UserPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        # Use database implementation in production\n        db = container.get(Database)\n        user_repo = DatabaseUserRepository(db)\n        container.instances[UserRepository] = user_repo\n</code></pre>"}, {"location": "guides/dependency-injection/#conditional-dependencies", "title": "Conditional Dependencies", "text": "<p>Register different implementations based on configuration:</p> <pre><code>class StoragePlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        config = self.get_config()\n        storage_type = config.get('storage_type', 'local')\n\n        if storage_type == 'local':\n            storage = LocalFileStorage(config.get('local_path', './uploads'))\n        elif storage_type == 's3':\n            storage = S3Storage(\n                bucket=config.get('s3_bucket'),\n                access_key=config.get('s3_access_key'),\n                secret_key=config.get('s3_secret_key')\n            )\n        else:\n            raise ValueError(f\"Unknown storage type: {storage_type}\")\n\n        container.instances[FileStorage] = storage\n</code></pre>"}, {"location": "guides/dependency-injection/#scoped-dependencies", "title": "Scoped Dependencies", "text": "<p>Create request-scoped dependencies that are unique per request:</p> <pre><code>class RequestScopedService:\n    def __init__(self, request_id: str):\n        self.request_id = request_id\n        self.data = {}\n\n    def store(self, key: str, value: any):\n        self.data[key] = value\n\n    def get(self, key: str):\n        return self.data.get(key)\n\nclass ScopedPlugin(Plugin):\n    async def on_app_request_begin(\n        self,\n        container: Container = dependency(),\n        request: Request = dependency()\n    ):\n        # Create a request-scoped service\n        request_id = request.headers.get('X-Request-ID', str(uuid.uuid4()))\n        scoped_service = RequestScopedService(request_id)\n        container.instances[RequestScopedService] = scoped_service\n</code></pre>"}, {"location": "guides/dependency-injection/#dependency-injection-in-different-contexts", "title": "Dependency Injection in Different Contexts", "text": ""}, {"location": "guides/dependency-injection/#in-route-handlers", "title": "In Route Handlers", "text": "<pre><code>async def user_handler(\n    user_id: str,\n    user_service: UserService = dependency(),\n    response: ResponseBuilder = dependency()\n):\n    user = await user_service.get_user(int(user_id))\n    if user:\n        response.content_type(\"application/json\")\n        response.body(json.dumps(user.to_dict()))\n    else:\n        response.set_status(404)\n        response.body(\"User not found\")\n</code></pre>"}, {"location": "guides/dependency-injection/#in-class-based-routes", "title": "In Class-Based Routes", "text": "<pre><code>from serv.routes import Route\n\nclass UserRoute(Route):\n    async def get_user(\n        self,\n        request: GetRequest,\n        user_service: UserService = dependency(),\n        response: ResponseBuilder = dependency()\n    ):\n        user_id = request.path_params.get('user_id')\n        user = await user_service.get_user(int(user_id))\n        # Handle response...\n\n    async def create_user(\n        self,\n        form: CreateUserForm,\n        user_service: UserService = dependency(),\n        response: ResponseBuilder = dependency()\n    ):\n        user = await user_service.create_user(form.to_dict())\n        # Handle response...\n</code></pre>"}, {"location": "guides/dependency-injection/#in-middleware", "title": "In Middleware", "text": "<pre><code>from typing import AsyncIterator\n\nasync def auth_middleware(\n    request: Request = dependency(),\n    auth_service: AuthService = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    # Check authentication before request\n    token = request.headers.get('Authorization')\n    if not token or not auth_service.validate_token(token):\n        response.set_status(401)\n        response.body(\"Unauthorized\")\n        return\n\n    # Set current user in request context\n    user = auth_service.get_user_from_token(token)\n    request.context['current_user'] = user\n\n    yield  # Process the request\n\n    # Cleanup after request if needed\n</code></pre>"}, {"location": "guides/dependency-injection/#in-plugin-event-handlers", "title": "In Plugin Event Handlers", "text": "<pre><code>class MyPlugin(Plugin):\n    async def on_app_startup(\n        self,\n        container: Container = dependency(),\n        app: App = dependency()\n    ):\n        # Initialize services\n        config = self.get_config()\n        service = MyService(config)\n        container.instances[MyService] = service\n\n    async def on_app_request_begin(\n        self,\n        router: Router = dependency(),\n        my_service: MyService = dependency()\n    ):\n        # Use the service to configure routes\n        if my_service.is_enabled():\n            router.add_route(\"/my-route\", self.my_handler)\n</code></pre>"}, {"location": "guides/dependency-injection/#testing-with-dependency-injection", "title": "Testing with Dependency Injection", "text": ""}, {"location": "guides/dependency-injection/#mocking-dependencies", "title": "Mocking Dependencies", "text": "<p>DI makes testing much easier by allowing you to mock dependencies:</p> <pre><code>import pytest\nfrom unittest.mock import Mock, AsyncMock\nfrom bevy import Container\n\*********************\nasync def test_user_handler():\n    # Create mocks\n    mock_user_service = Mock(spec=UserService)\n    mock_user_service.get_user = AsyncMock(return_value=User(id=1, name=\"<PERSON>\"))\n\n    mock_response = Mock(spec=ResponseBuilder)\n\n    # Create container with mocked dependencies\n    container = Container()\n    container.instances[UserService] = mock_user_service\n    container.instances[ResponseBuilder] = mock_response\n\n    # Test the handler\n    await container.call(user_handler, user_id=\"1\")\n\n    # Verify interactions\n    mock_user_service.get_user.assert_called_once_with(1)\n    mock_response.content_type.assert_called_once_with(\"application/json\")\n</code></pre>"}, {"location": "guides/dependency-injection/#test-fixtures", "title": "Test Fixtures", "text": "<p>Create reusable test fixtures:</p> <pre><code>@pytest.fixture\ndef mock_user_service():\n    service = Mock(spec=UserService)\n    service.get_user = AsyncMock()\n    service.create_user = AsyncMock()\n    return service\n\****************\ndef test_container(mock_user_service):\n    container = Container()\n    container.instances[UserService] = mock_user_service\n    return container\n\*********************\nasync def test_create_user(test_container, mock_user_service):\n    mock_user_service.create_user.return_value = User(id=1, name=\"<PERSON>\")\n\n    # Test using the container\n    result = await test_container.call(create_user_handler, user_data={\"name\": \"<PERSON>\"})\n\n    assert result is not None\n    mock_user_service.create_user.assert_called_once()\n</code></pre>"}, {"location": "guides/dependency-injection/#best-practices", "title": "Best Practices", "text": ""}, {"location": "guides/dependency-injection/#1-use-type-hints", "title": "1. Use Type Hints", "text": "<p>Always use type hints for better IDE support and clarity:</p> <pre><code>async def handler(\n    user_service: UserService = dependency(),  # Clear type\n    response: ResponseBuilder = dependency()\n) -&gt; None:  # Clear return type\n    pass\n</code></pre>"}, {"location": "guides/dependency-injection/#2-prefer-interfaces", "title": "2. <PERSON>fer Interfaces", "text": "<p>Use abstract base classes for better testability:</p> <pre><code>from abc import ABC, abstractmethod\n\nclass EmailService(ABC):\n    @abstractmethod\n    async def send_email(self, to: str, subject: str, body: str) -&gt; bool:\n        pass\n\nclass SMTPEmailService(EmailService):\n    async def send_email(self, to: str, subject: str, body: str) -&gt; bool:\n        # SMTP implementation\n        pass\n\nclass MockEmailService(EmailService):\n    async def send_email(self, to: str, subject: str, body: str) -&gt; bool:\n        # Mock implementation for testing\n        return True\n</code></pre>"}, {"location": "guides/dependency-injection/#3-register-dependencies-early", "title": "3. Register Dependencies Early", "text": "<p>Register dependencies during application startup:</p> <pre><code>class MyPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        # Register all services early\n        container.instances[UserService] = UserService()\n        container.instances[EmailService] = SMTPEmailService()\n        container.instances[CacheService] = RedisCache()\n</code></pre>"}, {"location": "guides/dependency-injection/#4-avoid-circular-dependencies", "title": "4. Avoid Circular Dependencies", "text": "<p>Be careful about circular dependencies:</p> <pre><code># Bad: Circular dependency\nclass UserService:\n    def __init__(self, order_service: OrderService):\n        self.order_service = order_service\n\nclass OrderService:\n    def __init__(self, user_service: UserService):\n        self.user_service = user_service\n\n# Good: Use events or interfaces to break the cycle\nclass UserService:\n    def __init__(self, event_bus: EventBus):\n        self.event_bus = event_bus\n\n    def create_user(self, user_data):\n        user = User(**user_data)\n        self.event_bus.emit('user.created', user)\n        return user\n\nclass OrderService:\n    def __init__(self, event_bus: EventBus):\n        self.event_bus = event_bus\n        self.event_bus.subscribe('user.created', self.on_user_created)\n\n    def on_user_created(self, user):\n        # Handle user creation\n        pass\n</code></pre>"}, {"location": "guides/dependency-injection/#5-use-factory-functions-for-complex-setup", "title": "5. Use Factory Functions for Complex Setup", "text": "<p>For complex object creation, use factory functions:</p> <pre><code>def create_database_service(config: dict) -&gt; DatabaseService:\n    connection_pool = create_connection_pool(\n        host=config['db_host'],\n        port=config['db_port'],\n        database=config['db_name'],\n        username=config['db_user'],\n        password=config['db_password'],\n        pool_size=config.get('db_pool_size', 10)\n    )\n\n    return DatabaseService(connection_pool)\n\nclass DatabasePlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        config = self.get_config()\n        db_service = create_database_service(config)\n        container.instances[DatabaseService] = db_service\n</code></pre>"}, {"location": "guides/dependency-injection/#common-patterns", "title": "Common Patterns", "text": ""}, {"location": "guides/dependency-injection/#service-locator-pattern", "title": "Service Locator Pattern", "text": "<p>Sometimes you need to access the container directly:</p> <pre><code>from bevy import dependency\n\nclass ServiceLocator:\n    def __init__(self, container: Container):\n        self.container = container\n\n    def get_service(self, service_type: type):\n        return self.container.get(service_type)\n\nclass MyPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        locator = ServiceLocator(container)\n        container.instances[ServiceLocator] = locator\n</code></pre>"}, {"location": "guides/dependency-injection/#configuration-injection", "title": "Configuration Injection", "text": "<p>Inject configuration objects:</p> <pre><code>from dataclasses import dataclass\n\n@dataclass\nclass DatabaseConfig:\n    host: str\n    port: int\n    database: str\n    username: str\n    password: str\n\nclass DatabasePlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        config_dict = self.get_config()\n        db_config = DatabaseConfig(**config_dict)\n        container.instances[DatabaseConfig] = db_config\n\n        # Use config to create service\n        db_service = DatabaseService(db_config)\n        container.instances[DatabaseService] = db_service\n\nasync def handler(\n    db_config: DatabaseConfig = dependency(),\n    response: ResponseBuilder = dependency()\n):\n    response.body(f\"Connected to {db_config.host}:{db_config.port}\")\n</code></pre>"}, {"location": "guides/dependency-injection/#next-steps", "title": "Next Steps", "text": "<ul> <li>Plugins - Learn how to create plugins that use DI</li> <li>Middleware - Use DI in middleware</li> <li>Testing - Test code that uses dependency injection</li> <li>Routing - Use DI in route handlers </li> </ul>"}, {"location": "guides/middleware/", "title": "Middleware", "text": "<p>Middleware in Serv allows you to process requests and responses at various stages of the request lifecycle. This guide covers how to create and use middleware effectively.</p>"}, {"location": "guides/middleware/#what-is-middleware", "title": "What is Middleware?", "text": "<p>Middleware are functions that execute during the request/response cycle. They can:</p> <ul> <li>Modify requests before they reach route handlers</li> <li>Modify responses before they're sent to clients</li> <li>Perform authentication and authorization</li> <li>Log requests and responses</li> <li>Handle errors</li> <li>Add security headers</li> </ul>"}, {"location": "guides/middleware/#basic-middleware", "title": "Basic Middleware", "text": ""}, {"location": "guides/middleware/#simple-middleware", "title": "Simple Middleware", "text": "<p>Here's a basic middleware that logs requests:</p> <pre><code>from typing import AsyncIterator\nfrom serv.requests import Request\nfrom bevy import dependency\n\nasync def logging_middleware(\n    request: Request = dependency()\n) -&gt; AsyncIterator[None]:\n    print(f\"Request: {request.method} {request.path}\")\n\n    yield  # Process the request\n\n    print(f\"Request completed: {request.method} {request.path}\")\n\n# Register the middleware\napp.add_middleware(logging_middleware)\n</code></pre>"}, {"location": "guides/middleware/#middleware-with-response-access", "title": "Middleware with Response Access", "text": "<p>Access and modify responses:</p> <pre><code>from serv.responses import Response<PERSON>uilder\n\nasync def response_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    # Before request processing\n    start_time = time.time()\n\n    yield  # Process the request\n\n    # After request processing\n    duration = time.time() - start_time\n    response.add_header(\"X-Response-Time\", f\"{duration:.3f}s\")\n</code></pre>"}, {"location": "guides/middleware/#middleware-patterns", "title": "Middleware Patterns", "text": ""}, {"location": "guides/middleware/#authentication-middleware", "title": "Authentication Middleware", "text": "<pre><code>async def auth_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    # Check for authentication token\n    token = request.headers.get(\"Authorization\")\n\n    if not token:\n        response.set_status(401)\n        response.content_type(\"application/json\")\n        response.body('{\"error\": \"Authentication required\"}')\n        return  # Don't yield - stop processing\n\n    # Validate token\n    if not is_valid_token(token):\n        response.set_status(401)\n        response.content_type(\"application/json\")\n        response.body('{\"error\": \"Invalid token\"}')\n        return\n\n    # Add user to request context\n    user = get_user_from_token(token)\n    request.context['user'] = user\n\n    yield  # Continue processing\n</code></pre>"}, {"location": "guides/middleware/#cors-middleware", "title": "CORS Middleware", "text": "<pre><code>async def cors_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    # Handle preflight requests\n    if request.method == \"OPTIONS\":\n        response.add_header(\"Access-Control-Allow-Origin\", \"*\")\n        response.add_header(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE\")\n        response.add_header(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\")\n        response.set_status(200)\n        response.body(\"\")\n        return\n\n    yield  # Process the request\n\n    # Add CORS headers to response\n    response.add_header(\"Access-Control-Allow-Origin\", \"*\")\n</code></pre>"}, {"location": "guides/middleware/#rate-limiting-middleware", "title": "Rate Limiting Middleware", "text": "<pre><code>import time\nfrom collections import defaultdict\n\n# Simple in-memory rate limiter\nrequest_counts = defaultdict(list)\n\nasync def rate_limit_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    client_ip = request.client_ip\n    current_time = time.time()\n\n    # Clean old requests (older than 1 minute)\n    request_counts[client_ip] = [\n        req_time for req_time in request_counts[client_ip]\n        if current_time - req_time &lt; 60\n    ]\n\n    # Check rate limit (60 requests per minute)\n    if len(request_counts[client_ip]) &gt;= 60:\n        response.set_status(429)\n        response.content_type(\"application/json\")\n        response.body('{\"error\": \"Rate limit exceeded\"}')\n        return\n\n    # Record this request\n    request_counts[client_ip].append(current_time)\n\n    yield  # Continue processing\n</code></pre>"}, {"location": "guides/middleware/#error-handling-in-middleware", "title": "Error Handling in Middleware", "text": ""}, {"location": "guides/middleware/#catching-exceptions", "title": "Catching Exceptions", "text": "<pre><code>async def error_handling_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    try:\n        yield  # Process the request\n    except ValueError as e:\n        response.set_status(400)\n        response.content_type(\"application/json\")\n        response.body(f'{{\"error\": \"Bad request: {str(e)}\"}}')\n    except Exception as e:\n        response.set_status(500)\n        response.content_type(\"application/json\")\n        response.body('{\"error\": \"Internal server error\"}')\n</code></pre>"}, {"location": "guides/middleware/#propagating-exceptions", "title": "Propagating Exceptions", "text": "<pre><code>async def logging_error_middleware(\n    request: Request = dependency()\n) -&gt; AsyncIterator[None]:\n    try:\n        yield\n    except Exception as e:\n        # Log the error\n        logger.error(f\"Error processing {request.method} {request.path}: {e}\")\n        # Re-raise to let other middleware handle it\n        raise\n</code></pre>"}, {"location": "guides/middleware/#middleware-with-dependencies", "title": "Middleware with Dependencies", "text": ""}, {"location": "guides/middleware/#using-services", "title": "Using Services", "text": "<pre><code>async def database_middleware(\n    request: Request = dependency(),\n    db_service: DatabaseService = dependency()\n) -&gt; AsyncIterator[None]:\n    # Start a database transaction\n    transaction = await db_service.begin_transaction()\n    request.context['db_transaction'] = transaction\n\n    try:\n        yield  # Process the request\n\n        # Commit the transaction\n        await transaction.commit()\n    except Exception:\n        # Rollback on error\n        await transaction.rollback()\n        raise\n</code></pre>"}, {"location": "guides/middleware/#configuration-based-middleware", "title": "Configuration-Based Middleware", "text": "<pre><code>class ConfigurableMiddleware:\n    def __init__(self, config: dict):\n        self.enabled = config.get('enabled', True)\n        self.log_level = config.get('log_level', 'INFO')\n\n    async def __call__(\n        self,\n        request: Request = dependency()\n    ) -&gt; AsyncIterator[None]:\n        if not self.enabled:\n            yield\n            return\n\n        if self.log_level == 'DEBUG':\n            print(f\"DEBUG: {request.method} {request.path}\")\n\n        yield\n\n# Register with configuration\nconfig = {'enabled': True, 'log_level': 'DEBUG'}\nmiddleware = ConfigurableMiddleware(config)\napp.add_middleware(middleware)\n</code></pre>"}, {"location": "guides/middleware/#middleware-order", "title": "Middleware Order", "text": "<p>Middleware executes in the order it's registered:</p> <pre><code># First middleware registered\nasync def first_middleware() -&gt; AsyncIterator[None]:\n    print(\"First: Before\")\n    yield\n    print(\"First: After\")\n\n# Second middleware registered  \nasync def second_middleware() -&gt; AsyncIterator[None]:\n    print(\"Second: Before\")\n    yield\n    print(\"Second: After\")\n\napp.add_middleware(first_middleware)\napp.add_middleware(second_middleware)\n\n# Output for a request:\n# First: Before\n# Second: Before\n# [Route handler executes]\n# Second: After\n# First: After\n</code></pre>"}, {"location": "guides/middleware/#plugin-based-middleware", "title": "Plugin-Based Middleware", "text": ""}, {"location": "guides/middleware/#registering-middleware-in-plugins", "title": "Registering Middleware in Plugins", "text": "<pre><code>class SecurityPlugin(Plugin):\n    async def on_app_startup(self, app: App = dependency()):\n        app.add_middleware(self.security_middleware)\n\n    async def security_middleware(\n        self,\n        request: Request = dependency(),\n        response: ResponseBuilder = dependency()\n    ) -&gt; AsyncIterator[None]:\n        # Add security headers\n        yield\n\n        response.add_header(\"X-Content-Type-Options\", \"nosniff\")\n        response.add_header(\"X-Frame-Options\", \"DENY\")\n        response.add_header(\"X-XSS-Protection\", \"1; mode=block\")\n</code></pre>"}, {"location": "guides/middleware/#conditional-middleware", "title": "Conditional Middleware", "text": "<pre><code>class ConditionalPlugin(Plugin):\n    async def on_app_startup(self, app: App = dependency()):\n        config = self.get_config()\n\n        if config.get('enable_auth', False):\n            app.add_middleware(self.auth_middleware)\n\n        if config.get('enable_logging', True):\n            app.add_middleware(self.logging_middleware)\n\n    async def auth_middleware(self) -&gt; AsyncIterator[None]:\n        # Authentication logic\n        yield\n\n    async def logging_middleware(self) -&gt; AsyncIterator[None]:\n        # Logging logic\n        yield\n</code></pre>"}, {"location": "guides/middleware/#advanced-middleware-patterns", "title": "Advanced Middleware Patterns", "text": ""}, {"location": "guides/middleware/#middleware-classes", "title": "Middleware Classes", "text": "<p>For complex middleware, use classes:</p> <pre><code>class AuthenticationMiddleware:\n    def __init__(self, secret_key: str, exempt_paths: list = None):\n        self.secret_key = secret_key\n        self.exempt_paths = exempt_paths or []\n\n    async def __call__(\n        self,\n        request: Request = dependency(),\n        response: ResponseBuilder = dependency()\n    ) -&gt; AsyncIterator[None]:\n        # Skip authentication for exempt paths\n        if request.path in self.exempt_paths:\n            yield\n            return\n\n        # Perform authentication\n        token = request.headers.get(\"Authorization\")\n        if not self.validate_token(token):\n            response.set_status(401)\n            response.body(\"Unauthorized\")\n            return\n\n        yield\n\n    def validate_token(self, token: str) -&gt; bool:\n        # Token validation logic\n        return token and token.startswith(\"Bearer \")\n\n# Register the middleware\nauth_middleware = AuthenticationMiddleware(\n    secret_key=\"your-secret-key\",\n    exempt_paths=[\"/health\", \"/login\"]\n)\napp.add_middleware(auth_middleware)\n</code></pre>"}, {"location": "guides/middleware/#middleware-factories", "title": "Middleware Factories", "text": "<p>Create middleware with different configurations:</p> <pre><code>def create_cors_middleware(\n    allowed_origins: list = None,\n    allowed_methods: list = None,\n    allowed_headers: list = None\n):\n    allowed_origins = allowed_origins or [\"*\"]\n    allowed_methods = allowed_methods or [\"GET\", \"POST\", \"PUT\", \"DELETE\"]\n    allowed_headers = allowed_headers or [\"Content-Type\", \"Authorization\"]\n\n    async def cors_middleware(\n        request: Request = dependency(),\n        response: ResponseBuilder = dependency()\n    ) -&gt; AsyncIterator[None]:\n        origin = request.headers.get(\"Origin\")\n\n        if request.method == \"OPTIONS\":\n            if \"*\" in allowed_origins or origin in allowed_origins:\n                response.add_header(\"Access-Control-Allow-Origin\", origin or \"*\")\n                response.add_header(\"Access-Control-Allow-Methods\", \", \".join(allowed_methods))\n                response.add_header(\"Access-Control-Allow-Headers\", \", \".join(allowed_headers))\n            response.set_status(200)\n            response.body(\"\")\n            return\n\n        yield\n\n        if \"*\" in allowed_origins or origin in allowed_origins:\n            response.add_header(\"Access-Control-Allow-Origin\", origin or \"*\")\n\n    return cors_middleware\n\n# Use the factory\ncors_middleware = create_cors_middleware(\n    allowed_origins=[\"http://localhost:3000\", \"https://myapp.com\"],\n    allowed_methods=[\"GET\", \"POST\"],\n    allowed_headers=[\"Content-Type\"]\n)\napp.add_middleware(cors_middleware)\n</code></pre>"}, {"location": "guides/middleware/#testing-middleware", "title": "Testing Middleware", "text": ""}, {"location": "guides/middleware/#unit-testing", "title": "Unit Testing", "text": "<pre><code>import pytest\nfrom unittest.mock import Mock, AsyncMock\n\*********************\nasync def test_auth_middleware():\n    # Create mocks\n    mock_request = Mock(spec=Request)\n    mock_request.headers = {\"Authorization\": \"Bearer valid-token\"}\n    mock_response = Mock(spec=ResponseBuilder)\n\n    # Create middleware\n    middleware = auth_middleware()\n\n    # Test with valid token\n    async for _ in middleware:\n        pass  # Should not raise or set error status\n\n    # Verify no error response was set\n    mock_response.set_status.assert_not_called()\n</code></pre>"}, {"location": "guides/middleware/#integration-testing", "title": "Integration Testing", "text": "<pre><code>import httpx\nimport pytest\n\*********************\nasync def test_middleware_integration():\n    async with httpx.AsyncClient(app=app, base_url=\"http://test\") as client:\n        # Test without auth header\n        response = await client.get(\"/protected\")\n        assert response.status_code == 401\n\n        # Test with auth header\n        headers = {\"Authorization\": \"Bearer valid-token\"}\n        response = await client.get(\"/protected\", headers=headers)\n        assert response.status_code == 200\n</code></pre>"}, {"location": "guides/middleware/#best-practices", "title": "Best Practices", "text": ""}, {"location": "guides/middleware/#1-keep-middleware-focused", "title": "1. Keep Middleware Focused", "text": "<p>Each middleware should have a single responsibility:</p> <pre><code># Good: Focused on authentication\nasync def auth_middleware() -&gt; AsyncIterator[None]:\n    # Only handle authentication\n    yield\n\n# Good: Focused on logging\nasync def logging_middleware() -&gt; AsyncIterator[None]:\n    # Only handle logging\n    yield\n\n# Bad: Too many responsibilities\nasync def everything_middleware() -&gt; AsyncIterator[None]:\n    # Authentication, logging, rate limiting, etc.\n    yield\n</code></pre>"}, {"location": "guides/middleware/#2-handle-errors-gracefully", "title": "2. <PERSON><PERSON><PERSON>", "text": "<pre><code>async def robust_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    try:\n        # Middleware logic\n        yield\n    except Exception as e:\n        # Log the error\n        logger.error(f\"Middleware error: {e}\")\n\n        # Don't break the request chain\n        if not response._headers_sent:\n            response.set_status(500)\n            response.body(\"Internal server error\")\n</code></pre>"}, {"location": "guides/middleware/#3-use-type-hints", "title": "3. Use Type Hints", "text": "<pre><code>from typing import AsyncIterator\n\nasync def typed_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    yield\n</code></pre>"}, {"location": "guides/middleware/#4-make-middleware-configurable", "title": "4. Make Middleware Configurable", "text": "<pre><code>def create_configurable_middleware(config: dict):\n    async def middleware(\n        request: Request = dependency()\n    ) -&gt; AsyncIterator[None]:\n        if config.get('enabled', True):\n            # Middleware logic\n            pass\n        yield\n\n    return middleware\n</code></pre>"}, {"location": "guides/middleware/#5-document-middleware-behavior", "title": "5. Document Middleware Behavior", "text": "<pre><code>async def documented_middleware(\n    request: Request = dependency(),\n    response: ResponseBuilder = dependency()\n) -&gt; AsyncIterator[None]:\n    \"\"\"\n    Authentication middleware that validates JWT tokens.\n\n    Checks for Authorization header with Bear<PERSON> token.\n    Sets 401 status if token is missing or invalid.\n    Adds user information to request.context if valid.\n\n    Exempt paths: /health, /login, /register\n    \"\"\"\n    yield\n</code></pre>"}, {"location": "guides/middleware/#next-steps", "title": "Next Steps", "text": "<ul> <li>Plugins - Learn how to create plugins that register middleware</li> <li>Dependency Injection - Use DI in middleware</li> <li>Error Handling - Handle errors in middleware</li> <li>Testing - Test your middleware effectively </li> </ul>"}, {"location": "guides/plugins/", "title": "Plugins", "text": "<p>Plugins are the heart of Serv's extensibility. They allow you to modularize your application, share functionality, and build reusable components. This guide covers everything you need to know about creating and using plugins in Serv.</p>"}, {"location": "guides/plugins/#what-are-plugins", "title": "What are Plugins?", "text": "<p>Plugins in Serv are Python classes that extend the <code>Plugin</code> base class and respond to application events. They can:</p> <ul> <li>Add routes to your application</li> <li>Register middleware</li> <li>Handle application lifecycle events</li> <li>Provide reusable functionality</li> <li>Be configured via YAML files</li> </ul>"}, {"location": "guides/plugins/#basic-plugin-structure", "title": "Basic Plugin Structure", "text": ""}, {"location": "guides/plugins/#simple-plugin", "title": "Simple Plugin", "text": "<p>Here's the simplest possible plugin:</p> <pre><code>from serv.plugins import Plugin\n\nclass MyPlugin(Plugin):\n    async def on_app_startup(self):\n        print(\"My plugin is starting up!\")\n\n    async def on_app_shutdown(self):\n        print(\"My plugin is shutting down!\")\n</code></pre>"}, {"location": "guides/plugins/#plugin-with-routes", "title": "Plugin with Routes", "text": "<p>Most plugins will add routes to your application:</p> <pre><code>from serv.plugins import Plugin\nfrom serv.plugins.routing import Router\nfrom serv.responses import ResponseBuilder\nfrom bevy import dependency\n\nclass HelloPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/hello\", self.hello_handler)\n        router.add_route(\"/hello/{name}\", self.hello_name_handler)\n\n    async def hello_handler(self, response: ResponseBuilder = dependency()):\n        response.content_type(\"text/plain\")\n        response.body(\"Hello from my plugin!\")\n\n    async def hello_name_handler(self, name: str, response: ResponseBuilder = dependency()):\n        response.content_type(\"text/plain\")\n        response.body(f\"Hello, {name}!\")\n</code></pre>"}, {"location": "guides/plugins/#plugin-events", "title": "Plugin Events", "text": "<p>Plugins respond to various application events. Here are the most common ones:</p>"}, {"location": "guides/plugins/#lifecycle-events", "title": "Lifecycle Events", "text": "<pre><code>class LifecyclePlugin(Plugin):\n    async def on_app_startup(self):\n        \"\"\"Called when the application starts\"\"\"\n        print(\"Application is starting up\")\n        # Initialize databases, connections, etc.\n\n    async def on_app_shutdown(self):\n        \"\"\"Called when the application shuts down\"\"\"\n        print(\"Application is shutting down\")\n        # Clean up resources, close connections, etc.\n</code></pre>"}, {"location": "guides/plugins/#request-events", "title": "Request Events", "text": "<pre><code>class RequestPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        \"\"\"Called at the beginning of each request\"\"\"\n        # Add routes, modify router, etc.\n        router.add_route(\"/my-route\", self.my_handler)\n\n    async def on_app_request_before_router(self, request: Request = dependency()):\n        \"\"\"Called before routing happens\"\"\"\n        # Log requests, modify headers, etc.\n        print(f\"Processing {request.method} {request.path}\")\n\n    async def on_app_request_after_router(self, request: Request = dependency(), error=None):\n        \"\"\"Called after routing (whether successful or not)\"\"\"\n        if error:\n            print(f\"Request failed: {error}\")\n        else:\n            print(f\"Request completed successfully\")\n\n    async def on_app_request_end(self, request: Request = dependency(), error=None):\n        \"\"\"Called at the end of each request\"\"\"\n        # Final cleanup, logging, etc.\n        print(f\"Request finished: {request.method} {request.path}\")\n</code></pre>"}, {"location": "guides/plugins/#plugin-configuration", "title": "Plugin Configuration", "text": ""}, {"location": "guides/plugins/#plugin-yaml-file", "title": "Plugin YAML File", "text": "<p>Create a <code>plugin.yaml</code> file to define your plugin's metadata and configuration:</p> <pre><code>name: My Awesome Plugin\ndescription: A plugin that does awesome things\nversion: 1.0.0\nauthor: Your Name\nentry: my_plugin.main:MyPlugin\n\n# Default settings\nsettings:\n  debug: false\n  api_key: \"\"\n  max_items: 100\n\n# Additional entry points\nentry_points:\n  - entry: my_plugin.admin:AdminPlugin\n    config:\n      admin_only: true\n\n# Middleware provided by this plugin\nmiddleware:\n  - entry: my_plugin.middleware:LoggingMiddleware\n    config:\n      log_level: \"INFO\"\n</code></pre>"}, {"location": "guides/plugins/#accessing-configuration", "title": "Accessing Configuration", "text": "<p>Access plugin configuration in your plugin code:</p> <pre><code>class ConfigurablePlugin(Plugin):\n    def __init__(self):\n        # Access plugin configuration\n        self.config = self.get_config()\n        self.debug = self.config.get('debug', False)\n        self.api_key = self.config.get('api_key', '')\n\n    async def on_app_startup(self):\n        if self.debug:\n            print(f\"Plugin starting with API key: {self.api_key}\")\n</code></pre>"}, {"location": "guides/plugins/#application-configuration-override", "title": "Application Configuration Override", "text": "<p>Users can override plugin settings in their <code>serv.config.yaml</code>:</p> <pre><code>plugins:\n  - plugin: my_plugin\n    settings:\n      debug: true\n      api_key: \"secret-key-123\"\n      max_items: 50\n</code></pre>"}, {"location": "guides/plugins/#advanced-plugin-patterns", "title": "Advanced Plugin Patterns", "text": ""}, {"location": "guides/plugins/#plugin-with-database", "title": "Plugin with Database", "text": "<pre><code>import sqlite3\nfrom contextlib import asynccontextmanager\n\nclass DatabasePlugin(Plugin):\n    def __init__(self):\n        self.db_path = self.get_config().get('db_path', 'app.db')\n        self.connection = None\n\n    async def on_app_startup(self):\n        \"\"\"Initialize database connection\"\"\"\n        self.connection = sqlite3.connect(self.db_path)\n        self._create_tables()\n\n    async def on_app_shutdown(self):\n        \"\"\"Close database connection\"\"\"\n        if self.connection:\n            self.connection.close()\n\n    def _create_tables(self):\n        \"\"\"Create necessary database tables\"\"\"\n        self.connection.execute(\"\"\"\n            CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY,\n                name TEXT NOT NULL,\n                email TEXT UNIQUE NOT NULL\n            )\n        \"\"\")\n        self.connection.commit()\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/users\", self.list_users)\n        router.add_route(\"/users/{user_id}\", self.get_user)\n\n    async def list_users(self, response: ResponseBuilder = dependency()):\n        cursor = self.connection.execute(\"SELECT * FROM users\")\n        users = cursor.fetchall()\n        response.content_type(\"application/json\")\n        response.body(json.dumps(users))\n\n    async def get_user(self, user_id: str, response: ResponseBuilder = dependency()):\n        cursor = self.connection.execute(\"SELECT * FROM users WHERE id = ?\", (user_id,))\n        user = cursor.fetchone()\n        if user:\n            response.content_type(\"application/json\")\n            response.body(json.dumps(user))\n        else:\n            response.set_status(404)\n            response.body(\"User not found\")\n</code></pre>"}, {"location": "guides/plugins/#plugin-with-services", "title": "Plugin with Services", "text": "<p>Create reusable services that can be injected into other parts of your application:</p> <pre><code>from bevy import dependency\n\nclass EmailService:\n    def __init__(self, smtp_host: str, smtp_port: int):\n        self.smtp_host = smtp_host\n        self.smtp_port = smtp_port\n\n    async def send_email(self, to: str, subject: str, body: str):\n        # Implementation for sending emails\n        print(f\"Sending email to {to}: {subject}\")\n\nclass EmailPlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        \"\"\"Register the email service\"\"\"\n        config = self.get_config()\n        email_service = EmailService(\n            smtp_host=config.get('smtp_host', 'localhost'),\n            smtp_port=config.get('smtp_port', 587)\n        )\n        container.instances[EmailService] = email_service\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/send-email\", self.send_email_handler)\n\n    async def send_email_handler(\n        self, \n        email_service: EmailService = dependency(),\n        response: ResponseBuilder = dependency()\n    ):\n        await email_service.send_email(\n            to=\"<EMAIL>\",\n            subject=\"Hello\",\n            body=\"Hello from Serv!\"\n        )\n        response.body(\"Email sent!\")\n</code></pre>"}, {"location": "guides/plugins/#plugin-with-middleware", "title": "Plugin with Middleware", "text": "<p>Plugins can register middleware:</p> <pre><code>from typing import AsyncIterator\n\nclass SecurityPlugin(Plugin):\n    async def on_app_startup(self, app: App = dependency()):\n        \"\"\"Register security middleware\"\"\"\n        app.add_middleware(self.security_middleware)\n\n    async def security_middleware(\n        self,\n        request: Request = dependency(),\n        response: ResponseBuilder = dependency()\n    ) -&gt; AsyncIterator[None]:\n        \"\"\"Add security headers to all responses\"\"\"\n        # Before request processing\n        if not self._is_secure_request(request):\n            response.set_status(403)\n            response.body(\"Forbidden\")\n            return\n\n        yield  # Process the request\n\n        # After request processing\n        response.add_header(\"X-Content-Type-Options\", \"nosniff\")\n        response.add_header(\"X-Frame-Options\", \"DENY\")\n        response.add_header(\"X-XSS-Protection\", \"1; mode=block\")\n\n    def _is_secure_request(self, request: Request) -&gt; bool:\n        # Implement your security logic\n        return True\n</code></pre>"}, {"location": "guides/plugins/#plugin-organization", "title": "Plugin Organization", "text": ""}, {"location": "guides/plugins/#directory-structure", "title": "Directory Structure", "text": "<p>Organize your plugins in a clear directory structure:</p> <pre><code>plugins/\n├── auth/\n│   ├── __init__.py\n│   ├── main.py          # Main plugin class\n│   ├── models.py        # Data models\n│   ├── handlers.py      # Route handlers\n│   ├── middleware.py    # Middleware\n│   └── plugin.yaml      # Plugin configuration\n├── blog/\n│   ├── __init__.py\n│   ├── main.py\n│   ├── models.py\n│   └── plugin.yaml\n└── api/\n    ├── __init__.py\n    ├── main.py\n    ├── v1/\n    │   ├── __init__.py\n    │   └── handlers.py\n    └── plugin.yaml\n</code></pre>"}, {"location": "guides/plugins/#multi-file-plugins", "title": "Multi-File Plugins", "text": "<p>For larger plugins, split functionality across multiple files:</p> <pre><code># plugins/blog/main.py\nfrom .handlers import BlogHandlers\nfrom .models import BlogStorage\n\nclass BlogPlugin(Plugin):\n    def __init__(self):\n        self.storage = BlogStorage()\n        self.handlers = BlogHandlers(self.storage)\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        self.handlers.register_routes(router)\n\n# plugins/blog/handlers.py\nclass BlogHandlers:\n    def __init__(self, storage):\n        self.storage = storage\n\n    def register_routes(self, router):\n        router.add_route(\"/blog\", self.list_posts)\n        router.add_route(\"/blog/{post_id}\", self.get_post)\n\n    async def list_posts(self, response: ResponseBuilder = dependency()):\n        # Implementation\n        pass\n\n    async def get_post(self, post_id: str, response: ResponseBuilder = dependency()):\n        # Implementation\n        pass\n</code></pre>"}, {"location": "guides/plugins/#plugin-loading", "title": "Plugin Loading", "text": ""}, {"location": "guides/plugins/#automatic-loading", "title": "Automatic Loading", "text": "<p>Serv can automatically load plugins from directories:</p> <pre><code>from serv import App\n\n# Load plugins from the ./plugins directory\napp = App(plugin_dir=\"./plugins\")\n</code></pre>"}, {"location": "guides/plugins/#manual-loading", "title": "Manual Loading", "text": "<p>You can also load plugins manually:</p> <pre><code>from serv import App\nfrom my_plugins import MyPlugin\n\napp = App()\napp.add_plugin(MyPlugin())\n</code></pre>"}, {"location": "guides/plugins/#configuration-based-loading", "title": "Configuration-Based Loading", "text": "<p>Load plugins via configuration:</p> <pre><code># serv.config.yaml\nplugins:\n  - plugin: auth\n    settings:\n      secret_key: \"your-secret-key\"\n  - plugin: blog\n    settings:\n      posts_per_page: 10\n  - entry: external_package.plugin:ExternalPlugin\n    config:\n      api_url: \"https://api.example.com\"\n</code></pre>"}, {"location": "guides/plugins/#plugin-dependencies", "title": "Plugin Dependencies", "text": ""}, {"location": "guides/plugins/#plugin-ordering", "title": "Plugin Ordering", "text": "<p>Sometimes plugins need to load in a specific order:</p> <pre><code>class DatabasePlugin(Plugin):\n    priority = 100  # Load early\n\n    async def on_app_startup(self):\n        # Set up database\n        pass\n\nclass UserPlugin(Plugin):\n    priority = 50  # Load after database\n    depends_on = ['database']\n\n    async def on_app_startup(self):\n        # Use database set up by DatabasePlugin\n        pass\n</code></pre>"}, {"location": "guides/plugins/#service-dependencies", "title": "Service Dependencies", "text": "<p>Use dependency injection to share services between plugins:</p> <pre><code>class DatabasePlugin(Plugin):\n    async def on_app_startup(self, container: Container = dependency()):\n        db = Database()\n        container.instances[Database] = db\n\nclass UserPlugin(Plugin):\n    async def on_app_request_begin(\n        self, \n        router: Router = dependency(),\n        db: Database = dependency()\n    ):\n        # Use the database service\n        router.add_route(\"/users\", lambda: self.list_users(db))\n</code></pre>"}, {"location": "guides/plugins/#testing-plugins", "title": "Testing Plugins", "text": ""}, {"location": "guides/plugins/#unit-testing", "title": "Unit Testing", "text": "<p>Test plugin functionality in isolation:</p> <pre><code>import pytest\nfrom unittest.mock import Mock\nfrom my_plugin import MyPlugin\n\*********************\nasync def test_plugin_startup():\n    plugin = MyPlugin()\n\n    # Mock dependencies\n    container = Mock()\n\n    await plugin.on_app_startup(container=container)\n\n    # Assert expected behavior\n    assert container.instances[SomeService] is not None\n</code></pre>"}, {"location": "guides/plugins/#integration-testing", "title": "Integration Testing", "text": "<p>Test plugins within a full application:</p> <pre><code>import pytest\nimport httpx\nfrom serv import App\nfrom my_plugin import MyPlugin\n\****************\ndef app():\n    app = App()\n    app.add_plugin(MyPlugin())\n    return app\n\*********************\nasync def test_plugin_routes(app):\n    async with httpx.AsyncClient(app=app, base_url=\"http://test\") as client:\n        response = await client.get(\"/my-route\")\n        assert response.status_code == 200\n</code></pre>"}, {"location": "guides/plugins/#best-practices", "title": "Best Practices", "text": ""}, {"location": "guides/plugins/#1-single-responsibility", "title": "1. Single Responsibility", "text": "<p>Each plugin should have a single, well-defined purpose:</p> <pre><code># Good: Focused on authentication\nclass AuthPlugin(Plugin):\n    pass\n\n# Good: Focused on blog functionality  \nclass BlogPlugin(Plugin):\n    pass\n\n# Bad: Too many responsibilities\nclass EverythingPlugin(Plugin):\n    pass\n</code></pre>"}, {"location": "guides/plugins/#2-configuration", "title": "2. Configuration", "text": "<p>Make your plugins configurable:</p> <pre><code>class MyPlugin(Plugin):\n    def __init__(self):\n        config = self.get_config()\n        self.enabled = config.get('enabled', True)\n        self.debug = config.get('debug', False)\n\n    async def on_app_request_begin(self, router: Router = dependency()):\n        if not self.enabled:\n            return\n\n        # Add routes only if enabled\n        router.add_route(\"/my-route\", self.handler)\n</code></pre>"}, {"location": "guides/plugins/#3-error-handling", "title": "3. <PERSON><PERSON><PERSON>", "text": "<p>Handle errors gracefully in your plugins:</p> <pre><code>class RobustPlugin(Plugin):\n    async def on_app_startup(self):\n        try:\n            # Initialize external service\n            self.service = ExternalService()\n        except Exception as e:\n            logger.error(f\"Failed to initialize service: {e}\")\n            # Provide fallback or disable functionality\n            self.service = None\n\n    async def my_handler(self, response: ResponseBuilder = dependency()):\n        if not self.service:\n            response.set_status(503)\n            response.body(\"Service unavailable\")\n            return\n\n        # Use the service\n        result = await self.service.do_something()\n        response.body(result)\n</code></pre>"}, {"location": "guides/plugins/#4-documentation", "title": "4. Documentation", "text": "<p>Document your plugins well:</p> <pre><code>class WellDocumentedPlugin(Plugin):\n    \"\"\"\n    A plugin that provides user authentication functionality.\n\n    Configuration:\n        secret_key (str): Secret key for JWT tokens\n        token_expiry (int): Token expiry time in seconds (default: 3600)\n\n    Events:\n        - Responds to app.startup to initialize JWT handler\n        - Responds to app.request.begin to add auth routes\n\n    Routes:\n        - POST /auth/login: User login\n        - POST /auth/logout: User logout\n        - GET /auth/profile: Get user profile\n    \"\"\"\n\n    async def on_app_startup(self):\n        \"\"\"Initialize JWT handler with configured secret key.\"\"\"\n        pass\n</code></pre>"}, {"location": "guides/plugins/#5-versioning", "title": "5. Versioning", "text": "<p>Version your plugins for compatibility:</p> <pre><code># plugin.yaml\nname: My Plugin\nversion: 2.1.0\nserv_version: \"&gt;=0.1.0,&lt;0.2.0\"\n</code></pre>"}, {"location": "guides/plugins/#publishing-plugins", "title": "Publishing Plugins", "text": ""}, {"location": "guides/plugins/#package-structure", "title": "Package Structure", "text": "<p>Structure your plugin as a Python package:</p> <pre><code>my-serv-plugin/\n├── setup.py\n├── README.md\n├── my_serv_plugin/\n│   ├── __init__.py\n│   ├── plugin.py\n│   └── handlers.py\n└── tests/\n    └── test_plugin.py\n</code></pre>"}, {"location": "guides/plugins/#setuppy", "title": "Setup.py", "text": "<pre><code>from setuptools import setup, find_packages\n\nsetup(\n    name=\"my-serv-plugin\",\n    version=\"1.0.0\",\n    packages=find_packages(),\n    install_requires=[\n        \"getserving&gt;=0.1.0\",\n    ],\n    entry_points={\n        \"serv.plugins\": [\n            \"my_plugin = my_serv_plugin.plugin:MyPlugin\",\n        ],\n    },\n)\n</code></pre>"}, {"location": "guides/plugins/#next-steps", "title": "Next Steps", "text": "<ul> <li>Middleware - Learn about middleware development</li> <li>Dependency Injection - Master the DI system</li> <li>Events - Understand the event system</li> <li>Testing - Test your plugins effectively </li> </ul>"}, {"location": "guides/routing/", "title": "Routing", "text": "<p>Serv provides a flexible and powerful routing system that supports both functional and class-based approaches. This guide covers everything you need to know about routing in Serv.</p>"}, {"location": "guides/routing/#basic-routing", "title": "Basic Routing", "text": ""}, {"location": "guides/routing/#function-based-routes", "title": "Function-Based Routes", "text": "<p>The simplest way to define routes is using functions:</p> <pre><code>from serv.responses import ResponseBuilder\nfrom serv.plugins import Plugin\nfrom serv.plugins.routing import Router\nfrom bevy import dependency\n\nasync def hello_world(response: ResponseBuilder = dependency()):\n    response.content_type(\"text/plain\")\n    response.body(\"Hello, World!\")\n\nasync def greet_user(name: str, response: ResponseBuilder = dependency()):\n    response.content_type(\"text/html\")\n    response.body(f\"&lt;h1&gt;Hello, {name}!&lt;/h1&gt;\")\n\nclass MyPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/\", hello_world)\n        router.add_route(\"/greet/{name}\", greet_user)\n</code></pre>"}, {"location": "guides/routing/#http-methods", "title": "HTTP Methods", "text": "<p>By default, routes respond to GET requests. You can specify different methods:</p> <pre><code>async def create_user(response: ResponseBuilder = dependency()):\n    response.body(\"User created!\")\n\nasync def update_user(user_id: str, response: ResponseBuilder = dependency()):\n    response.body(f\"User {user_id} updated!\")\n\nasync def delete_user(user_id: str, response: ResponseBuilder = dependency()):\n    response.body(f\"User {user_id} deleted!\")\n\n# In your plugin\nrouter.add_route(\"/users\", create_user, methods=[\"POST\"])\nrouter.add_route(\"/users/{user_id}\", update_user, methods=[\"PUT\"])\nrouter.add_route(\"/users/{user_id}\", delete_user, methods=[\"DELETE\"])\n</code></pre>"}, {"location": "guides/routing/#multiple-methods-on-one-route", "title": "Multiple Methods on One Route", "text": "<p>You can handle multiple HTTP methods with a single handler:</p> <pre><code>from serv.requests import Request\n\nasync def user_handler(user_id: str, request: Request, response: ResponseBuilder = dependency()):\n    if request.method == \"GET\":\n        response.body(f\"Getting user {user_id}\")\n    elif request.method == \"PUT\":\n        response.body(f\"Updating user {user_id}\")\n    elif request.method == \"DELETE\":\n        response.body(f\"Deleting user {user_id}\")\n\nrouter.add_route(\"/users/{user_id}\", user_handler, methods=[\"GET\", \"PUT\", \"DELETE\"])\n</code></pre>"}, {"location": "guides/routing/#path-parameters", "title": "Path Parameters", "text": ""}, {"location": "guides/routing/#basic-parameters", "title": "Basic Parameters", "text": "<p>Path parameters are defined using curly braces and automatically passed to your handler:</p> <pre><code>async def get_post(post_id: str, response: ResponseBuilder = dependency()):\n    response.body(f\"Post ID: {post_id}\")\n\nrouter.add_route(\"/posts/{post_id}\", get_post)\n</code></pre>"}, {"location": "guides/routing/#multiple-parameters", "title": "Multiple Parameters", "text": "<p>You can have multiple path parameters:</p> <pre><code>async def get_comment(post_id: str, comment_id: str, response: ResponseBuilder = dependency()):\n    response.body(f\"Post {post_id}, Comment {comment_id}\")\n\nrouter.add_route(\"/posts/{post_id}/comments/{comment_id}\", get_comment)\n</code></pre>"}, {"location": "guides/routing/#type-conversion", "title": "Type Conversion", "text": "<p>Path parameters are always strings, but you can convert them in your handler:</p> <pre><code>async def get_post_by_id(post_id: str, response: ResponseBuilder = dependency()):\n    try:\n        post_id_int = int(post_id)\n        # Use the integer ID\n        response.body(f\"Post ID as integer: {post_id_int}\")\n    except ValueError:\n        response.set_status(400)\n        response.body(\"Invalid post ID\")\n\nrouter.add_route(\"/posts/{post_id}\", get_post_by_id)\n</code></pre>"}, {"location": "guides/routing/#class-based-routes", "title": "Class-Based Routes", "text": "<p>For more complex routing scenarios, you can use class-based routes:</p> <pre><code>from serv.routes import Route\nfrom serv.requests import Request, GetRequest, PostRequest\n\nclass UserRoute(Route):\n    async def get_user(self, request: GetRequest, response: ResponseBuilder = dependency()):\n        \"\"\"Handle GET requests\"\"\"\n        response.body(\"Getting user information\")\n\n    async def create_user(self, request: PostRequest, response: ResponseBuilder = dependency()):\n        \"\"\"Handle POST requests\"\"\"\n        response.body(\"Creating new user\")\n\n    async def handle_any_method(self, request: Request, response: ResponseBuilder = dependency()):\n        \"\"\"Handle any HTTP method\"\"\"\n        response.body(f\"Handling {request.method} request\")\n\n# Register the route class\nrouter.add_route(\"/users\", UserRoute)\n</code></pre>"}, {"location": "guides/routing/#method-detection", "title": "Method Detection", "text": "<p>Serv automatically detects which methods to handle based on the parameter types:</p> <ul> <li><code>GetRequest</code> → GET requests</li> <li><code>PostRequest</code> → POST requests  </li> <li><code>PutRequest</code> → PUT requests</li> <li><code>DeleteRequest</code> → DELETE requests</li> <li><code>Request</code> → Any HTTP method</li> </ul>"}, {"location": "guides/routing/#form-handling", "title": "Form Handling", "text": "<p>Serv provides automatic form handling with class-based routes:</p> <pre><code>from serv.routes import Form\nfrom dataclasses import dataclass\n\n@dataclass\nclass UserForm(Form):\n    name: str\n    email: str\n    age: int = 0  # Optional field with default\n\nclass UserRoute(Route):\n    async def create_user(self, form: UserForm, response: ResponseBuilder = dependency()):\n        \"\"\"Automatically handles POST requests with form data\"\"\"\n        response.body(f\"Created user: {form.name} ({form.email})\")\n\nrouter.add_route(\"/users\", UserRoute)\n</code></pre>"}, {"location": "guides/routing/#custom-form-methods", "title": "Custom Form Methods", "text": "<p>You can specify which HTTP method a form should handle:</p> <pre><code>@dataclass\nclass UpdateUserForm(Form):\n    name: str\n    email: str\n\n    __form_method__ = \"PUT\"  # Handle PUT requests instead of POST\n\nclass UserRoute(Route):\n    async def update_user(self, form: UpdateUserForm, response: ResponseBuilder = dependency()):\n        response.body(f\"Updated user: {form.name}\")\n</code></pre>"}, {"location": "guides/routing/#advanced-routing", "title": "Advanced Routing", "text": ""}, {"location": "guides/routing/#route-priorities", "title": "Route Priorities", "text": "<p>Routes are matched in the order they're added. More specific routes should be added before more general ones:</p> <pre><code># Add specific routes first\nrouter.add_route(\"/users/admin\", admin_handler)\nrouter.add_route(\"/users/profile\", profile_handler)\n\n# Add parameterized routes last\nrouter.add_route(\"/users/{user_id}\", user_handler)\n</code></pre>"}, {"location": "guides/routing/#sub-routers", "title": "Sub-Routers", "text": "<p>You can create sub-routers for organizing complex applications:</p> <pre><code># Create separate routers for different sections\napi_router = Router()\nadmin_router = Router()\n\n# Add routes to sub-routers\napi_router.add_route(\"/users\", api_users_handler)\napi_router.add_route(\"/posts\", api_posts_handler)\n\nadmin_router.add_route(\"/dashboard\", admin_dashboard)\nadmin_router.add_route(\"/users\", admin_users)\n\n# Add sub-routers to main router\nclass MyPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_router(api_router, prefix=\"/api\")\n        router.add_router(admin_router, prefix=\"/admin\")\n</code></pre>"}, {"location": "guides/routing/#conditional-routing-with-middleware", "title": "Conditional Routing with Middleware", "text": "<p>You can use middleware to conditionally add routes:</p> <pre><code>from typing import AsyncIterator\n\nasync def conditional_routing_middleware(\n    router: Router = dependency(),\n    request: Request = dependency()\n) -&gt; AsyncIterator[None]:\n    # Add different routes based on conditions\n    if request.headers.get(\"X-API-Version\") == \"v2\":\n        router.add_route(\"/api/users\", users_v2_handler)\n    else:\n        router.add_route(\"/api/users\", users_v1_handler)\n\n    yield\n\napp.add_middleware(conditional_routing_middleware)\n</code></pre>"}, {"location": "guides/routing/#route-settings", "title": "Route Settings", "text": "<p>You can pass additional settings to routes that can be accessed in handlers:</p> <pre><code>from serv.routing import RouteSettings\n\nasync def protected_handler(\n    settings: RouteSettings = dependency(),\n    response: ResponseBuilder = dependency()\n):\n    if settings.require_auth:\n        # Check authentication\n        pass\n    response.body(\"Protected content\")\n\n# Add route with settings\nrouter.add_route(\n    \"/protected\", \n    protected_handler,\n    settings={\"require_auth\": True, \"roles\": [\"admin\"]}\n)\n</code></pre>"}, {"location": "guides/routing/#error-handling", "title": "Erro<PERSON>", "text": ""}, {"location": "guides/routing/#route-level-error-handling", "title": "Route-Level Error Handling", "text": "<p>Handle errors within your route handlers:</p> <pre><code>from serv.exceptions import HTTPNotFoundException\n\nasync def get_user(user_id: str, response: ResponseBuilder = dependency()):\n    user = find_user(user_id)\n    if not user:\n        raise HTTPNotFoundException(f\"User {user_id} not found\")\n\n    response.body(f\"User: {user.name}\")\n</code></pre>"}, {"location": "guides/routing/#custom-error-handlers", "title": "Custom Error Handlers", "text": "<p>Register custom error handlers for specific exceptions:</p> <pre><code>async def not_found_handler(\n    error: HTTPNotFoundException,\n    response: ResponseBuilder = dependency()\n):\n    response.set_status(404)\n    response.content_type(\"application/json\")\n    response.body('{\"error\": \"Resource not found\"}')\n\napp.add_error_handler(HTTPNotFoundException, not_found_handler)\n</code></pre>"}, {"location": "guides/routing/#response-types", "title": "Response Types", "text": ""}, {"location": "guides/routing/#different-response-types", "title": "Different Response Types", "text": "<p>Serv supports various response types through annotations:</p> <pre><code>from typing import Annotated\nfrom serv.responses import JsonResponse, HtmlResponse, RedirectResponse\n\nclass ApiRoute(Route):\n    async def get_data(self) -&gt; Annotated[dict, JsonResponse]:\n        return {\"message\": \"Hello, <PERSON>!\"}\n\n    async def get_page(self) -&gt; Annotated[str, HtmlResponse]:\n        return \"&lt;h1&gt;Hello, HTML!&lt;/h1&gt;\"\n\n    async def redirect_user(self) -&gt; Annotated[str, RedirectResponse]:\n        return \"/dashboard\"\n</code></pre>"}, {"location": "guides/routing/#template-responses", "title": "Template Responses", "text": "<p>Use Jinja2 templates for HTML responses:</p> <pre><code>from serv.responses import Jinja2Response\n\nclass PageRoute(Route):\n    async def home_page(self) -&gt; Annotated[tuple[str, dict], Jinja2Response]:\n        return \"home.html\", {\n            \"title\": \"Welcome\",\n            \"user\": {\"name\": \"<PERSON>\"}\n        }\n</code></pre>"}, {"location": "guides/routing/#best-practices", "title": "Best Practices", "text": ""}, {"location": "guides/routing/#1-organize-routes-by-feature", "title": "1. Organize Routes by Feature", "text": "<p>Group related routes into plugins:</p> <pre><code>class UserPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/users\", UserListRoute)\n        router.add_route(\"/users/{user_id}\", UserDetailRoute)\n\nclass PostPlugin(Plugin):\n    async def on_app_request_begin(self, router: Router = dependency()):\n        router.add_route(\"/posts\", PostListRoute)\n        router.add_route(\"/posts/{post_id}\", PostDetailRoute)\n</code></pre>"}, {"location": "guides/routing/#2-use-type-hints", "title": "2. Use Type Hints", "text": "<p>Always use type hints for better IDE support and documentation:</p> <pre><code>async def get_user(\n    user_id: str,\n    response: ResponseBuilder = dependency()\n) -&gt; None:\n    # Implementation\n</code></pre>"}, {"location": "guides/routing/#3-validate-input", "title": "3. Validate Input", "text": "<p>Always validate and sanitize input parameters:</p> <pre><code>import re\n\nasync def get_user(user_id: str, response: ResponseBuilder = dependency()):\n    # Validate user_id format\n    if not re.match(r'^\\d+$', user_id):\n        response.set_status(400)\n        response.body(\"Invalid user ID format\")\n        return\n\n    # Continue with valid input\n</code></pre>"}, {"location": "guides/routing/#4-use-descriptive-route-names", "title": "4. Use Descriptive Route Names", "text": "<p>Make your routes self-documenting:</p> <pre><code># Good\nrouter.add_route(\"/api/v1/users/{user_id}/posts\", get_user_posts)\n\n# Better with clear handler name\nasync def get_posts_by_user_id(user_id: str, response: ResponseBuilder = dependency()):\n    pass\n</code></pre>"}, {"location": "guides/routing/#5-handle-edge-cases", "title": "5. <PERSON><PERSON>", "text": "<p>Always consider edge cases in your routes:</p> <pre><code>async def get_user(user_id: str, response: ResponseBuilder = dependency()):\n    # Handle empty or invalid IDs\n    if not user_id or not user_id.strip():\n        response.set_status(400)\n        response.body(\"User ID is required\")\n        return\n\n    try:\n        user_id_int = int(user_id)\n        if user_id_int &lt;= 0:\n            response.set_status(400)\n            response.body(\"User ID must be positive\")\n            return\n    except ValueError:\n        response.set_status(400)\n        response.body(\"User ID must be a number\")\n        return\n\n    # Continue with valid input\n</code></pre>"}, {"location": "guides/routing/#testing-routes", "title": "Testing Routes", "text": ""}, {"location": "guides/routing/#unit-testing", "title": "Unit Testing", "text": "<p>Test your route handlers in isolation:</p> <pre><code>import pytest\nfrom serv.responses import ResponseBuilder\nfrom unittest.mock import Mock\n\*********************\nasync def test_hello_world():\n    response = Mock(spec=ResponseBuilder)\n\n    await hello_world(response)\n\n    response.content_type.assert_called_with(\"text/plain\")\n    response.body.assert_called_with(\"Hello, World!\")\n</code></pre>"}, {"location": "guides/routing/#integration-testing", "title": "Integration Testing", "text": "<p>Test complete request/response cycles:</p> <pre><code>import httpx\nimport pytest\n\*********************\nasync def test_user_endpoint():\n    async with httpx.AsyncClient(app=app, base_url=\"http://test\") as client:\n        response = await client.get(\"/users/123\")\n        assert response.status_code == 200\n        assert \"User 123\" in response.text\n</code></pre>"}, {"location": "guides/routing/#next-steps", "title": "Next Steps", "text": "<ul> <li>Dependency Injection - Learn how to inject services into your routes</li> <li>Request Handling - Master request processing</li> <li>Response Building - Create rich responses</li> <li>Forms and File Uploads - Handle form data and file uploads </li> </ul>"}, {"location": "reference/SUMMARY/", "title": "SUMMARY", "text": "<ul> <li>serv<ul> <li>additional_context</li> <li>app</li> <li>bundled<ul> <li>plugins<ul> <li>welcome<ul> <li>welcome</li> </ul> </li> </ul> </li> </ul> </li> <li>cli<ul> <li>commands</li> <li>main</li> <li>parser</li> <li>utils</li> </ul> </li> <li>config</li> <li>exceptions</li> <li>injectors</li> <li>multipart_parser</li> <li>plugins<ul> <li>importer</li> <li>loader</li> <li>middleware</li> <li>plugins</li> <li>router_plugin</li> </ul> </li> <li>requests</li> <li>responses</li> <li>routes</li> <li>routing</li> <li>serv</li> </ul> </li> </ul>"}, {"location": "reference/serv/", "title": "serv", "text": ""}, {"location": "reference/serv/#serv", "title": "serv", "text": ""}, {"location": "reference/serv/#serv.App", "title": "App", "text": "<pre><code>App(\n    *,\n    config: str = \"./serv.config.yaml\",\n    plugin_dir: str = \"./plugins\",\n    dev_mode: bool = False,\n)\n</code></pre> <p>This is the main class for an ASGI application.</p> <p>It is responsible for handling the incoming requests and delegating them to the appropriate routes.</p> <p>Initialize a new Serv application.</p> <p>Parameters:</p> Name Type Description Default <code>config</code> <code>str</code> <p>configuration dictionary (usually from serv.config.yaml)</p> <code>'./serv.config.yaml'</code> <code>plugin_dir</code> <code>str</code> <p>directory to search for plugins (default: './plugins')</p> <code>'./plugins'</code> <code>dev_mode</code> <code>bool</code> <p>whether to run in development mode (default: False)</p> <code>False</code> Source code in <code>serv/app.py</code> <pre><code>def __init__(\n    self,\n    *,\n    config: str = \"./serv.config.yaml\",\n    plugin_dir: str = \"./plugins\",\n    dev_mode: bool = False,\n):\n    \"\"\"Initialize a new Serv application.\n\n    Args:\n        config: configuration dictionary (usually from serv.config.yaml)\n        plugin_dir: directory to search for plugins (default: './plugins')\n        dev_mode: whether to run in development mode (default: False)\n    \"\"\"\n    self._config = self._load_config(config)\n    self._dev_mode = dev_mode\n    self._registry = get_registry()\n    self._container = self._registry.create_container()\n    self._async_exit_stack = contextlib.AsyncExitStack()\n    self._error_handlers: dict[\n        type[Exception], Callable[[Exception], Awaitable[None]]\n    ] = {}\n    self._middleware = []\n\n    self._plugin_loader = Importer(plugin_dir)\n    self._plugins: dict[Path, list[Plugin]] = defaultdict(list)\n\n    # Initialize the plugin loader\n    self._plugin_loader_instance = PluginLoader(self, self._plugin_loader)\n\n    self._emit = EventEmitter(self._plugins)\n\n    self._init_container()\n    self._register_default_error_handlers()\n    self._init_plugins(self._config.get(\"plugins\", []))\n</code></pre>"}, {"location": "reference/serv/#serv.App.load_middleware", "title": "load_middleware", "text": "<pre><code>load_middleware(\n    package_name: str, namespace: str = None\n) -&gt; bool\n</code></pre> <p>Load middleware from a package name.</p> <p>Parameters:</p> Name Type Description Default <code>package_name</code> <code>str</code> <p>The name of the package to load</p> required <code>namespace</code> <code>str</code> <p>Optional namespace to restrict search</p> <code>None</code> <p>Returns:</p> Type Description <code>bool</code> <p>True if the middleware was loaded successfully</p> Source code in <code>serv/app.py</code> <pre><code>def load_middleware(self, package_name: str, namespace: str = None) -&gt; bool:\n    \"\"\"Load middleware from a package name.\n\n    Args:\n        package_name: The name of the package to load\n        namespace: Optional namespace to restrict search\n\n    Returns:\n        True if the middleware was loaded successfully\n    \"\"\"\n    success, middleware_factory = (\n        self._plugin_loader_instance.load_middleware_from_package(\n            package_name, namespace\n        )\n    )\n    if success and middleware_factory:\n        self.add_middleware(middleware_factory)\n        return True\n    return False\n</code></pre>"}, {"location": "reference/serv/#serv.App.load_middleware_packages", "title": "load_middleware_packages", "text": "<pre><code>load_middleware_packages() -&gt; int\n</code></pre> <p>Load all middleware packages from the plugin directories.</p> <p>Returns:</p> Type Description <code>int</code> <p>Number of middleware packages loaded</p> Source code in <code>serv/app.py</code> <pre><code>def load_middleware_packages(self) -&gt; int:\n    \"\"\"Load all middleware packages from the plugin directories.\n\n    Returns:\n        Number of middleware packages loaded\n    \"\"\"\n    middleware_count = 0\n    plugins_dir = Path(self._plugin_loader.plugin_dir)\n\n    if not plugins_dir.exists():\n        logger.warning(f\"Plugin directory {plugins_dir} does not exist.\")\n        return 0\n\n    for plugin_dir in plugins_dir.iterdir():\n        if plugin_dir.is_dir() and not plugin_dir.name.startswith(\"_\"):\n            if self.load_middleware(plugin_dir.name):\n                middleware_count += 1\n\n    return middleware_count\n</code></pre>"}, {"location": "reference/serv/#serv.App.load_plugin", "title": "load_plugin", "text": "<pre><code>load_plugin(\n    package_name: str, namespace: str = None\n) -&gt; bool\n</code></pre> <p>Load a plugin from a package name.</p> <p>Parameters:</p> Name Type Description Default <code>package_name</code> <code>str</code> <p>The name of the package to load</p> required <code>namespace</code> <code>str</code> <p>Optional namespace to restrict search</p> <code>None</code> <p>Returns:</p> Type Description <code>bool</code> <p>True if the plugin was loaded successfully</p> Source code in <code>serv/app.py</code> <pre><code>def load_plugin(self, package_name: str, namespace: str = None) -&gt; bool:\n    \"\"\"Load a plugin from a package name.\n\n    Args:\n        package_name: The name of the package to load\n        namespace: Optional namespace to restrict search\n\n    Returns:\n        True if the plugin was loaded successfully\n    \"\"\"\n    success, plugin = self._plugin_loader_instance.load_plugin(\n        package_name, namespace\n    )\n    if success and plugin:\n        self.add_plugin(plugin)\n        return True\n    return False\n</code></pre>"}, {"location": "reference/serv/#serv.App.load_plugins", "title": "load_plugins", "text": "<pre><code>load_plugins() -&gt; int\n</code></pre> <p>Load all plugins from the plugin directories.</p> <p>Returns:</p> Type Description <code>int</code> <p>Number of plugins loaded</p> Source code in <code>serv/app.py</code> <pre><code>def load_plugins(self) -&gt; int:\n    \"\"\"Load all plugins from the plugin directories.\n\n    Returns:\n        Number of plugins loaded\n    \"\"\"\n    plugins_count = 0\n    plugins_dir = Path(self._plugin_loader.plugin_dir)\n\n    if not plugins_dir.exists():\n        logger.warning(f\"Plugin directory {plugins_dir} does not exist.\")\n        return 0\n\n    for plugin_dir in plugins_dir.iterdir():\n        if plugin_dir.is_dir() and not plugin_dir.name.startswith(\"_\"):\n            if self.load_plugin(plugin_dir.name):\n                plugins_count += 1\n\n    return plugins_count\n</code></pre>"}, {"location": "reference/serv/additional_context/", "title": "additional_context", "text": ""}, {"location": "reference/serv/additional_context/#serv.additional_context", "title": "serv.additional_context", "text": ""}, {"location": "reference/serv/additional_context/#serv.additional_context.ExceptionContext", "title": "ExceptionContext", "text": "<pre><code>ExceptionContext(exception: type[Exception] = Exception)\n</code></pre> <p>Context manager for adding additional context to exceptions.</p> <p>This can be used to add notes or perform additional actions when an exception is raised within the context.</p> Example <p>with ExceptionContext(ValueError).apply_note(\"Additional context\"):     raise ValueError(\"Original error message\")</p> Example <p>with ExceptionContext().capture(lambda exc: print(f\"Captured: {exc}\")):     raise ValueError(\"Original error message\")</p> Example <p>with ExceptionContext(ValueError).capture(lambda exc: print(f\"Captured: {exc}\")).apply_note(\"Additional context\"):     raise ValueError(\"Original error message\")</p> Source code in <code>serv/additional_context.py</code> <pre><code>def __init__(self, exception: type[Exception] = Exception):\n    self.exception_type = exception\n    self.capture_callback = None\n    self.note = None\n</code></pre>"}, {"location": "reference/serv/app/", "title": "app", "text": ""}, {"location": "reference/serv/app/#serv.app", "title": "serv.app", "text": ""}, {"location": "reference/serv/app/#serv.app.App", "title": "App", "text": "<pre><code>App(\n    *,\n    config: str = \"./serv.config.yaml\",\n    plugin_dir: str = \"./plugins\",\n    dev_mode: bool = False,\n)\n</code></pre> <p>This is the main class for an ASGI application.</p> <p>It is responsible for handling the incoming requests and delegating them to the appropriate routes.</p> <p>Initialize a new Serv application.</p> <p>Parameters:</p> Name Type Description Default <code>config</code> <code>str</code> <p>configuration dictionary (usually from serv.config.yaml)</p> <code>'./serv.config.yaml'</code> <code>plugin_dir</code> <code>str</code> <p>directory to search for plugins (default: './plugins')</p> <code>'./plugins'</code> <code>dev_mode</code> <code>bool</code> <p>whether to run in development mode (default: False)</p> <code>False</code> Source code in <code>serv/app.py</code> <pre><code>def __init__(\n    self,\n    *,\n    config: str = \"./serv.config.yaml\",\n    plugin_dir: str = \"./plugins\",\n    dev_mode: bool = False,\n):\n    \"\"\"Initialize a new Serv application.\n\n    Args:\n        config: configuration dictionary (usually from serv.config.yaml)\n        plugin_dir: directory to search for plugins (default: './plugins')\n        dev_mode: whether to run in development mode (default: False)\n    \"\"\"\n    self._config = self._load_config(config)\n    self._dev_mode = dev_mode\n    self._registry = get_registry()\n    self._container = self._registry.create_container()\n    self._async_exit_stack = contextlib.AsyncExitStack()\n    self._error_handlers: dict[\n        type[Exception], Callable[[Exception], Awaitable[None]]\n    ] = {}\n    self._middleware = []\n\n    self._plugin_loader = Importer(plugin_dir)\n    self._plugins: dict[Path, list[Plugin]] = defaultdict(list)\n\n    # Initialize the plugin loader\n    self._plugin_loader_instance = PluginLoader(self, self._plugin_loader)\n\n    self._emit = EventEmitter(self._plugins)\n\n    self._init_container()\n    self._register_default_error_handlers()\n    self._init_plugins(self._config.get(\"plugins\", []))\n</code></pre>"}, {"location": "reference/serv/app/#serv.app.App.load_middleware", "title": "load_middleware", "text": "<pre><code>load_middleware(\n    package_name: str, namespace: str = None\n) -&gt; bool\n</code></pre> <p>Load middleware from a package name.</p> <p>Parameters:</p> Name Type Description Default <code>package_name</code> <code>str</code> <p>The name of the package to load</p> required <code>namespace</code> <code>str</code> <p>Optional namespace to restrict search</p> <code>None</code> <p>Returns:</p> Type Description <code>bool</code> <p>True if the middleware was loaded successfully</p> Source code in <code>serv/app.py</code> <pre><code>def load_middleware(self, package_name: str, namespace: str = None) -&gt; bool:\n    \"\"\"Load middleware from a package name.\n\n    Args:\n        package_name: The name of the package to load\n        namespace: Optional namespace to restrict search\n\n    Returns:\n        True if the middleware was loaded successfully\n    \"\"\"\n    success, middleware_factory = (\n        self._plugin_loader_instance.load_middleware_from_package(\n            package_name, namespace\n        )\n    )\n    if success and middleware_factory:\n        self.add_middleware(middleware_factory)\n        return True\n    return False\n</code></pre>"}, {"location": "reference/serv/app/#serv.app.App.load_middleware_packages", "title": "load_middleware_packages", "text": "<pre><code>load_middleware_packages() -&gt; int\n</code></pre> <p>Load all middleware packages from the plugin directories.</p> <p>Returns:</p> Type Description <code>int</code> <p>Number of middleware packages loaded</p> Source code in <code>serv/app.py</code> <pre><code>def load_middleware_packages(self) -&gt; int:\n    \"\"\"Load all middleware packages from the plugin directories.\n\n    Returns:\n        Number of middleware packages loaded\n    \"\"\"\n    middleware_count = 0\n    plugins_dir = Path(self._plugin_loader.plugin_dir)\n\n    if not plugins_dir.exists():\n        logger.warning(f\"Plugin directory {plugins_dir} does not exist.\")\n        return 0\n\n    for plugin_dir in plugins_dir.iterdir():\n        if plugin_dir.is_dir() and not plugin_dir.name.startswith(\"_\"):\n            if self.load_middleware(plugin_dir.name):\n                middleware_count += 1\n\n    return middleware_count\n</code></pre>"}, {"location": "reference/serv/app/#serv.app.App.load_plugin", "title": "load_plugin", "text": "<pre><code>load_plugin(\n    package_name: str, namespace: str = None\n) -&gt; bool\n</code></pre> <p>Load a plugin from a package name.</p> <p>Parameters:</p> Name Type Description Default <code>package_name</code> <code>str</code> <p>The name of the package to load</p> required <code>namespace</code> <code>str</code> <p>Optional namespace to restrict search</p> <code>None</code> <p>Returns:</p> Type Description <code>bool</code> <p>True if the plugin was loaded successfully</p> Source code in <code>serv/app.py</code> <pre><code>def load_plugin(self, package_name: str, namespace: str = None) -&gt; bool:\n    \"\"\"Load a plugin from a package name.\n\n    Args:\n        package_name: The name of the package to load\n        namespace: Optional namespace to restrict search\n\n    Returns:\n        True if the plugin was loaded successfully\n    \"\"\"\n    success, plugin = self._plugin_loader_instance.load_plugin(\n        package_name, namespace\n    )\n    if success and plugin:\n        self.add_plugin(plugin)\n        return True\n    return False\n</code></pre>"}, {"location": "reference/serv/app/#serv.app.App.load_plugins", "title": "load_plugins", "text": "<pre><code>load_plugins() -&gt; int\n</code></pre> <p>Load all plugins from the plugin directories.</p> <p>Returns:</p> Type Description <code>int</code> <p>Number of plugins loaded</p> Source code in <code>serv/app.py</code> <pre><code>def load_plugins(self) -&gt; int:\n    \"\"\"Load all plugins from the plugin directories.\n\n    Returns:\n        Number of plugins loaded\n    \"\"\"\n    plugins_count = 0\n    plugins_dir = Path(self._plugin_loader.plugin_dir)\n\n    if not plugins_dir.exists():\n        logger.warning(f\"Plugin directory {plugins_dir} does not exist.\")\n        return 0\n\n    for plugin_dir in plugins_dir.iterdir():\n        if plugin_dir.is_dir() and not plugin_dir.name.startswith(\"_\"):\n            if self.load_plugin(plugin_dir.name):\n                plugins_count += 1\n\n    return plugins_count\n</code></pre>"}, {"location": "reference/serv/cli/", "title": "cli", "text": ""}, {"location": "reference/serv/cli/#serv.cli", "title": "serv.cli", "text": "<p>Serv CLI package.</p> <p>This package contains the modularized CLI components for the Serv web framework.</p>"}, {"location": "reference/serv/config/", "title": "config", "text": ""}, {"location": "reference/serv/config/#serv.config", "title": "serv.config", "text": ""}, {"location": "reference/serv/config/#serv.config.ServConfigError", "title": "ServConfigError", "text": "<p>               Bases: <code>Exception</code></p> <p>Custom exception for configuration errors.</p>"}, {"location": "reference/serv/config/#serv.config.import_from_string", "title": "import_from_string", "text": "<pre><code>import_from_string(import_str: str) -&gt; Any\n</code></pre> <p>Import a class, function, or variable from a module by string.</p> <p>Parameters:</p> Name Type Description Default <code>import_str</code> <code>str</code> <p>String in the format \"module.path:symbol\"</p> required <p>Returns:</p> Type Description <code>Any</code> <p>The imported object</p> <p>Raises:</p> Type Description <code>ServConfigError</code> <p>If the import failed.</p> Source code in <code>serv/config.py</code> <pre><code>def import_from_string(import_str: str) -&gt; Any:\n    \"\"\"\n    Import a class, function, or variable from a module by string.\n\n    Args:\n        import_str: String in the format \"module.path:symbol\"\n\n    Returns:\n        The imported object\n\n    Raises:\n        ServConfigError: If the import failed.\n    \"\"\"\n    if \":\" not in import_str:\n        raise ServConfigError(\n            f\"Invalid import string format '{import_str}'. Expected 'module.path:symbol'.\"\n        )\n\n    module_path, object_path = import_str.split(\":\", 1)\n\n    try:\n        module = importlib.import_module(module_path)\n\n        # Handle nested attributes\n        target = module\n        for part in object_path.split(\".\"):\n            target = getattr(target, part)\n\n        return target\n    except (ImportError, AttributeError) as e:\n        raise ServConfigError(f\"Failed to import '{import_str}': {str(e)}\") from e\n</code></pre>"}, {"location": "reference/serv/config/#serv.config.import_module_from_string", "title": "import_module_from_string", "text": "<pre><code>import_module_from_string(module_path: str) -&gt; Any\n</code></pre> <p>Import a module by string.</p> <p>Parameters:</p> Name Type Description Default <code>module_path</code> <code>str</code> <p>String representing the module path (e.g., \"serv.app\")</p> required <p>Returns:</p> Type Description <code>Any</code> <p>The imported module</p> <p>Raises:</p> Type Description <code>ServConfigError</code> <p>If the import failed.</p> Source code in <code>serv/config.py</code> <pre><code>def import_module_from_string(module_path: str) -&gt; Any:\n    \"\"\"\n    Import a module by string.\n\n    Args:\n        module_path: String representing the module path (e.g., \"serv.app\")\n\n    Returns:\n        The imported module\n\n    Raises:\n        ServConfigError: If the import failed.\n    \"\"\"\n    try:\n        return importlib.import_module(module_path)\n    except ImportError as e:\n        raise ServConfigError(\n            f\"Failed to import module '{module_path}': {str(e)}\"\n        ) from e\n</code></pre>"}, {"location": "reference/serv/config/#serv.config.load_raw_config", "title": "load_raw_config", "text": "<pre><code>load_raw_config(config_path: str | Path) -&gt; dict[str, Any]\n</code></pre> <p>Load a configuration file.</p> <p>Parameters:</p> Name Type Description Default <code>config_path</code> <code>str | Path</code> <p>Path to the configuration file.</p> required <p>Returns:</p> Type Description <code>dict[str, Any]</code> <p>Dictionary containing the configuration.</p> <p>Raises:</p> Type Description <code>ServConfigError</code> <p>If the configuration file could not be loaded.</p> Source code in <code>serv/config.py</code> <pre><code>def load_raw_config(config_path: str | Path) -&gt; dict[str, Any]:\n    \"\"\"\n    Load a configuration file.\n\n    Args:\n        config_path: Path to the configuration file.\n\n    Returns:\n        Dictionary containing the configuration.\n\n    Raises:\n        ServConfigError: If the configuration file could not be loaded.\n    \"\"\"\n    try:\n        config_path_obj = Path(config_path)\n        if not config_path_obj.exists():\n            return {}\n\n        with open(config_path_obj) as f:\n            config = yaml.safe_load(f)\n\n        if config is None:  # Empty file\n            config = {}\n\n        if not isinstance(config, dict):\n            raise ServConfigError(\n                f\"Invalid configuration format in {config_path}. Expected a dictionary.\"\n            )\n\n        return config\n    except Exception as e:\n        if isinstance(e, ServConfigError):\n            raise\n        raise ServConfigError(\n            f\"Error loading configuration from {config_path}: {str(e)}\"\n        ) from e\n</code></pre>"}, {"location": "reference/serv/exceptions/", "title": "exceptions", "text": ""}, {"location": "reference/serv/exceptions/#serv.exceptions", "title": "serv.exceptions", "text": ""}, {"location": "reference/serv/exceptions/#serv.exceptions.HTTPBadRequestException", "title": "HTTPBadRequestException", "text": "<pre><code>HTTPBadRequestException(message: str | None = None, *args)\n</code></pre> <p>               Bases: <code>ServException</code></p> <p>Raised for general client errors (400).</p> Source code in <code>serv/exceptions.py</code> <pre><code>def __init__(self, message: str | None = None, *args):\n    super().__init__(message, *args)  # Pass message to parent Exception\n    # Set self.message: use provided message, or if None, try to use the first arg (if any)\n    # or fall back to a default string representation of the class name.\n    if message is not None:\n        self.message = message\n    elif args and args[0]:  # If message is None but other args are present\n        self.message = str(args[0])\n    else:  # Fallback if no message-like argument is provided\n        self.message = self.__class__.__name__\n</code></pre>"}, {"location": "reference/serv/exceptions/#serv.exceptions.HTTPMethodNotAllowedException", "title": "HTTPMethodNotAllowedException", "text": "<pre><code>HTTPMethodNotAllowedException(\n    message: str, allowed_methods: list[str]\n)\n</code></pre> <p>               Bases: <code>ServException</code></p> <p>Raised when a route is found but the method is not allowed (405).</p> Source code in <code>serv/exceptions.py</code> <pre><code>def __init__(self, message: str, allowed_methods: list[str]):\n    super().__init__(message)\n    self.allowed_methods = allowed_methods\n</code></pre>"}, {"location": "reference/serv/exceptions/#serv.exceptions.HTTPNotFoundException", "title": "HTTPNotFoundException", "text": "<pre><code>HTTPNotFoundException(message: str | None = None, *args)\n</code></pre> <p>               Bases: <code>ServException</code></p> <p>Raised when a route is not found (404).</p> Source code in <code>serv/exceptions.py</code> <pre><code>def __init__(self, message: str | None = None, *args):\n    super().__init__(message, *args)  # Pass message to parent Exception\n    # Set self.message: use provided message, or if None, try to use the first arg (if any)\n    # or fall back to a default string representation of the class name.\n    if message is not None:\n        self.message = message\n    elif args and args[0]:  # If message is None but other args are present\n        self.message = str(args[0])\n    else:  # Fallback if no message-like argument is provided\n        self.message = self.__class__.__name__\n</code></pre>"}, {"location": "reference/serv/exceptions/#serv.exceptions.ServException", "title": "ServException", "text": "<pre><code>ServException(message: str | None = None, *args)\n</code></pre> <p>               Bases: <code>Exception</code></p> <p>Base exception for Serv application.</p> Source code in <code>serv/exceptions.py</code> <pre><code>def __init__(self, message: str | None = None, *args):\n    super().__init__(message, *args)  # Pass message to parent Exception\n    # Set self.message: use provided message, or if None, try to use the first arg (if any)\n    # or fall back to a default string representation of the class name.\n    if message is not None:\n        self.message = message\n    elif args and args[0]:  # If message is None but other args are present\n        self.message = str(args[0])\n    else:  # Fallback if no message-like argument is provided\n        self.message = self.__class__.__name__\n</code></pre>"}, {"location": "reference/serv/injectors/", "title": "injectors", "text": ""}, {"location": "reference/serv/injectors/#serv.injectors", "title": "serv.injectors", "text": ""}, {"location": "reference/serv/multipart_parser/", "title": "multipart_parser", "text": ""}, {"location": "reference/serv/multipart_parser/#serv.multipart_parser", "title": "serv.multipart_parser", "text": ""}, {"location": "reference/serv/multipart_parser/#serv.multipart_parser.MultipartParser", "title": "MultipartParser", "text": "<pre><code>MultipartParser(\n    boundary: bytes,\n    charset: str = \"utf-8\",\n    config: dict[str, int] | None = None,\n)\n</code></pre> <p>An asynchronous streaming multipart/form-data parser using python_multipart.multipart.MultipartParser.</p> <p>This parser processes a multipart body stream obtained via an ASGI receive callable and returns a dictionary of fields and files. ParsedFileUpload instances capture individual file details, including their content_type and all headers associated with their part.</p> Source code in <code>serv/multipart_parser.py</code> <pre><code>def __init__(\n    self,\n    boundary: bytes,\n    charset: str = \"utf-8\",\n    config: dict[str, int] | None = None,\n):\n    if not boundary:\n        raise ValueError(\"Boundary is required for MultipartParser\")\n    self.boundary = boundary\n    self.charset = charset\n\n    current_config = DEFAULT_MULTIPART_CONFIG.copy()\n    if config:\n        current_config.update(config)\n\n    self._max_header_name_size = current_config[\"max_header_name_size\"]\n    self._max_header_value_size = current_config[\"max_header_value_size\"]\n    self._max_headers_per_part = current_config[\"max_headers_per_part\"]\n    self._max_file_size = current_config[\"max_file_size\"]\n    self._max_num_files = current_config[\"max_num_files\"]\n    self._max_total_body_size = current_config[\"max_total_body_size\"]\n\n    self._callbacks = {\n        \"on_part_begin\": self._on_part_begin,\n        \"on_part_data\": self._on_part_data,\n        \"on_part_end\": self._on_part_end,\n        \"on_header_begin\": self._on_header_begin,\n        \"on_header_field\": self._on_header_field,\n        \"on_header_value\": self._on_header_value,\n        \"on_header_end\": self._on_header_end,\n        \"on_headers_finished\": self._on_headers_finished,\n        \"on_end\": self._on_end,\n    }\n    # The low_level_parser is re-initialized in each parse call\n    # to ensure it's fresh, as it's stateful.\n    self._low_level_parser: PBaseParser | None = None\n\n    # --- Results storage ---\n    self.fields: dict[str, list[str]] = defaultdict(list)\n    self.files: dict[str, list[ParsedFileUpload]] = defaultdict(\n        list\n    )  # Use ParsedFileUpload\n\n    # --- Current part processing state ---\n    self._current_part_headers: dict[str, str] = {}\n    self._current_part_name: str | None = None\n    self._current_part_filename: str | None = None\n    self._current_part_content_type: str | None = None\n    self._current_part_data_buffer: io.BytesIO | None = None\n    self._current_headers_count_in_part: int = 0  # For max_headers_per_part\n    self._is_file_part: bool = False\n\n    # --- Current header processing state ---\n    self._current_header_name_buffer: bytearray = bytearray()\n    self._current_header_value_buffer: bytearray = bytearray()\n</code></pre>"}, {"location": "reference/serv/multipart_parser/#serv.multipart_parser.MultipartParser.parse", "title": "parse  <code>async</code>", "text": "<pre><code>parse(\n    receive: Callable[[], Awaitable[dict[str, Any]]],\n) -&gt; dict[str, list[str | ParsedFileUpload]]\n</code></pre> <p>Asynchronously parses multipart/form-data from an ASGI receive callable.</p> <p>Parameters:</p> Name Type Description Default <code>receive</code> <code>Callable[[], Awaitable[dict[str, Any]]]</code> <p>An ASGI receive callable.</p> required <p>Returns:</p> Type Description <code>dict[str, list[str | ParsedFileUpload]]</code> <p>A dictionary where keys are field names and values are lists of</p> <code>dict[str, list[str | ParsedFileUpload]]</code> <p>strings (for regular fields) or ParsedFileUpload instances (for files).</p> Source code in <code>serv/multipart_parser.py</code> <pre><code>async def parse(\n    self, receive: Callable[[], Awaitable[dict[str, Any]]]\n) -&gt; dict[str, list[str | ParsedFileUpload]]:\n    \"\"\"\n    Asynchronously parses multipart/form-data from an ASGI receive callable.\n\n    Args:\n        receive: An ASGI receive callable.\n\n    Returns:\n        A dictionary where keys are field names and values are lists of\n        strings (for regular fields) or ParsedFileUpload instances (for files).\n    \"\"\"\n    self.fields.clear()\n    self.files.clear()\n\n    # Initialize the low-level parser for this parse operation\n    self._low_level_parser = PBaseParser(self.boundary, self._callbacks)\n    self._total_body_bytes_received = 0\n    self._total_files_count = 0  # Reset for this parse call\n\n    more_body = True\n    while more_body:\n        message = await receive()\n        if message[\"type\"] != \"http.request\":\n            # This shouldn't happen if called correctly within an ASGI request cycle\n            # for the body, but handle defensively.\n            # Could raise an error or log. For now, break.\n            break\n\n        body_chunk = message.get(\"body\", b\"\")\n        if body_chunk:\n            self._total_body_bytes_received += len(body_chunk)\n            if self._total_body_bytes_received &gt; self._max_total_body_size:\n                raise MultipartParserError(\n                    f\"Total request body size exceeds maximum of {self._max_total_body_size} bytes.\"\n                )\n            self._low_level_parser.write(body_chunk)\n\n        more_body = message.get(\"more_body\", False)\n\n    self._low_level_parser.finalize()\n\n    result: dict[str, list[Any]] = defaultdict(list)\n    for name, values_list in self.fields.items():\n        result[name].extend(values_list)\n    for name, file_uploads_list in self.files.items():\n        result[name].extend(file_uploads_list)\n\n    return dict(result)\n</code></pre>"}, {"location": "reference/serv/multipart_parser/#serv.multipart_parser.MultipartParserError", "title": "MultipartParserError", "text": "<p>               Bases: <code>ValueError</code></p> <p>Custom exception for multipart parsing errors.</p>"}, {"location": "reference/serv/requests/", "title": "requests", "text": ""}, {"location": "reference/serv/requests/#serv.requests", "title": "serv.requests", "text": ""}, {"location": "reference/serv/requests/#serv.requests.Request", "title": "Request", "text": "<pre><code>Request(scope, receive)\n</code></pre> Source code in <code>serv/requests.py</code> <pre><code>def __init__(self, scope, receive):\n    if scope[\"type\"] != \"http\":\n        raise RuntimeError(\"Request only supports HTTP scope\")\n\n    self.scope = scope\n    self._receive = receive\n    self._body_consumed = False\n    self._buffer = bytearray()\n</code></pre>"}, {"location": "reference/serv/requests/#serv.requests.Request.body", "title": "body  <code>async</code>", "text": "<pre><code>body(max_size: int = 10 * 1024 * 1024) -&gt; bytes\n</code></pre> <p>Returns the request body as bytes up to max_size (default 10MB). Aggregates chunks from the read() stream.</p> Source code in <code>serv/requests.py</code> <pre><code>async def body(self, max_size: int = 10 * 1024 * 1024) -&gt; bytes:\n    \"\"\"\n    Returns the request body as bytes up to max_size (default 10MB).\n    Aggregates chunks from the read() stream.\n    \"\"\"\n    body_bytes = bytearray()\n    async for chunk in self.read(max_size=max_size):\n        body_bytes.extend(chunk)\n    return bytes(body_bytes)\n</code></pre>"}, {"location": "reference/serv/requests/#serv.requests.Request.read", "title": "read  <code>async</code>", "text": "<pre><code>read(max_size: int = -1)\n</code></pre> <p>Async generator yielding chunks of the request body as bytes.</p> <p>Stops when no more chunks are available. If max_size is set, it only yields that many bytes across all yielded chunks.</p> <p>This method raises a RuntimeError if the body has been fully consumed.</p> Source code in <code>serv/requests.py</code> <pre><code>async def read(self, max_size: int = -1):\n    \"\"\"\n    Async generator yielding chunks of the request body as bytes.\n\n    Stops when no more chunks are available. If max_size is set, it only yields that many bytes\n    across all yielded chunks.\n\n    This method raises a RuntimeError if the body has been fully consumed.\n    \"\"\"\n    if self._body_consumed and not self._buffer:\n        raise RuntimeError(\"Request body already consumed\")\n\n    total_read = 0\n    while not self._body_consumed or self._buffer:\n        if not self._body_consumed and (\n            not self._buffer or total_read + len(self._buffer) &lt; max_size\n            if max_size &gt; 0\n            else True\n        ):\n            message = await self._receive()\n            if message[\"type\"] != \"http.request\":\n                break\n\n            self._buffer.extend(message.get(\"body\", b\"\"))\n            self._body_consumed = not message.get(\"more_body\", False)\n\n        if max_size &lt;= 0 or total_read + len(self._buffer) &lt;= max_size:\n            yield self._buffer\n            total_read += len(self._buffer)\n            self._buffer.clear()\n        else:  # max_size &gt; 0 and total_read + len(self._buffer) &gt; max_size\n            can_yield = max_size - total_read\n            yield self._buffer[:can_yield]\n            self._buffer = self._buffer[can_yield:]\n            total_read = max_size  # or total_read += can_yield\n            break\n</code></pre>"}, {"location": "reference/serv/responses/", "title": "responses", "text": ""}, {"location": "reference/serv/responses/#serv.responses", "title": "serv.responses", "text": ""}, {"location": "reference/serv/responses/#serv.responses.ResponseBuilder", "title": "ResponseBuilder", "text": "<pre><code>ResponseBuilder(send_callable)\n</code></pre> Source code in <code>serv/responses.py</code> <pre><code>def __init__(self, send_callable):\n    self._send = send_callable\n    self._status = 200\n    self._headers = []  # List of (name_bytes, value_bytes)\n    self._body_components = []\n    self._headers_sent = False\n    self._default_encoding = \"utf-8\"\n    self._has_content_type = False\n</code></pre>"}, {"location": "reference/serv/responses/#serv.responses.ResponseBuilder.clear", "title": "clear", "text": "<pre><code>clear()\n</code></pre> <p>Clears the response body and headers. This is useful for error handlers. It cannot change anything that has already been sent, it only affects future sends and is intended to be used before send_response() has been called.</p> Source code in <code>serv/responses.py</code> <pre><code>def clear(self):\n    \"\"\"Clears the response body and headers. This is useful for error handlers. It cannot change\n    anything that has already been sent, it only affects future sends and is intended to be used\n    before send_response() has been called.\"\"\"\n    self._body_components = []\n    self._headers = []\n    self._status = 200\n    return self\n</code></pre>"}, {"location": "reference/serv/responses/#serv.responses.ResponseBuilder.delete_cookie", "title": "delete_cookie", "text": "<pre><code>delete_cookie(\n    key: str,\n    path: str = \"/\",\n    domain: str | None = None,\n    secure: bool = False,\n    httponly: bool = False,\n    samesite: str = \"lax\",\n)\n</code></pre> <p>Instructs the client to delete a cookie by setting its Max-Age to 0 and an expiry date in the past.</p> Source code in <code>serv/responses.py</code> <pre><code>def delete_cookie(\n    self,\n    key: str,\n    path: str = \"/\",\n    domain: str | None = None,\n    secure: bool = False,\n    httponly: bool = False,\n    samesite: str = \"lax\",\n):\n    \"\"\"Instructs the client to delete a cookie by setting its Max-Age to 0 and an expiry date in the past.\"\"\"\n    # Setting Max-Age=0 is the primary method. Expires is a fallback.\n    # Use a known past date string for Expires for robustness.\n    past_expiry_date = \"Thu, 01 Jan 1970 00:00:00 GMT\"\n    self.set_cookie(\n        key,\n        value=\"\",\n        max_age=0,\n        path=path,\n        domain=domain,\n        expires=past_expiry_date,\n        secure=secure,\n        httponly=httponly,\n        samesite=samesite,\n    )\n    return self\n</code></pre>"}, {"location": "reference/serv/routes/", "title": "routes", "text": ""}, {"location": "reference/serv/routes/#serv.routes", "title": "serv.routes", "text": ""}, {"location": "reference/serv/routing/", "title": "routing", "text": ""}, {"location": "reference/serv/routing/#serv.routing", "title": "serv.routing", "text": ""}, {"location": "reference/serv/routing/#serv.routing.Router", "title": "Router", "text": "<pre><code>Router(settings: dict[str, Any] = None)\n</code></pre> Source code in <code>serv/routing.py</code> <pre><code>def __init__(self, settings: dict[str, Any] = None):\n    # Stores tuples of (path_pattern, methods, handler_callable, settings)\n    self._routes: list[\n        tuple[str, frozenset[str] | None, Callable, dict[str, Any]]\n    ] = []\n    # Stores mapping of (route_class -&gt; path_pattern) for url_for lookups\n    self._route_class_paths: dict[type[routes.Route], list[str]] = {}\n    # Stores mapping of route path patterns to settings\n    self._route_settings: dict[str, dict[str, Any]] = {}\n    # Stores tuples of (mount_path, router_instance)\n    self._mounted_routers: list[tuple[str, Router]] = []\n    self._sub_routers: list[Router] = []\n    # Router-level settings\n    self._settings: dict[str, Any] = settings or {}\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.Router.add_route", "title": "add_route", "text": "<pre><code>add_route(\n    path: str,\n    handler: type[Route],\n    *,\n    settings: dict[str, Any] = None,\n)\n</code></pre><pre><code>add_route(\n    path: str,\n    handler: Callable[..., Awaitable[Any]],\n    methods: Sequence[str] | None = None,\n    *,\n    settings: dict[str, Any] = None,\n)\n</code></pre> <pre><code>add_route(\n    path: str,\n    handler: Callable[..., Awaitable[Any]] | type[Route],\n    methods: Sequence[str] | None = None,\n    *,\n    settings: dict[str, Any] = None,\n    container: Container = None,\n)\n</code></pre> <p>Adds a route to this router.</p> <p>This method can handle both direct route handlers and Route objects. For Route objects, it will automatically register all method and form handlers defined in the route.</p> <p>Parameters:</p> Name Type Description Default <code>path</code> <code>str</code> <p>The path pattern for the route.</p> required <code>handler</code> <code>Callable[..., Awaitable[Any]] | type[Route]</code> <p>Either a Route object or an async handler function.</p> required <code>methods</code> <code>Sequence[str] | None</code> <p>A list of HTTP methods (e.g., ['GET', 'POST']). Only used when handler is a function.     If None, allows all methods.</p> <code>None</code> <code>settings</code> <code>dict[str, Any]</code> <p>Optional dictionary of settings to be added to the container when handling this route.</p> <code>None</code> <code>container</code> <code>Container</code> <p>Optional container instance to use for dependency injection. If not provided, uses the global container.</p> <code>None</code> <p>Examples:</p> <pre><code>&gt;&gt;&gt; router.add_route(\"/users\", user_handler, [\"GET\", \"POST\"])\n&gt;&gt;&gt; router.add_route(\"/items\", ItemRoute, settings={\"db_table\": \"items\"})\n</code></pre> Source code in <code>serv/routing.py</code> <pre><code>def add_route(\n    self,\n    path: str,\n    handler: \"Callable[..., Awaitable[Any]] | type[routes.Route]\",\n    methods: Sequence[str] | None = None,\n    *,\n    settings: dict[str, Any] = None,\n    container: Container = None,\n):\n    \"\"\"Adds a route to this router.\n\n    This method can handle both direct route handlers and Route objects. For Route objects,\n    it will automatically register all method and form handlers defined in the route.\n\n    Args:\n        path: The path pattern for the route.\n        handler: Either a Route object or an async handler function.\n        methods: A list of HTTP methods (e.g., ['GET', 'POST']). Only used when handler is a function.\n                If None, allows all methods.\n        settings: Optional dictionary of settings to be added to the container when handling this route.\n        container: Optional container instance to use for dependency injection. If not provided, uses the global container.\n\n    Examples:\n        &gt;&gt;&gt; router.add_route(\"/users\", user_handler, [\"GET\", \"POST\"])\n        &gt;&gt;&gt; router.add_route(\"/items\", ItemRoute, settings={\"db_table\": \"items\"})\n    \"\"\"\n    match handler:\n        case type() as route if hasattr(route, \"__method_handlers__\") and hasattr(\n            route, \"__form_handlers__\"\n        ):\n            # Keep track of which paths are mapped to which Route classes\n            if route not in self._route_class_paths:\n                self._route_class_paths[route] = []\n            self._route_class_paths[route].append(path)\n\n            # Initialize the Route class directly to avoid container.call issues\n            # We'll still use a container branch to handle RouteSettings\n            if container is None:\n                # Create a new container from scratch if none was provided\n                from bevy import get_registry\n\n                container = get_registry().create_container()\n\n            with container.branch() as branch_container:\n                branch_container.instances[RouteSettings] = RouteSettings(\n                    **settings or {}\n                )\n                # Create route instance directly instead of using container.call\n                try:\n                    route_instance = route()\n                except Exception as e:\n                    import logging\n\n                    logging.getLogger(__name__).error(\n                        f\"Error initializing route {route}: {e}\"\n                    )\n                    raise\n\n            methods = (\n                route.__method_handlers__.keys() | route.__form_handlers__.keys()\n            )\n            # Store these settings for the actual path\n            self._route_settings[path] = settings or {}\n            self.add_route(\n                path, route_instance.__call__, list(methods), settings=settings\n            )\n\n        case _:\n            normalized_methods = (\n                frozenset(m.upper() for m in methods) if methods else None\n            )\n            self._routes.append((path, normalized_methods, handler, settings or {}))\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.Router.add_router", "title": "add_router", "text": "<pre><code>add_router(router: Router)\n</code></pre> <p>Adds a sub-router. Sub-routers are checked before the current router's own routes. Later added sub-routers are checked first (LIFO order for matching).</p> Source code in <code>serv/routing.py</code> <pre><code>def add_router(self, router: \"Router\"):\n    \"\"\"Adds a sub-router. Sub-routers are checked before the current router's own routes.\n    Later added sub-routers are checked first (LIFO order for matching).\"\"\"\n    self._sub_routers.append(router)\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.Router.mount", "title": "mount", "text": "<pre><code>mount(path: str, router: Router)\n</code></pre> <p>Mounts a router at a specific path.</p> <p>Unlike add_router which adds a router with full request path access, mount prefixes all routes in the mounted router with the given path.</p> <p>Parameters:</p> Name Type Description Default <code>path</code> <code>str</code> <p>The path prefix where the router should be mounted.  Should start with a '/' and not end with one.</p> required <code>router</code> <code>Router</code> <p>The router instance to mount at the specified path.</p> required <p>Examples:</p> <pre><code>&gt;&gt;&gt; api_router = Router()\n&gt;&gt;&gt; api_router.add_route(\"/users\", users_handler)\n&gt;&gt;&gt; main_router.mount(\"/api\", api_router)\n# Now \"/api/users\" will be handled by users_handler\n</code></pre> Source code in <code>serv/routing.py</code> <pre><code>def mount(self, path: str, router: \"Router\"):\n    \"\"\"Mounts a router at a specific path.\n\n    Unlike add_router which adds a router with full request path access,\n    mount prefixes all routes in the mounted router with the given path.\n\n    Args:\n        path: The path prefix where the router should be mounted.\n             Should start with a '/' and not end with one.\n        router: The router instance to mount at the specified path.\n\n    Examples:\n        &gt;&gt;&gt; api_router = Router()\n        &gt;&gt;&gt; api_router.add_route(\"/users\", users_handler)\n        &gt;&gt;&gt; main_router.mount(\"/api\", api_router)\n        # Now \"/api/users\" will be handled by users_handler\n    \"\"\"\n    if not path.startswith(\"/\"):\n        path = \"/\" + path\n    if path.endswith(\"/\"):\n        path = path[:-1]\n\n    self._mounted_routers.append((path, router))\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.Router.resolve_route", "title": "resolve_route", "text": "<pre><code>resolve_route(\n    request_path: str, request_method: str\n) -&gt; tuple[Callable, dict[str, Any], dict[str, Any]] | None\n</code></pre> <p>Recursively finds a handler for the given path and method.</p> <p>Parameters:</p> Name Type Description Default <code>request_path</code> <code>str</code> <p>The path of the incoming request.</p> required <code>request_method</code> <code>str</code> <p>The HTTP method of the incoming request.</p> required <p>Returns:</p> Type Description <code>tuple[Callable, dict[str, Any], dict[str, Any]] | None</code> <p>A tuple of (handler_callable, path_parameters_dict, settings_dict) if a match is found.</p> <code>tuple[Callable, dict[str, Any], dict[str, Any]] | None</code> <p>None if no route matches the path (results in a 404).</p> <p>Raises:</p> Type Description <code>HTTPMethodNotAllowedException</code> <p>If one or more routes match the path but not the method,                            and no route matches both path and method.</p> Source code in <code>serv/routing.py</code> <pre><code>def resolve_route(\n    self, request_path: str, request_method: str\n) -&gt; tuple[Callable, dict[str, Any], dict[str, Any]] | None:\n    \"\"\"Recursively finds a handler for the given path and method.\n\n    Args:\n        request_path: The path of the incoming request.\n        request_method: The HTTP method of the incoming request.\n\n    Returns:\n        A tuple of (handler_callable, path_parameters_dict, settings_dict) if a match is found.\n        None if no route matches the path (results in a 404).\n\n    Raises:\n        HTTPMethodNotAllowedException: If one or more routes match the path but not the method,\n                                       and no route matches both path and method.\n    \"\"\"\n    collected_allowed_methods: set[str] = set()\n    found_path_match_but_not_method = False\n\n    # 1. Check mounted routers first\n    for mount_path, mounted_router in self._mounted_routers:\n        if request_path.startswith(mount_path):\n            # Strip the mount path prefix for the mounted router\n            sub_path = request_path[len(mount_path) :]\n            if not sub_path:\n                sub_path = \"/\"\n            elif not sub_path.startswith(\"/\"):\n                sub_path = \"/\" + sub_path\n\n            try:\n                resolved_in_mounted = mounted_router.resolve_route(\n                    sub_path, request_method\n                )\n                if resolved_in_mounted:\n                    handler, params, settings = resolved_in_mounted\n                    # Merge router settings with any more specific settings\n                    merged_settings = {\n                        **self._settings,\n                        **mounted_router._settings,\n                        **settings,\n                    }\n                    return handler, params, merged_settings\n            except HTTPMethodNotAllowedException as e:\n                # Mounted router matched the path but not the method\n                collected_allowed_methods.update(e.allowed_methods)\n                found_path_match_but_not_method = True\n            except HTTPNotFoundException:\n                # Mounted router did not find the path. Continue search.\n                pass\n\n    # 2. Check sub-routers in reverse order of addition (LIFO for matching)\n    for sub_router in reversed(self._sub_routers):\n        try:\n            resolved_in_sub = sub_router.resolve_route(request_path, request_method)\n            if resolved_in_sub:\n                handler, params, settings = resolved_in_sub\n                # Merge router settings with any more specific settings\n                merged_settings = {\n                    **self._settings,\n                    **sub_router._settings,\n                    **settings,\n                }\n                return handler, params, merged_settings\n        except HTTPMethodNotAllowedException as e:\n            # Sub-router matched the path but not the method.\n            # Collect its allowed methods and mark that a path match occurred.\n            collected_allowed_methods.update(e.allowed_methods)\n            found_path_match_but_not_method = True\n            # Continue searching other sub-routers or parent's direct routes.\n        except HTTPNotFoundException:\n            # Sub-router did not find the path at all. Continue search.\n            pass\n\n    # 3. Check own routes\n    for (\n        path_pattern,\n        route_specific_methods,\n        handler_callable,\n        route_settings,\n    ) in self._routes:\n        match_info = self._match_path(request_path, path_pattern)\n        if match_info is not None:  # Path matches\n            found_path_match_but_not_method = (\n                True  # Mark that we at least matched the path\n            )\n            if (\n                route_specific_methods is None\n                or request_method.upper() in route_specific_methods\n            ):\n                # Path and method match\n                # Merge router settings with route settings\n                merged_settings = {**self._settings, **route_settings}\n                return handler_callable, match_info, merged_settings\n            else:\n                # Path matches, but method is not allowed for this specific route.\n                # Collect allowed methods.\n                if route_specific_methods:\n                    collected_allowed_methods.update(route_specific_methods)\n\n    # 4. After checking all mounted routers, sub-routers and own routes:\n    if found_path_match_but_not_method and collected_allowed_methods:\n        # We found one or more path matches, but no method matches for that path.\n        # And we have a list of methods that *would* have been allowed.\n        raise HTTPMethodNotAllowedException(\n            f\"Method {request_method} not allowed for {request_path}\",\n            allowed_methods=list(collected_allowed_methods),\n        )\n\n    # If no path match was found at all, or if path matched but no methods were ever defined for it\n    # (e.g. route_specific_methods was None and it wasn't a match, which is unlikely with current logic\n    # but covering bases if collected_allowed_methods is empty despite found_path_match_but_not_method)\n    if found_path_match_but_not_method and not collected_allowed_methods:\n        # This case implies a path was matched by a route that allows ALL methods (None),\n        # but the request_method somehow didn't trigger the \"return handler_callable, match_info\"\n        # This shouldn't happen if request_method.upper() is in route_specific_methods when it's None.\n        # For safety, if we matched a path but have no specific allowed methods to suggest,\n        # it's still a method not allowed situation, but without specific 'Allow' header.\n        # However, current logic means if route_specific_methods is None, it's an immediate match.\n        # This path should ideally not be hit frequently.\n        # To be safe, we will treat it as a 404 if no specific methods were collected.\n        pass\n\n    # No route matched the path at all, or a path was matched but it didn't lead to a 405 (e.g. ill-defined route).\n    return None\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.Router.url_for", "title": "url_for", "text": "<pre><code>url_for(handler: Callable | type[Route], **kwargs) -&gt; str\n</code></pre> <p>Builds a URL for a registered route handler with the given path parameters.</p> <p>Parameters:</p> Name Type Description Default <code>handler</code> <code>Callable | type[Route]</code> <p>The route handler function or Route class for which to build a URL.</p> required <code>**kwargs</code> <p>Path parameters to substitute in the URL pattern.</p> <code>{}</code> <p>Returns:</p> Type Description <code>str</code> <p>A URL string with path parameters filled in.</p> <p>Raises:</p> Type Description <code>ValueError</code> <p>If the handler is not found in any router, or if required path        parameters are missing from kwargs.</p> Source code in <code>serv/routing.py</code> <pre><code>def url_for(self, handler: Callable | type[routes.Route], **kwargs) -&gt; str:\n    \"\"\"Builds a URL for a registered route handler with the given path parameters.\n\n    Args:\n        handler: The route handler function or Route class for which to build a URL.\n        **kwargs: Path parameters to substitute in the URL pattern.\n\n    Returns:\n        A URL string with path parameters filled in.\n\n    Raises:\n        ValueError: If the handler is not found in any router, or if required path\n                   parameters are missing from kwargs.\n    \"\"\"\n    # First check if handler is a Route class\n    if isinstance(handler, type) and issubclass(handler, routes.Route):\n        # Look for route class in the _route_class_paths dictionary\n        if handler in self._route_class_paths:\n            path_list = self._route_class_paths[handler]\n\n            # Find the best matching path based on provided kwargs\n            path = self._find_best_matching_path(path_list, kwargs)\n            if not path:\n                # If no path can be fully satisfied with the provided kwargs,\n                # use the most recently added path (last in the list)\n                path = path_list[-1]\n        else:\n            # If not found directly, check mounted routers\n            for mount_path, mounted_router in self._mounted_routers:\n                try:\n                    sub_path = mounted_router.url_for(handler, **kwargs)\n                    return f\"{mount_path}{sub_path}\"\n                except ValueError:\n                    continue\n\n            # Check sub-routers\n            for sub_router in self._sub_routers:\n                try:\n                    return sub_router.url_for(handler, **kwargs)\n                except ValueError:\n                    continue\n\n            raise ValueError(\n                f\"Route class {handler.__name__} not found in any router\"\n            )\n\n    # Handle methods on Route instances (less common case)\n    elif hasattr(handler, \"__self__\") and isinstance(\n        handler.__self__, routes.Route\n    ):\n        route_instance = handler.__self__\n        handler = route_instance.__call__\n        path = self._find_handler_path(handler)\n        if not path:\n            raise ValueError(\n                f\"Route instance method {handler.__name__} not found in any router\"\n            )\n\n    # For function handlers\n    else:\n        # Try to find all paths for this handler\n        paths = self._find_all_handler_paths(handler)\n        if not paths:\n            # If not found directly, check mounted routers\n            for mount_path, mounted_router in self._mounted_routers:\n                try:\n                    sub_path = mounted_router.url_for(handler, **kwargs)\n                    return f\"{mount_path}{sub_path}\"\n                except ValueError:\n                    continue\n\n            # If not found in mounted routers, check sub-routers\n            for sub_router in self._sub_routers:\n                try:\n                    return sub_router.url_for(handler, **kwargs)\n                except ValueError:\n                    continue\n\n            raise ValueError(f\"Handler {handler.__name__} not found in any router\")\n\n        # Try to find the best path based on the provided kwargs\n        path = self._find_best_matching_path(paths, kwargs)\n        if not path:\n            # If no path can be fully satisfied, use the last registered path\n            path = paths[-1]\n\n    # Try to build the URL with the selected path\n    # If the required parameters aren't in kwargs, we'll need to try other paths\n    try:\n        return self._build_url_from_path(path, kwargs)\n    except ValueError as e:\n        if isinstance(handler, type) and issubclass(handler, routes.Route):\n            # For Route classes, try other paths if available\n            path_list = self._route_class_paths[handler]\n            for alt_path in reversed(path_list):\n                if alt_path != path:\n                    try:\n                        return self._build_url_from_path(alt_path, kwargs)\n                    except ValueError:\n                        continue\n\n        elif paths and len(paths) &gt; 1:\n            # For function handlers, try other paths if available\n            for alt_path in reversed(paths):\n                if alt_path != path:\n                    try:\n                        return self._build_url_from_path(alt_path, kwargs)\n                    except ValueError:\n                        continue\n\n        # If we get here, no path could be satisfied with the provided kwargs\n        raise e\n</code></pre>"}, {"location": "reference/serv/routing/#serv.routing.get_current_router", "title": "get_current_router", "text": "<pre><code>get_current_router(\n    container: Container = dependency(),\n) -&gt; Router\n</code></pre> <p>Retrieves the current request's root Router instance from the Bevy container.</p> Source code in <code>serv/routing.py</code> <pre><code>@inject\ndef get_current_router(container: Container = dependency()) -&gt; Router:\n    \"\"\"Retrieves the current request's root Router instance from the Bevy container.\"\"\"\n    try:\n        return container.get(Router)\n    except Exception as e:  # Bevy might raise a specific exception if not found\n        raise RuntimeError(\n            \"Router not found in the current request container. Ensure it's added during request setup.\"\n        ) from e\n</code></pre>"}, {"location": "reference/serv/serv/", "title": "serv", "text": ""}, {"location": "reference/serv/serv/#serv.serv", "title": "serv.serv", "text": ""}, {"location": "reference/serv/bundled/", "title": "bundled", "text": ""}, {"location": "reference/serv/bundled/#serv.bundled", "title": "serv.bundled", "text": ""}, {"location": "reference/serv/bundled/plugins/", "title": "plugins", "text": ""}, {"location": "reference/serv/bundled/plugins/#serv.bundled.plugins", "title": "serv.bundled.plugins", "text": ""}, {"location": "reference/serv/bundled/plugins/welcome/", "title": "welcome", "text": ""}, {"location": "reference/serv/bundled/plugins/welcome/#serv.bundled.plugins.welcome", "title": "serv.bundled.plugins.welcome", "text": ""}, {"location": "reference/serv/bundled/plugins/welcome/#serv.bundled.plugins.welcome.WelcomePlugin", "title": "WelcomePlugin", "text": "<pre><code>WelcomePlugin(\n    *,\n    plugin_spec: PluginSpec | None = None,\n    stand_alone: bool = False,\n)\n</code></pre> <p>               Bases: <code>Plugin</code></p> <p>A simple plugin that registers the WelcomeRoute at the root path (/).</p> Source code in <code>serv/plugins/plugins.py</code> <pre><code>def __init__(\n    self, *, plugin_spec: \"pl.PluginSpec | None\" = None, stand_alone: bool = False\n):\n    \"\"\"Initialize the plugin.\n\n    Loads plugin configuration and sets up any defined routers and routes\n    if they are configured in the plugin.yaml file.\n\n    Args:\n        stand_alone: If True, don't attempt to load plugin.yaml\n    \"\"\"\n    self._stand_alone = stand_alone\n    if plugin_spec:\n        self.__plugin_spec__ = plugin_spec\n    else:\n        module = sys.modules[self.__module__]\n        if not hasattr(module, \"__plugin_spec__\"):\n            raise Exception(\n                f\"Plugin {self.__class__.__name__} does not exist in a plugin package. No plugin.yaml found in \"\n                f\"parent directories.\"\n            )\n        self.__plugin_spec__ = module.__plugin_spec__\n</code></pre>"}, {"location": "reference/serv/bundled/plugins/welcome/#serv.bundled.plugins.welcome.WelcomePlugin.on_app_request_begin", "title": "on_app_request_begin  <code>async</code>", "text": "<pre><code>on_app_request_begin(router: Router = dependency()) -&gt; None\n</code></pre> <p>Registers the WelcomeRoute at '/' if no other route is already defined for it.</p> Source code in <code>serv/bundled/plugins/welcome/welcome.py</code> <pre><code>async def on_app_request_begin(self, router: Router = dependency()) -&gt; None:\n    \"\"\"Registers the WelcomeRoute at '/' if no other route is already defined for it.\"\"\"\n\n    # Basic check: see if any route is already registered for GET on \"/\"\n    # This is a simplification. A more robust router might offer a has_route(path, method) method.\n    path_already_handled = False\n    if hasattr(router, \"_routes\"):  # Basic check if router has _routes attribute\n        for path_pattern, methods, _ in router._routes:\n            if path_pattern == \"/\":\n                if methods is None or \"GET\" in methods:  # None means all methods\n                    path_already_handled = True\n                    break\n\n    if not path_already_handled:\n        router.add_route(\"/\", WelcomeRoute)\n</code></pre>"}, {"location": "reference/serv/bundled/plugins/welcome/welcome/", "title": "welcome", "text": ""}, {"location": "reference/serv/bundled/plugins/welcome/welcome/#serv.bundled.plugins.welcome.welcome", "title": "serv.bundled.plugins.welcome.welcome", "text": ""}, {"location": "reference/serv/bundled/plugins/welcome/welcome/#serv.bundled.plugins.welcome.welcome.WelcomePlugin", "title": "WelcomePlugin", "text": "<pre><code>WelcomePlugin(\n    *,\n    plugin_spec: PluginSpec | None = None,\n    stand_alone: bool = False,\n)\n</code></pre> <p>               Bases: <code>Plugin</code></p> <p>A simple plugin that registers the WelcomeRoute at the root path (/).</p> Source code in <code>serv/plugins/plugins.py</code> <pre><code>def __init__(\n    self, *, plugin_spec: \"pl.PluginSpec | None\" = None, stand_alone: bool = False\n):\n    \"\"\"Initialize the plugin.\n\n    Loads plugin configuration and sets up any defined routers and routes\n    if they are configured in the plugin.yaml file.\n\n    Args:\n        stand_alone: If True, don't attempt to load plugin.yaml\n    \"\"\"\n    self._stand_alone = stand_alone\n    if plugin_spec:\n        self.__plugin_spec__ = plugin_spec\n    else:\n        module = sys.modules[self.__module__]\n        if not hasattr(module, \"__plugin_spec__\"):\n            raise Exception(\n                f\"Plugin {self.__class__.__name__} does not exist in a plugin package. No plugin.yaml found in \"\n                f\"parent directories.\"\n            )\n        self.__plugin_spec__ = module.__plugin_spec__\n</code></pre>"}, {"location": "reference/serv/bundled/plugins/welcome/welcome/#serv.bundled.plugins.welcome.welcome.WelcomePlugin.on_app_request_begin", "title": "on_app_request_begin  <code>async</code>", "text": "<pre><code>on_app_request_begin(router: Router = dependency()) -&gt; None\n</code></pre> <p>Registers the WelcomeRoute at '/' if no other route is already defined for it.</p> Source code in <code>serv/bundled/plugins/welcome/welcome.py</code> <pre><code>async def on_app_request_begin(self, router: Router = dependency()) -&gt; None:\n    \"\"\"Registers the WelcomeRoute at '/' if no other route is already defined for it.\"\"\"\n\n    # Basic check: see if any route is already registered for GET on \"/\"\n    # This is a simplification. A more robust router might offer a has_route(path, method) method.\n    path_already_handled = False\n    if hasattr(router, \"_routes\"):  # Basic check if router has _routes attribute\n        for path_pattern, methods, _ in router._routes:\n            if path_pattern == \"/\":\n                if methods is None or \"GET\" in methods:  # None means all methods\n                    path_already_handled = True\n                    break\n\n    if not path_already_handled:\n        router.add_route(\"/\", WelcomeRoute)\n</code></pre>"}, {"location": "reference/serv/cli/", "title": "Index", "text": ""}, {"location": "reference/serv/cli/#serv.cli", "title": "serv.cli", "text": "<p>Serv CLI package.</p> <p>This package contains the modularized CLI components for the Serv web framework.</p>"}, {"location": "reference/serv/cli/commands/", "title": "commands", "text": ""}, {"location": "reference/serv/cli/commands/#serv.cli.commands", "title": "serv.cli.commands", "text": "<p>CLI command handlers.</p> <p>This module contains all the command handlers for the Serv CLI.</p>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_config_get_command", "title": "handle_config_get_command", "text": "<pre><code>handle_config_get_command(args_ns)\n</code></pre> <p>Handles the 'config get' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_config_get_command(args_ns):\n    \"\"\"Handles the 'config get' command.\"\"\"\n    logger.debug(\"Config get command started.\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        print(f\"❌ Configuration file '{config_path}' not found\")\n        return False\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f)\n\n        if not config:\n            print(\"❌ Configuration file is empty\")\n            return False\n\n        value = _get_config_value(config, args_ns.key)\n\n        if value is None:\n            print(f\"❌ Key '{args_ns.key}' not found in configuration\")\n            return False\n\n        print(f\"🔑 {args_ns.key}: {value}\")\n        return True\n\n    except Exception as e:\n        logger.error(f\"Error reading configuration: {e}\")\n        print(f\"❌ Error reading configuration: {e}\")\n        return False\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_config_set_command", "title": "handle_config_set_command", "text": "<pre><code>handle_config_set_command(args_ns)\n</code></pre> <p>Handles the 'config set' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_config_set_command(args_ns):\n    \"\"\"Handles the 'config set' command.\"\"\"\n    logger.debug(\"Config set command started.\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        print(f\"❌ Configuration file '{config_path}' not found\")\n        print(\"   Run 'serv app init' to create a configuration file\")\n        return False\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f) or {}\n\n        # Convert value to appropriate type\n        value = args_ns.value\n        if args_ns.type == \"int\":\n            value = int(value)\n        elif args_ns.type == \"float\":\n            value = float(value)\n        elif args_ns.type == \"bool\":\n            value = value.lower() in (\"true\", \"yes\", \"1\", \"on\")\n        elif args_ns.type == \"list\":\n            # Simple comma-separated list\n            value = [item.strip() for item in value.split(\",\")]\n\n        # Set the value\n        _set_config_value(config, args_ns.key, value)\n\n        # Write back to file\n        with open(config_path, \"w\") as f:\n            yaml.dump(config, f, sort_keys=False, indent=2, default_flow_style=False)\n\n        print(f\"✅ Set {args_ns.key} = {value}\")\n        return True\n\n    except ValueError as e:\n        print(f\"❌ Invalid value type: {e}\")\n        return False\n    except Exception as e:\n        logger.error(f\"Error setting configuration: {e}\")\n        print(f\"❌ Error setting configuration: {e}\")\n        return False\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_config_show_command", "title": "handle_config_show_command", "text": "<pre><code>handle_config_show_command(args_ns)\n</code></pre> <p>Handles the 'config show' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_config_show_command(args_ns):\n    \"\"\"Handles the 'config show' command.\"\"\"\n    logger.debug(\"Config show command started.\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        print(f\"❌ Configuration file '{config_path}' not found\")\n        print(\"   Run 'serv app init' to create a configuration file\")\n        return False\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f)\n\n        if not config:\n            print(\"❌ Configuration file is empty\")\n            return False\n\n        print(f\"📄 Configuration from '{config_path}':\")\n        print(\"=\" * 50)\n\n        if args_ns.format == \"json\":\n            print(json.dumps(config, indent=2, default=str))\n        else:\n            print(\n                yaml.dump(config, sort_keys=False, indent=2, default_flow_style=False)\n            )\n\n        return True\n\n    except Exception as e:\n        logger.error(f\"Error reading configuration: {e}\")\n        print(f\"❌ Error reading configuration: {e}\")\n        return False\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_config_validate_command", "title": "handle_config_validate_command", "text": "<pre><code>handle_config_validate_command(args_ns)\n</code></pre> <p>Handles the 'config validate' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_config_validate_command(args_ns):\n    \"\"\"Handles the 'config validate' command.\"\"\"\n    logger.debug(\"Config validate command started.\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        print(f\"❌ Configuration file '{config_path}' not found\")\n        return False\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f)\n\n        if not config:\n            print(\"❌ Configuration file is empty\")\n            return False\n\n        print(\"✅ Configuration file is valid YAML\")\n\n        # Basic structure validation\n        issues = 0\n\n        # Check required sections\n        required_sections = [\"site_info\"]\n        for section in required_sections:\n            if section not in config:\n                print(f\"⚠️  Missing recommended section: {section}\")\n                issues += 1\n\n        # Check site_info structure\n        if \"site_info\" in config:\n            site_info = config[\"site_info\"]\n            if not isinstance(site_info, dict):\n                print(\"❌ 'site_info' must be a dictionary\")\n                issues += 1\n            elif not site_info.get(\"name\"):\n                print(\"⚠️  Missing 'site_info.name'\")\n                issues += 1\n\n        # Check plugins structure\n        if \"plugins\" in config:\n            plugins = config[\"plugins\"]\n            if not isinstance(plugins, list):\n                print(\"❌ 'plugins' must be a list\")\n                issues += 1\n\n        # Check middleware structure\n        if \"middleware\" in config:\n            middleware = config[\"middleware\"]\n            if not isinstance(middleware, list):\n                print(\"❌ 'middleware' must be a list\")\n                issues += 1\n\n        if issues == 0:\n            print(\"🎉 Configuration validation passed!\")\n        else:\n            print(f\"⚠️  Found {issues} validation issue(s)\")\n\n        return issues == 0\n\n    except yaml.YAMLError as e:\n        print(f\"❌ Invalid YAML syntax: {e}\")\n        return False\n    except Exception as e:\n        logger.error(f\"Error validating configuration: {e}\")\n        print(f\"❌ Error validating configuration: {e}\")\n        return False\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_create_entrypoint_command", "title": "handle_create_entrypoint_command", "text": "<pre><code>handle_create_entrypoint_command(args_ns)\n</code></pre> <p>Handles the 'create entrypoint' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_create_entrypoint_command(args_ns):\n    \"\"\"Handles the 'create entrypoint' command.\"\"\"\n    logger.debug(\"Create entrypoint command started.\")\n\n    component_name = args_ns.name\n    plugin_name, plugin_dir = _detect_plugin_context(args_ns.plugin)\n\n    if not plugin_name:\n        if args_ns.plugin:\n            logger.error(f\"Plugin '{args_ns.plugin}' not found.\")\n            return\n        else:\n            # Interactive prompt for plugin\n            plugins_dir = Path.cwd() / \"plugins\"\n            if plugins_dir.exists():\n                available_plugins = [\n                    d.name\n                    for d in plugins_dir.iterdir()\n                    if d.is_dir()\n                    and (d / \"plugin.yaml\").exists()\n                    and not d.name.startswith(\"_\")\n                ]\n                if available_plugins:\n                    print(\"Available plugins:\")\n                    for i, plugin in enumerate(available_plugins, 1):\n                        print(f\"  {i}. {plugin}\")\n                    plugin_choice = prompt_user(\"Select plugin (name or number)\")\n                    if plugin_choice and plugin_choice.isdigit():\n                        idx = int(plugin_choice) - 1\n                        if 0 &lt;= idx &lt; len(available_plugins):\n                            plugin_name = available_plugins[idx]\n                            plugin_dir = plugins_dir / plugin_name\n                    elif plugin_choice in available_plugins:\n                        plugin_name = plugin_choice\n                        plugin_dir = plugins_dir / plugin_name\n\n            if not plugin_name:\n                logger.error(\"No plugin specified and none could be auto-detected.\")\n                return\n\n    class_name = to_pascal_case(component_name)\n    file_name = f\"entrypoint_{to_snake_case(component_name)}.py\"\n    file_path = plugin_dir / file_name\n\n    if file_path.exists() and not args_ns.force:\n        print(f\"Warning: File '{file_path}' already exists. Use --force to overwrite.\")\n        return\n\n    # Create the entrypoint file\n    context = {\n        \"class_name\": class_name,\n        \"entrypoint_name\": component_name,\n        \"route_path\": to_snake_case(component_name),\n        \"handler_name\": f\"handle_{to_snake_case(component_name)}\",\n    }\n\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"entrypoint_main_py.template\")\n        content = template.render(**context)\n\n        with open(file_path, \"w\") as f:\n            f.write(content)\n\n        print(f\"Created '{file_path}'\")\n\n        # Update plugin config\n        entry_path = f\"{file_name[:-3]}:{class_name}\"\n        if _update_plugin_config(\n            plugin_dir, \"entry_points\", component_name, entry_path\n        ):\n            print(\"Added entrypoint to plugin configuration\")\n\n        print(\n            f\"Entrypoint '{component_name}' created successfully in plugin '{plugin_name}'.\"\n        )\n\n    except Exception as e:\n        logger.error(f\"Error creating entrypoint: {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_create_middleware_command", "title": "handle_create_middleware_command", "text": "<pre><code>handle_create_middleware_command(args_ns)\n</code></pre> <p>Handles the 'create middleware' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_create_middleware_command(args_ns):\n    \"\"\"Handles the 'create middleware' command.\"\"\"\n    logger.debug(\"Create middleware command started.\")\n\n    component_name = args_ns.name\n    plugin_name, plugin_dir = _detect_plugin_context(args_ns.plugin)\n\n    if not plugin_name:\n        if args_ns.plugin:\n            logger.error(f\"Plugin '{args_ns.plugin}' not found.\")\n            return\n        else:\n            # Interactive prompt for plugin\n            plugins_dir = Path.cwd() / \"plugins\"\n            if plugins_dir.exists():\n                available_plugins = [\n                    d.name\n                    for d in plugins_dir.iterdir()\n                    if d.is_dir()\n                    and (d / \"plugin.yaml\").exists()\n                    and not d.name.startswith(\"_\")\n                ]\n                if available_plugins:\n                    print(\"Available plugins:\")\n                    for i, plugin in enumerate(available_plugins, 1):\n                        print(f\"  {i}. {plugin}\")\n                    plugin_choice = prompt_user(\"Select plugin (name or number)\")\n                    if plugin_choice and plugin_choice.isdigit():\n                        idx = int(plugin_choice) - 1\n                        if 0 &lt;= idx &lt; len(available_plugins):\n                            plugin_name = available_plugins[idx]\n                            plugin_dir = plugins_dir / plugin_name\n                    elif plugin_choice in available_plugins:\n                        plugin_name = plugin_choice\n                        plugin_dir = plugins_dir / plugin_name\n\n            if not plugin_name:\n                logger.error(\"No plugin specified and none could be auto-detected.\")\n                return\n\n    middleware_name = to_snake_case(component_name)\n    file_name = f\"middleware_{middleware_name}.py\"\n    file_path = plugin_dir / file_name\n\n    if file_path.exists() and not args_ns.force:\n        print(f\"Warning: File '{file_path}' already exists. Use --force to overwrite.\")\n        return\n\n    # Create the middleware file\n    context = {\n        \"middleware_name\": middleware_name,\n        \"middleware_description\": f\"Middleware for {component_name.replace('_', ' ')} functionality.\",\n    }\n\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"middleware_main_py.template\")\n        content = template.render(**context)\n\n        with open(file_path, \"w\") as f:\n            f.write(content)\n\n        print(f\"Created '{file_path}'\")\n\n        # Update plugin config\n        entry_path = f\"{file_name[:-3]}:{middleware_name}_middleware\"\n        if _update_plugin_config(plugin_dir, \"middleware\", component_name, entry_path):\n            print(\"Added middleware to plugin configuration\")\n\n        print(\n            f\"Middleware '{component_name}' created successfully in plugin '{plugin_name}'.\"\n        )\n\n    except Exception as e:\n        logger.error(f\"Error creating middleware: {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_create_plugin_command", "title": "handle_create_plugin_command", "text": "<pre><code>handle_create_plugin_command(args_ns)\n</code></pre> <p>Handles the 'create plugin' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_create_plugin_command(args_ns):\n    \"\"\"Handles the 'create plugin' command.\"\"\"\n    logger.debug(\"Create plugin command started.\")\n\n    # Get plugin name from args\n    plugin_name_human = args_ns.name\n\n    # For non-interactive mode, use default values\n    if getattr(args_ns, \"non_interactive\", False):\n        plugin_author = \"Test Author\"\n        plugin_description = \"A test plugin for Serv\"\n        plugin_version = \"1.0.0\"\n    else:\n        plugin_author = prompt_user(\"Author\", \"Your Name\") or \"Your Name\"\n        plugin_description = (\n            prompt_user(\"Description\", \"A cool Serv plugin.\") or \"A cool Serv plugin.\"\n        )\n        plugin_version = prompt_user(\"Version\", \"0.1.0\") or \"0.1.0\"\n\n    class_name = to_pascal_case(plugin_name_human)\n    plugin_dir_name = to_snake_case(plugin_name_human)\n    if not plugin_dir_name:\n        logger.error(\n            f\"Could not derive a valid module name from '{plugin_name_human}'. Please use alphanumeric characters.\"\n        )\n        return\n\n    python_file_name = f\"{plugin_dir_name}.py\"\n\n    plugins_root_dir = Path.cwd() / \"plugins\"\n    plugin_specific_dir = plugins_root_dir / plugin_dir_name\n\n    if plugin_specific_dir.exists() and not getattr(args_ns, \"force\", False):\n        print(\n            f\"Warning: Plugin directory '{plugin_specific_dir}' already exists. Files might be overwritten.\"\n        )\n\n    try:\n        os.makedirs(plugin_specific_dir, exist_ok=True)\n        (plugins_root_dir / \"__init__.py\").touch(exist_ok=True)\n        (plugin_specific_dir / \"__init__.py\").touch(exist_ok=True)\n\n    except OSError as e:\n        logger.error(\n            f\"Error creating plugin directory structure '{plugin_specific_dir}': {e}\"\n        )\n        return\n\n    # Create plugin.yaml\n    plugin_yaml_path = plugin_specific_dir / \"plugin.yaml\"\n    plugin_entry_path = f\"{python_file_name.replace('.py', '')}:{class_name}\"\n\n    plugin_yaml_context = {\n        \"plugin_name\": plugin_name_human,\n        \"plugin_entry_path\": plugin_entry_path,\n        \"plugin_version\": plugin_version,\n        \"plugin_author\": plugin_author,\n        \"plugin_description\": plugin_description,\n    }\n\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"plugin_yaml.template\")\n        plugin_yaml_content_str = template.render(**plugin_yaml_context)\n    except Exception as e_template:\n        logger.error(f\"Error loading plugin_yaml.template: {e_template}\")\n        return\n\n    try:\n        with open(plugin_yaml_path, \"w\") as f:\n            f.write(plugin_yaml_content_str)\n        print(f\"Created '{plugin_yaml_path}'\")\n    except OSError as e:\n        logger.error(f\"Error writing '{plugin_yaml_path}': {e}\")\n        return\n\n    # Create main.py (plugin Python file)\n    plugin_py_path = plugin_specific_dir / python_file_name\n\n    plugin_py_context = {\n        \"class_name\": class_name,\n        \"plugin_name\": plugin_name_human,\n    }\n\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"plugin_main_py.template\")\n        plugin_py_content_str = template.render(**plugin_py_context)\n    except Exception as e_template:\n        logger.error(f\"Error loading plugin_main_py.template: {e_template}\")\n        return\n\n    try:\n        with open(plugin_py_path, \"w\") as f:\n            f.write(plugin_py_content_str)\n        print(f\"Created '{plugin_py_path}'\")\n        print(\n            f\"Plugin '{plugin_name_human}' created successfully in '{plugin_specific_dir}'.\"\n        )\n        print(f\"To use it, add its entry path to your '{DEFAULT_CONFIG_FILE}':\")\n        print(f\"  - plugin: {plugin_dir_name}\")\n        print(\"    config: {} # Optional config\")\n\n    except OSError as e:\n        logger.error(f\"Error writing '{plugin_py_path}': {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_create_route_command", "title": "handle_create_route_command", "text": "<pre><code>handle_create_route_command(args_ns)\n</code></pre> <p>Handles the 'create route' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_create_route_command(args_ns):\n    \"\"\"Handles the 'create route' command.\"\"\"\n    logger.debug(\"Create route command started.\")\n\n    component_name = args_ns.name\n    plugin_name, plugin_dir = _detect_plugin_context(args_ns.plugin)\n\n    if not plugin_name:\n        if args_ns.plugin:\n            logger.error(f\"Plugin '{args_ns.plugin}' not found.\")\n            return\n        else:\n            # Interactive prompt for plugin\n            plugins_dir = Path.cwd() / \"plugins\"\n            if plugins_dir.exists():\n                available_plugins = [\n                    d.name\n                    for d in plugins_dir.iterdir()\n                    if d.is_dir()\n                    and (d / \"plugin.yaml\").exists()\n                    and not d.name.startswith(\"_\")\n                ]\n                if available_plugins:\n                    print(\"Available plugins:\")\n                    for i, plugin in enumerate(available_plugins, 1):\n                        print(f\"  {i}. {plugin}\")\n                    plugin_choice = prompt_user(\"Select plugin (name or number)\")\n                    if plugin_choice and plugin_choice.isdigit():\n                        idx = int(plugin_choice) - 1\n                        if 0 &lt;= idx &lt; len(available_plugins):\n                            plugin_name = available_plugins[idx]\n                            plugin_dir = plugins_dir / plugin_name\n                    elif plugin_choice in available_plugins:\n                        plugin_name = plugin_choice\n                        plugin_dir = plugins_dir / plugin_name\n\n            if not plugin_name:\n                logger.error(\"No plugin specified and none could be auto-detected.\")\n                return\n\n    # Get route path\n    route_path = args_ns.path\n    if not route_path:\n        default_path = f\"/{to_snake_case(component_name)}\"\n        # Check if stdin is available for interactive input\n        try:\n            import sys\n\n            if not sys.stdin.isatty():\n                # Non-interactive environment (like tests), use default\n                route_path = default_path\n            else:\n                route_path = prompt_user(\"Route path\", default_path) or default_path\n        except (EOFError, OSError):\n            # Fallback to default if input fails\n            route_path = default_path\n\n    # Ensure path starts with /\n    if not route_path.startswith(\"/\"):\n        route_path = \"/\" + route_path\n\n    # Get router name\n    router_name = args_ns.router\n    if not router_name:\n        # Check existing routers in plugin config\n        plugin_yaml_path = plugin_dir / \"plugin.yaml\"\n        existing_routers = []\n\n        if plugin_yaml_path.exists():\n            try:\n                with open(plugin_yaml_path) as f:\n                    plugin_config = yaml.safe_load(f) or {}\n\n                routers = plugin_config.get(\"routers\", [])\n                existing_routers = [\n                    router.get(\"name\") for router in routers if router.get(\"name\")\n                ]\n            except Exception:\n                pass\n\n        # Check if stdin is available for interactive input\n        try:\n            import sys\n\n            if not sys.stdin.isatty():\n                # Non-interactive environment (like tests), use default\n                router_name = \"main_router\"\n            elif existing_routers:\n                print(\"Existing routers:\")\n                for i, router in enumerate(existing_routers, 1):\n                    print(f\"  {i}. {router}\")\n                print(f\"  {len(existing_routers) + 1}. Create new router\")\n\n                router_choice = prompt_user(\"Select router (name or number)\", \"1\")\n                if router_choice and router_choice.isdigit():\n                    idx = int(router_choice) - 1\n                    if 0 &lt;= idx &lt; len(existing_routers):\n                        router_name = existing_routers[idx]\n                    elif idx == len(existing_routers):\n                        router_name = (\n                            prompt_user(\"New router name\", \"main_router\")\n                            or \"main_router\"\n                        )\n                elif router_choice in existing_routers:\n                    router_name = router_choice\n                else:\n                    router_name = router_choice or \"main_router\"\n            else:\n                router_name = prompt_user(\"Router name\", \"main_router\") or \"main_router\"\n        except (EOFError, OSError):\n            # Fallback to default if input fails\n            router_name = \"main_router\"\n\n    class_name = to_pascal_case(component_name)\n    file_name = f\"route_{to_snake_case(component_name)}.py\"\n    file_path = plugin_dir / file_name\n\n    if file_path.exists() and not args_ns.force:\n        print(f\"Warning: File '{file_path}' already exists. Use --force to overwrite.\")\n        return\n\n    # Create the route file\n    context = {\n        \"class_name\": class_name,\n        \"route_name\": component_name,\n        \"route_path\": route_path,\n    }\n\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"route_main_py.template\")\n        content = template.render(**context)\n\n        with open(file_path, \"w\") as f:\n            f.write(content)\n\n        print(f\"Created '{file_path}'\")\n\n        # Update plugin config with router name and path\n        entry_path = f\"{file_name[:-3]}:{class_name}\"\n        route_config = {\n            \"path\": route_path,\n            \"handler\": entry_path,\n            \"router_name\": router_name,\n            \"component_name\": component_name,\n        }\n\n        if _update_plugin_config(plugin_dir, \"routers\", component_name, route_config):\n            print(f\"Added route to router '{router_name}' in plugin configuration\")\n\n        print(\n            f\"Route '{component_name}' created successfully in plugin '{plugin_name}' at path '{route_path}'.\"\n        )\n\n    except Exception as e:\n        logger.error(f\"Error creating route: {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_dev_command", "title": "handle_dev_command  <code>async</code>", "text": "<pre><code>handle_dev_command(args_ns)\n</code></pre> <p>Handles the 'dev' command for enhanced development server.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>async def handle_dev_command(args_ns):\n    \"\"\"Handles the 'dev' command for enhanced development server.\"\"\"\n    logger.debug(\"Dev command started.\")\n\n    try:\n        print(\"🚀 Starting Serv development server...\")\n        print(\"📝 Development mode features:\")\n        print(\"   • Auto-reload enabled (unless --no-reload)\")\n        print(\"   • Enhanced error reporting\")\n        print(\"   • Development mode enabled\")\n\n        app = _get_configured_app(args_ns.app, args_ns)\n\n        # Force development mode\n        if hasattr(app, \"dev_mode\"):\n            app.dev_mode = True\n\n        # Configure uvicorn for development\n        reload = not args_ns.no_reload\n        uvicorn_config = {\n            \"app\": app,\n            \"host\": args_ns.host,\n            \"port\": args_ns.port,\n            \"reload\": reload,\n            \"workers\": 1\n            if reload\n            else args_ns.workers,  # Reload doesn't work with multiple workers\n            \"log_level\": \"debug\",\n            \"access_log\": True,\n        }\n\n        logger.info(f\"Starting development server on {args_ns.host}:{args_ns.port}\")\n        if reload:\n            print(\"🔄 Auto-reload is enabled - files will be watched for changes\")\n        else:\n            print(\"⚠️  Auto-reload is disabled\")\n\n        # Start the server\n        server = uvicorn.Server(uvicorn.Config(**uvicorn_config))\n        await server.serve()\n\n    except Exception as e:\n        logger.error(f\"Error starting development server: {e}\")\n        sys.exit(1)\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_disable_plugin_command", "title": "handle_disable_plugin_command", "text": "<pre><code>handle_disable_plugin_command(args_ns)\n</code></pre> <p>Handles the 'disable-plugin' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_disable_plugin_command(args_ns):\n    \"\"\"Handles the 'disable-plugin' command.\"\"\"\n    plugin_identifier = args_ns.plugin_identifier\n    logger.debug(f\"Attempting to disable plugin: '{plugin_identifier}'...\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        logger.error(\n            f\"Configuration file '{config_path}' not found. Please run 'serv init' first.\"\n        )\n        return\n\n    # Convert plugin identifier to directory name\n    plugin_id = to_snake_case(plugin_identifier)\n    plugin_name_human = plugin_identifier\n\n    # Check if plugin directory exists and get human name\n    plugins_dir = Path.cwd() / \"plugins\"\n    plugin_yaml_path = plugins_dir / plugin_id / \"plugin.yaml\"\n\n    if plugin_yaml_path.exists():\n        try:\n            with open(plugin_yaml_path) as f:\n                plugin_meta = yaml.safe_load(f)\n            if isinstance(plugin_meta, dict):\n                plugin_name_human = plugin_meta.get(\"name\", plugin_identifier)\n        except Exception:\n            plugin_name_human = plugin_identifier\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f) or {}\n    except Exception as e:\n        logger.error(f\"Error reading config file '{config_path}': {e}\")\n        return\n\n    plugins = config.get(\"plugins\", [])\n    original_count = len(plugins)\n\n    # Remove the plugin\n    plugins = [\n        p\n        for p in plugins\n        if (\n            (\n                isinstance(p, dict)\n                and p.get(\"plugin\") not in [plugin_id, plugin_identifier]\n            )\n            or (isinstance(p, str) and p not in [plugin_id, plugin_identifier])\n        )\n    ]\n\n    if len(plugins) == original_count:\n        print(f\"Plugin '{plugin_identifier}' was not found in the configuration.\")\n        return\n\n    config[\"plugins\"] = plugins\n\n    try:\n        with open(config_path, \"w\") as f:\n            yaml.dump(config, f, sort_keys=False, indent=2, default_flow_style=False)\n        print(f\"Plugin '{plugin_identifier}' disabled successfully.\")\n        if plugin_name_human and plugin_name_human != plugin_identifier:\n            print(f\"Human name: {plugin_name_human}\")\n    except Exception as e:\n        logger.error(f\"Error writing config file '{config_path}': {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_enable_plugin_command", "title": "handle_enable_plugin_command", "text": "<pre><code>handle_enable_plugin_command(args_ns)\n</code></pre> <p>Handles the 'enable-plugin' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_enable_plugin_command(args_ns):\n    \"\"\"Handles the 'enable-plugin' command.\"\"\"\n    plugin_identifier = args_ns.plugin_identifier\n    logger.debug(f\"Attempting to enable plugin: '{plugin_identifier}'...\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n    if not config_path.exists():\n        logger.error(\n            f\"Configuration file '{config_path}' not found. Please run 'serv init' first.\"\n        )\n        return\n\n    # Convert plugin identifier to directory name\n    plugin_id = to_snake_case(plugin_identifier)\n    plugin_name_human = plugin_identifier\n\n    # Check if plugin directory exists\n    plugins_dir = Path.cwd() / \"plugins\"\n    plugin_yaml_path = plugins_dir / plugin_id / \"plugin.yaml\"\n\n    if not plugin_yaml_path.exists():\n        logger.error(\n            f\"Plugin '{plugin_identifier}' not found. Expected plugin.yaml at '{plugin_yaml_path}'.\"\n        )\n        return\n\n    # Get human name from plugin.yaml\n    try:\n        with open(plugin_yaml_path) as f:\n            plugin_meta = yaml.safe_load(f)\n        if isinstance(plugin_meta, dict):\n            plugin_name_human = plugin_meta.get(\"name\", plugin_identifier)\n    except Exception:\n        plugin_name_human = plugin_identifier\n\n    try:\n        with open(config_path) as f:\n            config = yaml.safe_load(f) or {}\n    except Exception as e:\n        logger.error(f\"Error reading config file '{config_path}': {e}\")\n        return\n\n    plugins = config.get(\"plugins\", [])\n\n    # Check if plugin is already enabled\n    for plugin_entry in plugins:\n        if isinstance(plugin_entry, dict):\n            existing_plugin = plugin_entry.get(\"plugin\")\n        else:\n            existing_plugin = plugin_entry\n\n        if existing_plugin == plugin_id or existing_plugin == plugin_identifier:\n            print(f\"Plugin '{plugin_identifier}' is already enabled.\")\n            return\n\n    # Add the plugin\n    plugins.append({\"plugin\": plugin_id})\n    config[\"plugins\"] = plugins\n\n    try:\n        with open(config_path, \"w\") as f:\n            yaml.dump(config, f, sort_keys=False, indent=2, default_flow_style=False)\n        print(f\"Plugin '{plugin_identifier}' enabled successfully.\")\n        if plugin_name_human and plugin_name_human != plugin_identifier:\n            print(f\"Human name: {plugin_name_human}\")\n    except Exception as e:\n        logger.error(f\"Error writing config file '{config_path}': {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_init_command", "title": "handle_init_command", "text": "<pre><code>handle_init_command(args_ns)\n</code></pre> <p>Handles the 'init' command to create serv.config.yaml.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_init_command(args_ns):\n    \"\"\"Handles the 'init' command to create serv.config.yaml.\"\"\"\n    logger.debug(\"Init command started.\")\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n\n    if config_path.exists() and not args_ns.force:\n        overwrite_prompt = prompt_user(\n            f\"'{config_path.name}' already exists in '{Path.cwd()}'. Overwrite? (yes/no)\",\n            \"no\",\n        )\n        if overwrite_prompt is None or overwrite_prompt.lower() != \"yes\":\n            print(\"Initialization cancelled by user.\")\n            return\n\n    # For non-interactive mode, use default values\n    if getattr(args_ns, \"non_interactive\", False) or (\n        args_ns.force and config_path.exists()\n    ):\n        site_name = \"My Serv Site\"\n        site_description = \"A new website powered by Serv\"\n    else:\n        site_name = prompt_user(\"Enter site name\", \"My Serv Site\") or \"My Serv Site\"\n        site_description = (\n            prompt_user(\"Enter site description\", \"A new website powered by Serv\")\n            or \"A new website powered by Serv\"\n        )\n\n    # Load and render the config template\n    try:\n        template_dir = (\n            Path(importlib.util.find_spec(\"serv.cli\").submodule_search_locations[0])\n            / \"scaffolding\"\n        )\n        env = jinja2.Environment(loader=jinja2.FileSystemLoader(template_dir))\n        template = env.get_template(\"config_yaml.template\")\n\n        config_context = {\n            \"site_name\": site_name,\n            \"site_description\": site_description,\n        }\n\n        config_content_str = template.render(**config_context)\n    except Exception as e_template:\n        logger.error(f\"Error loading config_yaml.template: {e_template}\")\n        return\n\n    try:\n        with open(config_path, \"w\") as f:\n            f.write(config_content_str)\n\n        print(f\"Successfully created '{config_path}'.\")\n        print(\"You can now configure your plugins in this file.\")\n    except OSError as e:\n        logger.error(f\"Error writing config file '{config_path}': {e}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_launch_command", "title": "handle_launch_command  <code>async</code>", "text": "<pre><code>handle_launch_command(args_ns)\n</code></pre> <p>Handles the 'launch' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>async def handle_launch_command(args_ns):\n    \"\"\"Handles the 'launch' command.\"\"\"\n    logger.debug(\"Launch command started.\")\n\n    try:\n        app = _get_configured_app(args_ns.app, args_ns)\n\n        if args_ns.dry_run:\n            print(\"=== Dry Run Mode ===\")\n            print(\"Application loaded successfully. Server would start with:\")\n            print(f\"  Host: {args_ns.host}\")\n            print(f\"  Port: {args_ns.port}\")\n            print(f\"  Reload: {args_ns.reload}\")\n            print(f\"  Workers: {args_ns.workers}\")\n            return\n\n        # Configure uvicorn\n        uvicorn_config = {\n            \"app\": app,\n            \"host\": args_ns.host,\n            \"port\": args_ns.port,\n            \"reload\": args_ns.reload,\n            \"workers\": args_ns.workers\n            if not args_ns.reload\n            else 1,  # Reload doesn't work with multiple workers\n        }\n\n        if args_ns.factory:\n            # If factory mode, we need to pass the app as a string\n            if args_ns.app:\n                uvicorn_config[\"app\"] = args_ns.app\n            else:\n                uvicorn_config[\"app\"] = \"serv.app:App\"\n\n        logger.info(f\"Starting Serv application on {args_ns.host}:{args_ns.port}\")\n\n        # Start the server\n        server = uvicorn.Server(uvicorn.Config(**uvicorn_config))\n        await server.serve()\n\n    except Exception as e:\n        logger.error(f\"Error launching application: {e}\")\n        sys.exit(1)\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_list_plugin_command", "title": "handle_list_plugin_command", "text": "<pre><code>handle_list_plugin_command(args_ns)\n</code></pre> <p>Handles the 'list plugin' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_list_plugin_command(args_ns):\n    \"\"\"Handles the 'list plugin' command.\"\"\"\n    logger.debug(\"List plugin command started.\")\n\n    config_path = Path.cwd() / DEFAULT_CONFIG_FILE\n\n    if args_ns.available:\n        # Show all available plugins in the plugins directory\n        plugins_dir = Path.cwd() / \"plugins\"\n        if not plugins_dir.exists():\n            print(\"No plugins directory found.\")\n            return\n\n        available_plugins = []\n        for plugin_dir in plugins_dir.iterdir():\n            if (\n                plugin_dir.is_dir()\n                and not plugin_dir.name.startswith(\"_\")\n                and (plugin_dir / \"plugin.yaml\").exists()\n            ):\n                try:\n                    with open(plugin_dir / \"plugin.yaml\") as f:\n                        plugin_meta = yaml.safe_load(f) or {}\n\n                    plugin_name = plugin_meta.get(\"name\", plugin_dir.name)\n                    plugin_version = plugin_meta.get(\"version\", \"Unknown\")\n                    plugin_description = plugin_meta.get(\n                        \"description\", \"No description\"\n                    )\n\n                    available_plugins.append(\n                        {\n                            \"dir_name\": plugin_dir.name,\n                            \"name\": plugin_name,\n                            \"version\": plugin_version,\n                            \"description\": plugin_description,\n                        }\n                    )\n                except Exception as e:\n                    logger.warning(\n                        f\"Error reading plugin metadata for '{plugin_dir.name}': {e}\"\n                    )\n                    available_plugins.append(\n                        {\n                            \"dir_name\": plugin_dir.name,\n                            \"name\": plugin_dir.name,\n                            \"version\": \"Unknown\",\n                            \"description\": \"Error reading metadata\",\n                        }\n                    )\n\n        if not available_plugins:\n            print(\"No plugins found in the plugins directory.\")\n            return\n\n        print(f\"Available plugins ({len(available_plugins)}):\")\n        for plugin in available_plugins:\n            print(f\"  • {plugin['name']} (v{plugin['version']}) [{plugin['dir_name']}]\")\n            print(f\"    {plugin['description']}\")\n    else:\n        # Show enabled plugins from config\n        if not config_path.exists():\n            print(f\"Configuration file '{config_path}' not found.\")\n            print(\"Run 'serv app init' to create a configuration file.\")\n            return\n\n        try:\n            with open(config_path) as f:\n                config = yaml.safe_load(f) or {}\n        except Exception as e:\n            logger.error(f\"Error reading config file '{config_path}': {e}\")\n            return\n\n        plugins = config.get(\"plugins\", [])\n\n        if not plugins:\n            print(\"No plugins are currently enabled.\")\n            print(\"Use 'serv plugin enable &lt;plugin&gt;' to enable a plugin.\")\n            return\n\n        print(f\"Enabled plugins ({len(plugins)}):\")\n        for plugin_entry in plugins:\n            if isinstance(plugin_entry, dict):\n                plugin_id = plugin_entry.get(\"plugin\", \"Unknown\")\n                plugin_config = plugin_entry.get(\"config\", {})\n                config_info = \" (with config)\" if plugin_config else \"\"\n            else:\n                plugin_id = plugin_entry\n                config_info = \"\"\n\n            # Try to get human-readable name from plugin metadata\n            plugin_name = plugin_id\n            plugin_version = \"Unknown\"\n\n            # Check if this is a directory-based plugin\n            plugins_dir = Path.cwd() / \"plugins\"\n            if plugins_dir.exists():\n                # Extract directory name from plugin_id (handle both simple names and module paths)\n                if \":\" in plugin_id:\n                    # Full module path like \"test_plugin.test_plugin:TestPlugin\"\n                    module_path = plugin_id.split(\":\")[0]\n                    dir_name = module_path.split(\".\")[0]\n                else:\n                    # Simple name or just module path\n                    dir_name = plugin_id.split(\".\")[0]\n\n                # Try to find the plugin directory\n                plugin_dir = plugins_dir / dir_name\n                if (\n                    plugin_dir.exists()\n                    and plugin_dir.is_dir()\n                    and (plugin_dir / \"plugin.yaml\").exists()\n                ):\n                    try:\n                        with open(plugin_dir / \"plugin.yaml\") as f:\n                            plugin_meta = yaml.safe_load(f) or {}\n                        plugin_name = plugin_meta.get(\"name\", plugin_id)\n                        plugin_version = plugin_meta.get(\"version\", \"Unknown\")\n                    except Exception:\n                        pass\n\n            print(f\"  • {plugin_name} (v{plugin_version}) [{plugin_id}]{config_info}\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_shell_command", "title": "handle_shell_command", "text": "<pre><code>handle_shell_command(args_ns)\n</code></pre> <p>Handles the 'shell' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_shell_command(args_ns):\n    \"\"\"Handles the 'shell' command.\"\"\"\n    logger.debug(\"Shell command started.\")\n\n    print(\"🐍 Starting interactive Python shell...\")\n\n    # Prepare the shell environment\n    shell_locals = {\"__name__\": \"__console__\", \"__doc__\": None}\n\n    if not args_ns.no_startup:\n        try:\n            print(\"📦 Loading Serv app context...\")\n            app = _get_configured_app(args_ns.app, args_ns)\n            shell_locals.update(\n                {\n                    \"app\": app,\n                    \"serv\": importlib.import_module(\"serv\"),\n                    \"Path\": Path,\n                    \"yaml\": yaml,\n                }\n            )\n\n            # Add plugins to shell context\n            if hasattr(app, \"_plugins\"):\n                all_plugins = []\n                for plugin_list in app._plugins.values():\n                    all_plugins.extend(plugin_list)\n                shell_locals[\"plugins\"] = all_plugins\n                print(f\"🔌 Loaded {len(all_plugins)} plugins into context\")\n\n            print(\"✅ App context loaded successfully\")\n            print(\"Available objects: app, serv, plugins, Path, yaml\")\n\n        except Exception as e:\n            logger.warning(f\"Could not load app context: {e}\")\n            print(\"⚠️  App context not available, starting basic shell\")\n\n    # Try to use IPython if available and requested\n    if args_ns.ipython:\n        try:\n            from IPython import start_ipython\n\n            print(\"🎨 Starting IPython shell...\")\n            start_ipython(argv=[], user_ns=shell_locals)\n            return\n        except ImportError:\n            print(\"⚠️  IPython not available, falling back to standard shell\")\n\n    # Use standard Python shell\n    import code\n\n    print(\"🐍 Starting Python shell...\")\n    print(\"Type 'exit()' or Ctrl+D to exit\")\n\n    shell = code.InteractiveConsole(locals=shell_locals)\n    shell.interact(banner=\"\")\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_test_command", "title": "handle_test_command", "text": "<pre><code>handle_test_command(args_ns)\n</code></pre> <p>Handles the 'test' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_test_command(args_ns):\n    \"\"\"Handles the 'test' command.\"\"\"\n    logger.debug(\"Test command started.\")\n\n    # Check if pytest is available\n    try:\n        import pytest\n    except ImportError:\n        print(\"❌ pytest is not installed. Install it with: pip install pytest\")\n        return False\n\n    print(\"🧪 Running tests...\")\n\n    # Build pytest command\n    pytest_args = []\n\n    # Determine what to test\n    if args_ns.test_path:\n        pytest_args.append(args_ns.test_path)\n    elif args_ns.plugins:\n        # Look for plugin tests\n        plugins_dir = Path.cwd() / \"plugins\"\n        if plugins_dir.exists():\n            plugin_test_paths = []\n            for plugin_dir in plugins_dir.iterdir():\n                if plugin_dir.is_dir() and not plugin_dir.name.startswith(\"_\"):\n                    test_files = list(plugin_dir.glob(\"test_*.py\")) + list(\n                        plugin_dir.glob(\"*_test.py\")\n                    )\n                    if test_files:\n                        plugin_test_paths.extend(str(f) for f in test_files)\n\n            if plugin_test_paths:\n                pytest_args.extend(plugin_test_paths)\n                print(f\"📦 Found {len(plugin_test_paths)} plugin test files\")\n            else:\n                print(\"ℹ️  No plugin tests found\")\n                return True\n        else:\n            print(\"⚠️  No plugins directory found\")\n            return True\n    elif args_ns.e2e:\n        # Run e2e tests\n        e2e_dir = Path.cwd() / \"tests\" / \"e2e\"\n        if e2e_dir.exists():\n            pytest_args.append(str(e2e_dir))\n            print(\"🌐 Running end-to-end tests\")\n        else:\n            print(\"⚠️  No e2e tests directory found\")\n            return True\n    else:\n        # Run all tests\n        test_dir = Path.cwd() / \"tests\"\n        if test_dir.exists():\n            pytest_args.append(str(test_dir))\n            print(\"🔍 Running all tests\")\n        else:\n            print(\"⚠️  No tests directory found\")\n            return True\n\n    # Add coverage if requested\n    if args_ns.coverage:\n        try:\n            import importlib.util\n\n            if importlib.util.find_spec(\"pytest_cov\") is not None:\n                pytest_args.extend(\n                    [\"--cov=.\", \"--cov-report=html\", \"--cov-report=term\"]\n                )\n                print(\"📊 Coverage reporting enabled\")\n            else:\n                print(\"⚠️  pytest-cov not installed, skipping coverage reporting\")\n        except ImportError:\n            print(\n                \"⚠️  pytest-cov not installed, skipping coverage. Install with: pip install pytest-cov\"\n            )\n\n    # Add verbose if requested\n    if args_ns.verbose:\n        pytest_args.append(\"-v\")\n\n    # Run pytest\n    try:\n        print(f\"Running: pytest {' '.join(pytest_args)}\")\n        exit_code = pytest.main(pytest_args)\n\n        if exit_code == 0:\n            print(\"✅ All tests passed!\")\n        else:\n            print(f\"❌ Tests failed with exit code {exit_code}\")\n\n        return exit_code == 0\n\n    except Exception as e:\n        logger.error(f\"Error running tests: {e}\")\n        return False\n</code></pre>"}, {"location": "reference/serv/cli/commands/#serv.cli.commands.handle_validate_plugin_command", "title": "handle_validate_plugin_command", "text": "<pre><code>handle_validate_plugin_command(args_ns)\n</code></pre> <p>Handles the 'plugin validate' command.</p> Source code in <code>serv/cli/commands.py</code> <pre><code>def handle_validate_plugin_command(args_ns):\n    \"\"\"Handles the 'plugin validate' command.\"\"\"\n    logger.debug(\"Plugin validate command started.\")\n\n    plugins_dir = Path.cwd() / \"plugins\"\n    if not plugins_dir.exists():\n        print(\"❌ No plugins directory found.\")\n        return False\n\n    # Determine which plugins to validate\n    if args_ns.plugin_identifier and not args_ns.all:\n        # Validate specific plugin\n        plugin_dirs = []\n        plugin_dir = plugins_dir / args_ns.plugin_identifier\n        if plugin_dir.exists() and plugin_dir.is_dir():\n            plugin_dirs = [plugin_dir]\n        else:\n            print(f\"❌ Plugin '{args_ns.plugin_identifier}' not found.\")\n            return False\n    else:\n        # Validate all plugins\n        plugin_dirs = [\n            d\n            for d in plugins_dir.iterdir()\n            if d.is_dir() and not d.name.startswith(\"_\")\n        ]\n\n    if not plugin_dirs:\n        print(\"ℹ️  No plugins found to validate.\")\n        return True\n\n    print(f\"=== Validating {len(plugin_dirs)} Plugin(s) ===\")\n\n    total_issues = 0\n\n    for plugin_dir in plugin_dirs:\n        print(f\"\\n🔍 Validating plugin: {plugin_dir.name}\")\n        issues = 0\n\n        # Check for plugin.yaml\n        plugin_yaml = plugin_dir / \"plugin.yaml\"\n        if not plugin_yaml.exists():\n            print(\"❌ Missing plugin.yaml\")\n            issues += 1\n        else:\n            try:\n                with open(plugin_yaml) as f:\n                    plugin_config = yaml.safe_load(f)\n\n                if not plugin_config:\n                    print(\"❌ plugin.yaml is empty\")\n                    issues += 1\n                else:\n                    print(\"✅ plugin.yaml is valid YAML\")\n\n                    # Check required fields\n                    required_fields = [\"name\", \"version\"]\n                    for field in required_fields:\n                        if field not in plugin_config:\n                            print(f\"❌ Missing required field: {field}\")\n                            issues += 1\n                        else:\n                            print(f\"✅ Has required field: {field}\")\n\n                    # Check optional but recommended fields\n                    recommended_fields = [\"description\", \"author\"]\n                    for field in recommended_fields:\n                        if field not in plugin_config:\n                            print(f\"⚠️  Missing recommended field: {field}\")\n                        else:\n                            print(f\"✅ Has recommended field: {field}\")\n\n                    # Validate version format\n                    version = plugin_config.get(\"version\", \"\")\n                    if (\n                        version\n                        and not version.replace(\".\", \"\")\n                        .replace(\"-\", \"\")\n                        .replace(\"_\", \"\")\n                        .isalnum()\n                    ):\n                        print(f\"⚠️  Version format may be invalid: {version}\")\n\n            except yaml.YAMLError as e:\n                print(f\"❌ plugin.yaml contains invalid YAML: {e}\")\n                issues += 1\n            except Exception as e:\n                print(f\"❌ Error reading plugin.yaml: {e}\")\n                issues += 1\n\n        # Check for __init__.py\n        init_file = plugin_dir / \"__init__.py\"\n        if not init_file.exists():\n            print(\"⚠️  Missing __init__.py (recommended for Python packages)\")\n        else:\n            print(\"✅ Has __init__.py\")\n\n        # Check for Python files\n        py_files = list(plugin_dir.glob(\"*.py\"))\n        if not py_files:\n            print(\"❌ No Python files found\")\n            issues += 1\n        else:\n            print(f\"✅ Found {len(py_files)} Python file(s)\")\n\n            # Check for main plugin file (matching directory name)\n            expected_main_file = plugin_dir / f\"{plugin_dir.name}.py\"\n            if expected_main_file.exists():\n                print(f\"✅ Has main plugin file: {expected_main_file.name}\")\n            else:\n                print(f\"⚠️  No main plugin file found (expected: {plugin_dir.name}.py)\")\n\n        # Check for common issues\n        if (plugin_dir / \"main.py\").exists() and not expected_main_file.exists():\n            print(\n                f\"⚠️  Found main.py but expected {plugin_dir.name}.py (consider renaming)\"\n            )\n\n        # Try to import the plugin (basic syntax check)\n        if py_files:\n            try:\n                # This is a basic check - we're not actually importing to avoid side effects\n                for py_file in py_files:\n                    with open(py_file) as f:\n                        content = f.read()\n\n                    # Basic syntax check\n                    try:\n                        compile(content, str(py_file), \"exec\")\n                        print(f\"✅ {py_file.name} has valid Python syntax\")\n                    except SyntaxError as e:\n                        print(f\"❌ {py_file.name} has syntax error: {e}\")\n                        issues += 1\n\n            except Exception as e:\n                print(f\"⚠️  Could not perform syntax check: {e}\")\n\n        if issues == 0:\n            print(f\"🎉 Plugin '{plugin_dir.name}' validation passed!\")\n        else:\n            print(f\"⚠️  Plugin '{plugin_dir.name}' has {issues} issue(s)\")\n\n        total_issues += issues\n\n    print(\"\\n=== Validation Summary ===\")\n    if total_issues == 0:\n        print(\"🎉 All plugins passed validation!\")\n    else:\n        print(f\"⚠️  Found {total_issues} total issue(s) across all plugins\")\n\n    return total_issues == 0\n</code></pre>"}, {"location": "reference/serv/cli/main/", "title": "main", "text": ""}, {"location": "reference/serv/cli/main/#serv.cli.main", "title": "serv.cli.main", "text": "<p>Main CLI entry point.</p> <p>This module contains the main function that orchestrates the CLI execution.</p>"}, {"location": "reference/serv/cli/main/#serv.cli.main.main", "title": "main", "text": "<pre><code>main()\n</code></pre> <p>Main CLI entry point.</p> Source code in <code>serv/cli/main.py</code> <pre><code>def main():\n    \"\"\"Main CLI entry point.\"\"\"\n    parser, launch_parser = create_parser()\n\n    # Process args\n    args_ns = parser.parse_args()\n\n    if args_ns.debug or os.getenv(\"SERV_DEBUG\"):\n        os.environ[\"SERV_DEBUG\"] = \"1\"\n        logger.setLevel(logging.DEBUG)\n        logger.debug(\"Debug logging enabled.\")\n\n    current_args_to_use = args_ns\n\n    if not hasattr(args_ns, \"command\") or args_ns.command is None:\n        # No command specified, default to 'launch'\n        non_command_cli_args = sys.argv[1:]\n        logger.debug(\n            f\"No command specified. Defaulting to 'launch'. Using CLI args: {non_command_cli_args}\"\n        )\n        try:\n            launch_specific_args = launch_parser.parse_args(non_command_cli_args)\n            for global_arg_name in [\"debug\", \"version\"]:\n                if hasattr(args_ns, global_arg_name):\n                    setattr(\n                        launch_specific_args,\n                        global_arg_name,\n                        getattr(args_ns, global_arg_name),\n                    )\n            current_args_to_use = launch_specific_args\n            current_args_to_use.func = handle_launch_command  # Ensure func is set\n        except SystemExit:\n            # If there's a parsing error, let's use the original args to show help\n            parser.print_help()\n            sys.exit(1)\n\n    if hasattr(current_args_to_use, \"func\"):\n        # Use async if the handler is async\n        handler = current_args_to_use.func\n        if asyncio.iscoroutinefunction(handler):\n            asyncio.run(handler(current_args_to_use))\n        else:\n            handler(current_args_to_use)\n    else:\n        # No command found, show help\n        parser.print_help()\n</code></pre>"}, {"location": "reference/serv/cli/parser/", "title": "parser", "text": ""}, {"location": "reference/serv/cli/parser/#serv.cli.parser", "title": "serv.cli.parser", "text": "<p>CLI argument parser.</p> <p>This module contains the argument parser setup for the Serv CLI.</p>"}, {"location": "reference/serv/cli/parser/#serv.cli.parser.create_parser", "title": "create_parser", "text": "<pre><code>create_parser()\n</code></pre> <p><PERSON>reate and configure the CLI argument parser.</p> Source code in <code>serv/cli/parser.py</code> <pre><code>def create_parser():\n    \"\"\"Create and configure the CLI argument parser.\"\"\"\n    parser = argparse.ArgumentParser(\n        prog=\"serv\", description=\"Command-line interface for the Serv web framework.\"\n    )\n    serv_version = \"0.1.0-dev\"  # Placeholder\n\n    parser.add_argument(\n        \"--version\", action=\"version\", version=f\"%(prog)s {serv_version}\"\n    )\n    parser.add_argument(\n        \"--debug\",\n        action=\"store_true\",\n        help=\"Enable debug logging for Serv CLI and potentially the app.\",\n    )\n    parser.add_argument(\n        \"--app\",\n        \"-a\",\n        help='Custom application CLASS in the format \"module.path:ClassName\". If not provided, Serv\\'s default App is'\n        \" used.\",\n        default=None,  # Default is to use serv.app.App\n    )\n    parser.add_argument(\n        \"--config\",\n        \"-c\",\n        help=f\"Path to config file. Default: ./{DEFAULT_CONFIG_FILE} or App default.\",\n        default=None,  # App will handle its default if this is None\n    )\n    parser.add_argument(\n        \"--plugin-dirs\",  # Name changed for consistency, was plugin_dirs before\n        help=\"Directory to search for plugins. Default: ./plugins or App default.\",\n        default=None,  # App will handle its default\n    )\n\n    # Subparsers for subcommands\n    subparsers = parser.add_subparsers(\n        title=\"commands\", dest=\"command\", required=False, help=\"Command to execute\"\n    )\n\n    # Launch parser\n    launch_parser = subparsers.add_parser(\"launch\", help=\"Launch the Serv application.\")\n    launch_parser.add_argument(\n        \"--host\",\n        help=\"Bind socket to this host. Default: 127.0.0.1\",\n        default=\"127.0.0.1\",\n    )\n    launch_parser.add_argument(\n        \"--port\",\n        \"-p\",\n        type=int,\n        help=\"Bind socket to this port. Default: 8000\",\n        default=8000,\n    )\n    launch_parser.add_argument(\n        \"--reload\", action=\"store_true\", help=\"Enable auto-reload.\"\n    )\n    launch_parser.add_argument(\n        \"--workers\",\n        \"-w\",\n        type=int,\n        help=\"Number of worker processes. Defaults to 1.\",\n        default=1,\n    )\n    launch_parser.add_argument(\n        \"--factory\",\n        action=\"store_true\",\n        help=\"Treat APP_MODULE as an application factory string (e.g., 'module:create_app').\",\n    )\n    launch_parser.add_argument(\n        \"--dry-run\",\n        action=\"store_true\",\n        help=\"Load and configure the app and plugins but don't start the server.\",\n    )\n    launch_parser.add_argument(\n        \"--dev\",\n        action=\"store_true\",\n        help=\"Enable development mode for the application.\",\n    )\n    launch_parser.set_defaults(func=handle_launch_command)\n\n    # Dev parser\n    dev_parser = subparsers.add_parser(\n        \"dev\", help=\"Start development server with enhanced features.\"\n    )\n    dev_parser.add_argument(\n        \"--host\",\n        help=\"Bind socket to this host. Default: 127.0.0.1\",\n        default=\"127.0.0.1\",\n    )\n    dev_parser.add_argument(\n        \"--port\",\n        \"-p\",\n        type=int,\n        help=\"Bind socket to this port. Default: 8000\",\n        default=8000,\n    )\n    dev_parser.add_argument(\n        \"--no-reload\",\n        action=\"store_true\",\n        help=\"Disable auto-reload (enabled by default in dev mode).\",\n    )\n    dev_parser.add_argument(\n        \"--workers\",\n        \"-w\",\n        type=int,\n        help=\"Number of worker processes. Defaults to 1 (reload disabled with multiple workers).\",\n        default=1,\n    )\n    dev_parser.set_defaults(func=handle_dev_command)\n\n    # Test parser\n    test_parser = subparsers.add_parser(\n        \"test\", help=\"Run tests for the application and plugins.\"\n    )\n    test_parser.add_argument(\n        \"--plugins\", action=\"store_true\", help=\"Run plugin tests only\"\n    )\n    test_parser.add_argument(\n        \"--e2e\", action=\"store_true\", help=\"Run end-to-end tests only\"\n    )\n    test_parser.add_argument(\n        \"--coverage\", action=\"store_true\", help=\"Generate coverage report\"\n    )\n    test_parser.add_argument(\n        \"--verbose\", \"-v\", action=\"store_true\", help=\"Verbose test output\"\n    )\n    test_parser.add_argument(\n        \"test_path\", nargs=\"?\", help=\"Specific test file or directory to run\"\n    )\n    test_parser.set_defaults(func=handle_test_command)\n\n    # Shell parser\n    shell_parser = subparsers.add_parser(\n        \"shell\", help=\"Start interactive Python shell with app context.\"\n    )\n    shell_parser.add_argument(\n        \"--ipython\", action=\"store_true\", help=\"Use IPython if available\"\n    )\n    shell_parser.add_argument(\n        \"--no-startup\", action=\"store_true\", help=\"Skip loading app context\"\n    )\n    shell_parser.set_defaults(func=handle_shell_command)\n\n    # Config commands\n    config_parser = subparsers.add_parser(\n        \"config\", help=\"Configuration management commands\"\n    )\n    config_subparsers = config_parser.add_subparsers(\n        title=\"config commands\",\n        dest=\"config_command\",\n        required=True,\n        help=\"Config command to execute\",\n    )\n\n    # Config show command\n    config_show_parser = config_subparsers.add_parser(\n        \"show\", help=\"Display current configuration\"\n    )\n    config_show_parser.add_argument(\n        \"--format\", choices=[\"yaml\", \"json\"], default=\"yaml\", help=\"Output format\"\n    )\n    config_show_parser.set_defaults(func=handle_config_show_command)\n\n    # Config validate command\n    config_validate_parser = config_subparsers.add_parser(\n        \"validate\", help=\"Validate configuration file\"\n    )\n    config_validate_parser.set_defaults(func=handle_config_validate_command)\n\n    # Config get command\n    config_get_parser = config_subparsers.add_parser(\n        \"get\", help=\"Get configuration value\"\n    )\n    config_get_parser.add_argument(\n        \"key\", help=\"Configuration key (dot notation supported)\"\n    )\n    config_get_parser.set_defaults(func=handle_config_get_command)\n\n    # Config set command\n    config_set_parser = config_subparsers.add_parser(\n        \"set\", help=\"Set configuration value\"\n    )\n    config_set_parser.add_argument(\n        \"key\", help=\"Configuration key (dot notation supported)\"\n    )\n    config_set_parser.add_argument(\"value\", help=\"Configuration value\")\n    config_set_parser.add_argument(\n        \"--type\",\n        choices=[\"string\", \"int\", \"float\", \"bool\", \"list\"],\n        default=\"string\",\n        help=\"Value type\",\n    )\n    config_set_parser.set_defaults(func=handle_config_set_command)\n\n    # Plugin commands\n    plugin_parser = subparsers.add_parser(\"plugin\", help=\"Plugin management commands\")\n    plugin_subparsers = plugin_parser.add_subparsers(\n        title=\"plugin commands\",\n        dest=\"plugin_command\",\n        required=True,\n        help=\"Plugin command to execute\",\n    )\n\n    # Plugin enable command\n    plugin_enable_parser = plugin_subparsers.add_parser(\n        \"enable\", help=\"Enable a plugin\"\n    )\n    plugin_enable_parser.add_argument(\n        \"plugin_identifier\", help=\"Plugin identifier (directory name or module path)\"\n    )\n    plugin_enable_parser.set_defaults(func=handle_enable_plugin_command)\n\n    # Plugin disable command\n    plugin_disable_parser = plugin_subparsers.add_parser(\n        \"disable\", help=\"Disable a plugin\"\n    )\n    plugin_disable_parser.add_argument(\n        \"plugin_identifier\", help=\"Plugin identifier (directory name or module path)\"\n    )\n    plugin_disable_parser.set_defaults(func=handle_disable_plugin_command)\n\n    # Plugin list command\n    plugin_list_parser = plugin_subparsers.add_parser(\n        \"list\", help=\"List available and enabled plugins\"\n    )\n    plugin_list_parser.add_argument(\n        \"--available\",\n        action=\"store_true\",\n        help=\"Show all available plugins (default shows enabled plugins)\",\n    )\n    plugin_list_parser.set_defaults(func=handle_list_plugin_command)\n\n    # Plugin validate command\n    plugin_validate_parser = plugin_subparsers.add_parser(\n        \"validate\", help=\"Validate plugin structure and configuration\"\n    )\n    plugin_validate_parser.add_argument(\n        \"plugin_identifier\",\n        nargs=\"?\",\n        help=\"Plugin identifier (directory name or module path). If not provided, validates all plugins.\",\n    )\n    plugin_validate_parser.add_argument(\n        \"--all\", action=\"store_true\", help=\"Validate all plugins\"\n    )\n    plugin_validate_parser.set_defaults(func=handle_validate_plugin_command)\n\n    # Create commands\n    create_parser = subparsers.add_parser(\n        \"create\", help=\"Create apps, plugins and components\"\n    )\n    create_subparsers = create_parser.add_subparsers(\n        title=\"create commands\",\n        dest=\"create_command\",\n        required=True,\n        help=\"Item to create\",\n    )\n\n    # Create app command\n    create_app_parser = create_subparsers.add_parser(\n        \"app\", help=\"Initialize a new Serv project\"\n    )\n    create_app_parser.add_argument(\n        \"--force\", action=\"store_true\", help=\"Force overwrite of existing config file\"\n    )\n    create_app_parser.add_argument(\n        \"--non-interactive\",\n        action=\"store_true\",\n        dest=\"non_interactive\",\n        help=\"Non-interactive mode with default values (for testing)\",\n    )\n    create_app_parser.set_defaults(func=handle_init_command)\n\n    # Create plugin command\n    create_plugin_parser = create_subparsers.add_parser(\n        \"plugin\", help=\"Create a new plugin\"\n    )\n    create_plugin_parser.add_argument(\n        \"--name\", required=True, help=\"Name of the plugin\"\n    )\n    create_plugin_parser.add_argument(\n        \"--force\", action=\"store_true\", help=\"Force overwrite of existing plugin\"\n    )\n    create_plugin_parser.add_argument(\n        \"--non-interactive\",\n        action=\"store_true\",\n        dest=\"non_interactive\",\n        help=\"Non-interactive mode with default values (for testing)\",\n    )\n    create_plugin_parser.set_defaults(func=handle_create_plugin_command)\n\n    # Create entrypoint command\n    create_entrypoint_parser = create_subparsers.add_parser(\n        \"entrypoint\", help=\"Create a new plugin entrypoint\"\n    )\n    create_entrypoint_parser.add_argument(\n        \"--name\", required=True, help=\"Name of the entrypoint\"\n    )\n    create_entrypoint_parser.add_argument(\n        \"--plugin\",\n        help=\"Plugin to add the entrypoint to (auto-detected if not provided)\",\n    )\n    create_entrypoint_parser.add_argument(\n        \"--force\", action=\"store_true\", help=\"Force overwrite of existing files\"\n    )\n    create_entrypoint_parser.set_defaults(func=handle_create_entrypoint_command)\n\n    # Create route command\n    create_route_parser = create_subparsers.add_parser(\n        \"route\", help=\"Create a new plugin route\"\n    )\n    create_route_parser.add_argument(\"--name\", required=True, help=\"Name of the route\")\n    create_route_parser.add_argument(\n        \"--path\", help=\"URL path for the route (e.g., /users/{id}/profile)\"\n    )\n    create_route_parser.add_argument(\"--router\", help=\"Router name to add the route to\")\n    create_route_parser.add_argument(\n        \"--plugin\", help=\"Plugin to add the route to (auto-detected if not provided)\"\n    )\n    create_route_parser.add_argument(\n        \"--force\", action=\"store_true\", help=\"Force overwrite of existing files\"\n    )\n    create_route_parser.set_defaults(func=handle_create_route_command)\n\n    # Create middleware command\n    create_middleware_parser = create_subparsers.add_parser(\n        \"middleware\", help=\"Create a new plugin middleware\"\n    )\n    create_middleware_parser.add_argument(\n        \"--name\", required=True, help=\"Name of the middleware\"\n    )\n    create_middleware_parser.add_argument(\n        \"--plugin\",\n        help=\"Plugin to add the middleware to (auto-detected if not provided)\",\n    )\n    create_middleware_parser.add_argument(\n        \"--force\", action=\"store_true\", help=\"Force overwrite of existing files\"\n    )\n    create_middleware_parser.set_defaults(func=handle_create_middleware_command)\n\n    return parser, launch_parser\n</code></pre>"}, {"location": "reference/serv/cli/utils/", "title": "utils", "text": ""}, {"location": "reference/serv/cli/utils/#serv.cli.utils", "title": "serv.cli.utils", "text": "<p>CLI utility functions.</p> <p>This module contains helper functions used across the CLI commands.</p>"}, {"location": "reference/serv/cli/utils/#serv.cli.utils.prompt_user", "title": "prompt_user", "text": "<pre><code>prompt_user(text: str, default: str | None = None) -&gt; str\n</code></pre> <p>Prompts the user for input with an optional default value.</p> Source code in <code>serv/cli/utils.py</code> <pre><code>def prompt_user(text: str, default: str | None = None) -&gt; str:\n    \"\"\"Prompts the user for input with an optional default value.\"\"\"\n    prompt_text = f\"{text}\"\n    if default is not None:\n        prompt_text += f\" [{default}]\"\n    prompt_text += \": \"\n\n    while True:\n        response = input(prompt_text).strip()\n        if response:\n            return response\n        if default is not None:\n            return default\n</code></pre>"}, {"location": "reference/serv/cli/utils/#serv.cli.utils.resolve_plugin_module_string", "title": "resolve_plugin_module_string", "text": "<pre><code>resolve_plugin_module_string(\n    identifier: str, project_root: Path\n) -&gt; tuple[str | None, str | None]\n</code></pre> <p>Resolves a plugin identifier to its module string and name.</p> <p>Parameters:</p> Name Type Description Default <code>identifier</code> <code>str</code> <p>The plugin identifier (simple name or full module.path:Class).</p> required <code>project_root</code> <code>Path</code> <p>The root directory of the project (usually CWD).</p> required <p>Returns:</p> Type Description <code>str | None</code> <p>A tuple (module_string, plugin_name_human) or (None, None) if not found.</p> <code>str | None</code> <p>plugin_name_human is extracted from plugin.yaml if resolved via simple name.</p> Source code in <code>serv/cli/utils.py</code> <pre><code>def resolve_plugin_module_string(\n    identifier: str, project_root: Path\n) -&gt; tuple[str | None, str | None]:\n    \"\"\"Resolves a plugin identifier to its module string and name.\n\n    Args:\n        identifier: The plugin identifier (simple name or full module.path:Class).\n        project_root: The root directory of the project (usually CWD).\n\n    Returns:\n        A tuple (module_string, plugin_name_human) or (None, None) if not found.\n        plugin_name_human is extracted from plugin.yaml if resolved via simple name.\n    \"\"\"\n    plugins_dir = project_root / \"plugins\"\n    if \":\" in identifier:\n        # Assume it's a direct module string. We don't have a simple name here.\n        return (\n            identifier,\n            None,\n        )  # No simple name to derive human name from, user provided full path\n\n    # Simple name. Convert to snake_case for directory lookup.\n    dir_name = to_snake_case(identifier)\n    if not dir_name:\n        logger.error(\n            f\"Could not derive a valid directory name from identifier '{identifier}'.\"\n        )\n        return None, None\n\n    plugin_yaml_path = plugins_dir / dir_name / \"plugin.yaml\"\n\n    if not plugin_yaml_path.exists():\n        logger.warning(\n            f\"Plugin configuration '{plugin_yaml_path}' not found for simple name '{identifier}'.\"\n        )\n        logger.warning(\n            f\"Attempted to find it for directory '{dir_name}'. Ensure the plugin exists and the name is correct.\"\n        )\n        return None, None\n\n    try:\n        with open(plugin_yaml_path) as f:\n            plugin_meta = yaml.safe_load(f)\n        if not isinstance(plugin_meta, dict):\n            logger.error(\n                f\"Invalid YAML format in '{plugin_yaml_path}'. Expected a dictionary.\"\n            )\n            return None, None\n\n        entry_string = plugin_meta.get(\"entry\")\n        plugin_name_human = plugin_meta.get(\n            \"name\", identifier\n        )  # Fallback to identifier if name not in yaml\n\n        if not entry_string:\n            logger.error(f\"'entry' key not found in '{plugin_yaml_path}'.\")\n            return None, None\n        return entry_string, plugin_name_human\n    except Exception as e:\n        logger.error(f\"Error reading or parsing '{plugin_yaml_path}': {e}\")\n        return None, None\n</code></pre>"}, {"location": "reference/serv/cli/utils/#serv.cli.utils.to_pascal_case", "title": "to_pascal_case", "text": "<pre><code>to_pascal_case(name: str) -&gt; str\n</code></pre> <p>Converts a string to PascalCase.</p> Source code in <code>serv/cli/utils.py</code> <pre><code>def to_pascal_case(name: str) -&gt; str:\n    \"\"\"Converts a string to PascalCase.\"\"\"\n    name = name.replace(\"-\", \" \").replace(\"_\", \" \")\n    parts = name.split(\" \")\n    processed_parts = []\n    for part in parts:\n        if not part:\n            continue\n        # Handle 'v' followed by digit, e.g., v2 -&gt; V2\n        if len(part) &gt; 1 and part[0].lower() == \"v\" and part[1:].isdigit():\n            processed_parts.append(\"V\" + part[1:])\n        else:\n            processed_parts.append(part.capitalize())\n    return \"\".join(processed_parts)\n</code></pre>"}, {"location": "reference/serv/cli/utils/#serv.cli.utils.to_snake_case", "title": "to_snake_case", "text": "<pre><code>to_snake_case(name: str) -&gt; str\n</code></pre> <p>Converts a string to snake_case. Handles spaces, hyphens, and existing PascalCase/camelCase.</p> Source code in <code>serv/cli/utils.py</code> <pre><code>def to_snake_case(name: str) -&gt; str:\n    \"\"\"Converts a string to snake_case. Handles spaces, hyphens, and existing PascalCase/camelCase.\"\"\"\n    s = re.sub(r\"[\\s-]+\", \"_\", name)  # Replace spaces/hyphens with underscores\n    s = re.sub(\n        r\"(.)([A-Z][a-z]+)\", r\"\\1_\\2\", s\n    )  # Underscore before capital if followed by lowercase\n    s = re.sub(\n        r\"([a-z0-9])([A-Z])\", r\"\\1_\\2\", s\n    ).lower()  # Underscore before capital if followed by lowercase/digit\n    s = re.sub(r\"_+\", \"_\", s)  # Consolidate multiple underscores\n    s = s.strip(\"_\")  # Remove leading/trailing underscores\n    return s\n</code></pre>"}, {"location": "reference/serv/plugins/", "title": "plugins", "text": ""}, {"location": "reference/serv/plugins/#serv.plugins", "title": "serv.plugins", "text": ""}, {"location": "reference/serv/plugins/#serv.plugins.Plugin", "title": "Plugin", "text": "<pre><code>Plugin(\n    *,\n    plugin_spec: PluginSpec | None = None,\n    stand_alone: bool = False,\n)\n</code></pre> <p>Initialize the plugin.</p> <p>Loads plugin configuration and sets up any defined routers and routes if they are configured in the plugin.yaml file.</p> <p>Parameters:</p> Name Type Description Default <code>stand_alone</code> <code>bool</code> <p>If True, don't attempt to load plugin.yaml</p> <code>False</code> Source code in <code>serv/plugins/plugins.py</code> <pre><code>def __init__(\n    self, *, plugin_spec: \"pl.PluginSpec | None\" = None, stand_alone: bool = False\n):\n    \"\"\"Initialize the plugin.\n\n    Loads plugin configuration and sets up any defined routers and routes\n    if they are configured in the plugin.yaml file.\n\n    Args:\n        stand_alone: If True, don't attempt to load plugin.yaml\n    \"\"\"\n    self._stand_alone = stand_alone\n    if plugin_spec:\n        self.__plugin_spec__ = plugin_spec\n    else:\n        module = sys.modules[self.__module__]\n        if not hasattr(module, \"__plugin_spec__\"):\n            raise Exception(\n                f\"Plugin {self.__class__.__name__} does not exist in a plugin package. No plugin.yaml found in \"\n                f\"parent directories.\"\n            )\n        self.__plugin_spec__ = module.__plugin_spec__\n</code></pre>"}, {"location": "reference/serv/plugins/#serv.plugins.Plugin.on", "title": "on  <code>async</code>", "text": "<pre><code>on(\n    event_name: str,\n    container: Container | None = None,\n    *args: Any,\n    **kwargs: Any,\n) -&gt; None\n</code></pre> <p>Receives event notifications.</p> <p>This method will be called by the application when an event this plugin is registered for occurs. Subclasses should implement this method to handle specific events.</p> <p>Parameters:</p> Name Type Description Default <code>event_name</code> <code>str</code> <p>The name of the event that occurred.</p> required <code>**kwargs</code> <code>Any</code> <p>Arbitrary keyword arguments associated with the event.</p> <code>{}</code> Source code in <code>serv/plugins/plugins.py</code> <pre><code>async def on(\n    self,\n    event_name: str,\n    container: Container | None = None,\n    *args: Any,\n    **kwargs: Any,\n) -&gt; None:\n    \"\"\"Receives event notifications.\n\n    This method will be called by the application when an event this plugin\n    is registered for occurs. Subclasses should implement this method to handle\n    specific events.\n\n    Args:\n        event_name: The name of the event that occurred.\n        **kwargs: Arbitrary keyword arguments associated with the event.\n    \"\"\"\n    event_name = re.sub(r\"[^a-z0-9]+\", \"_\", event_name.lower())\n    for plugin_handler_name in self.__plugins__[event_name]:\n        callback = getattr(self, plugin_handler_name)\n        result = get_container(container).call(callback, *args, **kwargs)\n        if isawaitable(result):\n            await result\n</code></pre>"}, {"location": "reference/serv/plugins/#serv.plugins.ServMiddleware", "title": "ServMiddleware", "text": "<pre><code>ServMiddleware(\n    config: dict | None = None,\n    container: Container = dependency(),\n)\n</code></pre> <p>Base class for Serv middleware, providing enter, leave, and on_error hooks.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>def __init__(self, config: dict | None = None, container: Container = dependency()):\n    self._container = container\n    self._config = config\n</code></pre>"}, {"location": "reference/serv/plugins/#serv.plugins.ServMiddleware.enter", "title": "enter  <code>async</code>", "text": "<pre><code>enter()\n</code></pre> <p>Called before the request is processed further. Override to inspect/modify the request or return an early response.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def enter(self):\n    \"\"\"\n    Called before the request is processed further.\n    Override to inspect/modify the request or return an early response.\n    \"\"\"\n    pass\n</code></pre>"}, {"location": "reference/serv/plugins/#serv.plugins.ServMiddleware.leave", "title": "leave  <code>async</code>", "text": "<pre><code>leave()\n</code></pre> <p>Called after the request has been processed and a response is available. Override to inspect/modify the response. This is not called if an exception occurred during request processing.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def leave(self):\n    \"\"\"\n    Called after the request has been processed and a response is available.\n    Override to inspect/modify the response. This is not called if an exception occurred\n    during request processing.\n    \"\"\"\n    pass\n</code></pre>"}, {"location": "reference/serv/plugins/#serv.plugins.ServMiddleware.on_error", "title": "on_error  <code>async</code>", "text": "<pre><code>on_error(exc: Exception)\n</code></pre> <p>Called if an exception occurs during request processing after 'enter'. Override to handle errors and optionally return a custom error response.</p> <p>The base implementation raises the exception again.</p> <p>Parameters:</p> Name Type Description Default <code>exc</code> <code>Exception</code> <p>The exception that occurred.</p> required Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def on_error(self, exc: Exception):\n    \"\"\"\n    Called if an exception occurs during request processing after 'enter'.\n    Override to handle errors and optionally return a custom error response.\n\n    The base implementation raises the exception again.\n\n    Args:\n        exc: The exception that occurred.\n    \"\"\"\n    raise exc\n</code></pre>"}, {"location": "reference/serv/plugins/importer/", "title": "importer", "text": ""}, {"location": "reference/serv/plugins/importer/#serv.plugins.importer", "title": "serv.plugins.importer", "text": "<p>Package importer utility for Serv.</p> <p>This module provides functionality to load packages from directories without modifying sys.path. Packages are loaded and namespaced with their respective folder names.</p>"}, {"location": "reference/serv/plugins/importer/#serv.plugins.importer.Importer", "title": "Importer", "text": "<pre><code>Importer(directory: Path | str, dotpath: str | None = None)\n</code></pre> <p>Loader for Serv packages.</p> <p>This class provides functionality to load packages/modules from a given package directory without modifying sys.path.</p> <p>Parameters:</p> Name Type Description Default <code>directory</code> <code>Path | str</code> <p>Directory to search for packages</p> required Source code in <code>serv/plugins/importer.py</code> <pre><code>def __init__(self, directory: Path | str, dotpath: str | None = None):\n    \"\"\"\n    Args:\n        directory: Directory to search for packages\n    \"\"\"\n    self.directory = Path(directory).resolve()\n    self._dotpath = dotpath\n    if not dotpath:\n        self._dotpath = self.directory.name\n        ImporterMetaPathFinder.inject(self.directory)\n</code></pre>"}, {"location": "reference/serv/plugins/importer/#serv.plugins.importer.Importer.load_module", "title": "load_module", "text": "<pre><code>load_module(module_path: DottedPath) -&gt; ModuleType\n</code></pre> <p>Imports a module from inside of the search directory package. This assumes that the dotted path directly correlates with the file structure and that the path is for a python file.</p> Source code in <code>serv/plugins/importer.py</code> <pre><code>def load_module(self, module_path: DottedPath) -&gt; ModuleType:\n    \"\"\"Imports a module from inside of the search directory package. This assumes that\n    the dotted path directly correlates with the file structure and that the path is\n    for a python file.\"\"\"\n    return importlib.import_module(f\"{self._dotpath}.{module_path}\")\n</code></pre>"}, {"location": "reference/serv/plugins/loader/", "title": "loader", "text": ""}, {"location": "reference/serv/plugins/loader/#serv.plugins.loader", "title": "serv.plugins.loader", "text": ""}, {"location": "reference/serv/plugins/loader/#serv.plugins.loader.PluginLoader", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "<pre><code>PluginLoader(app: App, plugin_loader: Importer)\n</code></pre> <p>Handles loading and management of plugins and middleware.</p> <p>Initialize the PluginLoader.</p> <p>Parameters:</p> Name Type Description Default <code>plugin_loader</code> <code>Importer</code> <p>Importer instance for loading plugin packages</p> required Source code in <code>serv/plugins/loader.py</code> <pre><code>def __init__(self, app: \"App\", plugin_loader: \"Importer\"):\n    \"\"\"Initialize the PluginLoader.\n\n    Args:\n        plugin_loader: Importer instance for loading plugin packages\n    \"\"\"\n    self._app = app\n    self._plugin_loader = plugin_loader\n</code></pre>"}, {"location": "reference/serv/plugins/loader/#serv.plugins.loader.PluginLoader.load_plugin", "title": "load_plugin", "text": "<pre><code>load_plugin(\n    plugin_import: str,\n    app_plugin_settings: dict[str, Any] | None = None,\n) -&gt; tuple[PluginSpec | None, list[Exception]]\n</code></pre> <p>Load a single plugin.</p> <p>Parameters:</p> Name Type Description Default <code>plugin_import</code> <code>str</code> <p>Dot-separated import path to the plugin</p> required <p>Returns:</p> Type Description <code>tuple[PluginSpec | None, list[Exception]]</code> <p>Tuple of (plugin_spec, exceptions)</p> Source code in <code>serv/plugins/loader.py</code> <pre><code>def load_plugin(\n    self, plugin_import: str, app_plugin_settings: dict[str, Any] | None = None\n) -&gt; tuple[PluginSpec | None, list[Exception]]:\n    \"\"\"Load a single plugin.\n\n    Args:\n        plugin_import: Dot-separated import path to the plugin\n\n    Returns:\n        Tuple of (plugin_spec, exceptions)\n    \"\"\"\n    exceptions = []\n    try:\n        plugin_spec = self._load_plugin_spec(\n            plugin_import, app_plugin_settings or {}\n        )\n    except Exception as e:\n        e.add_note(f\" - Failed to load plugin spec for {plugin_import}\")\n        exceptions.append(e)\n        return None, exceptions\n\n    try:\n        _, failed_entry_points = self._load_plugin_entry_points(\n            plugin_spec.entry_points, plugin_import\n        )\n    except Exception as e:\n        e.add_note(f\" - Failed while loading entry points for {plugin_import}\")\n        exceptions.append(e)\n    else:\n        exceptions.extend(failed_entry_points)\n\n    try:\n        _, failed_middleware = self._load_plugin_middleware(\n            plugin_spec.middleware, plugin_import\n        )\n    except Exception as e:\n        e.add_note(f\" - Failed while loading middleware for {plugin_import}\")\n        exceptions.append(e)\n    else:\n        exceptions.extend(failed_middleware)\n\n    try:\n        self._setup_router_plugin(plugin_spec)\n    except Exception as e:\n        e.add_note(f\" - Failed while setting up router plugin for {plugin_import}\")\n        exceptions.append(e)\n\n    logger.info(f\"Loaded plugin {plugin_spec.name!r}\")\n    return plugin_spec, exceptions\n</code></pre>"}, {"location": "reference/serv/plugins/loader/#serv.plugins.loader.PluginLoader.load_plugins", "title": "load_plugins", "text": "<pre><code>load_plugins(\n    plugins_config: list[dict[str, Any]],\n) -&gt; tuple[\n    list[Plugin], list[Callable[[], AsyncIterator[None]]]\n]\n</code></pre> <p>Load plugins from a list of plugin configs.</p> <p>Parameters:</p> Name Type Description Default <code>plugins_config</code> <code>list[dict[str, Any]]</code> <p>List of plugin configs (usually from serv.config.yaml)</p> required <p>Returns:</p> Type Description <code>tuple[list[Plugin], list[Callable[[], AsyncIterator[None]]]]</code> <p>Tuple of (Plugin specs, Middleware iterators)</p> <p>Raises:</p> Type Description <code>ExceptionGroup</code> <p>If any errors occurred during loading</p> Source code in <code>serv/plugins/loader.py</code> <pre><code>def load_plugins(\n    self, plugins_config: list[dict[str, Any]]\n) -&gt; \"tuple[list[p.Plugin], list[Callable[[], AsyncIterator[None]]]]\":\n    \"\"\"Load plugins from a list of plugin configs.\n\n    Args:\n        plugins_config: List of plugin configs (usually from serv.config.yaml)\n\n    Returns:\n        Tuple of (Plugin specs, Middleware iterators)\n\n    Raises:\n        ExceptionGroup: If any errors occurred during loading\n    \"\"\"\n    exceptions = []\n    loaded_plugins = []\n    middleware_list = []\n    for plugin_settings in plugins_config:\n        try:\n            plugin_import, settings = self._process_app_plugin_settings(\n                plugin_settings\n            )\n            plugin_spec, plugin_exceptions = self.load_plugin(\n                plugin_import, settings\n            )\n\n        except Exception as e:\n            e.add_note(f\" - Failed to load plugin {plugin_settings}\")\n            exceptions.append(e)\n            continue\n        else:\n            if plugin_spec:\n                known_plugins[plugin_spec.path] = plugin_spec\n                loaded_plugins.append(plugin_spec)\n                middleware_list.extend(plugin_spec.middleware)\n\n            if plugin_exceptions:\n                exceptions.extend(plugin_exceptions)\n\n    if exceptions:\n        logger.warning(\n            f\"Encountered {len(exceptions)} errors during plugin and middleware loading.\"\n        )\n        raise ExceptionGroup(\n            \"Exceptions raised while loading plugins and middleware\", exceptions\n        )\n\n    return loaded_plugins, middleware_list\n</code></pre>"}, {"location": "reference/serv/plugins/loader/#serv.plugins.loader.get_package_location", "title": "get_package_location", "text": "<pre><code>get_package_location(package_name: str) -&gt; Path\n</code></pre> <p>Retrieves the filesystem path of a Python package/module without importing it.</p> <p>Parameters:</p> Name Type Description Default <code>package_name</code> <code>str</code> <p>Dot-separated module/package name (e.g., \"numpy\" or \"my_package.submodule\")</p> required <p>Returns:</p> Type Description <code>Path</code> <p>Absolute path to the package directory (for packages) or module file (for single-file modules)</p> <p>Raises:</p> Type Description <code>ValueError</code> <p>If the package/module isn't found or is a built-in</p> Source code in <code>serv/plugins/loader.py</code> <pre><code>def get_package_location(package_name: str) -&gt; Path:\n    \"\"\"\n    Retrieves the filesystem path of a Python package/module without importing it.\n\n    Args:\n        package_name: Dot-separated module/package name (e.g., \"numpy\" or \"my_package.submodule\")\n\n    Returns:\n        Absolute path to the package directory (for packages) or module file (for single-file modules)\n\n    Raises:\n        ValueError: If the package/module isn't found or is a built-in\n    \"\"\"\n    spec = importlib.util.find_spec(package_name)\n\n    if not spec:\n        raise ValueError(f\"'{package_name}' not found in Python path\")\n    if not spec.origin:\n        raise ValueError(f\"'{package_name}' is a built-in module with no file location\")\n\n    # Handle packages (multi-file)\n    if spec.submodule_search_locations:\n        return Path(spec.submodule_search_locations[0])\n\n    # Handle single-file modules\n    return Path(spec.origin).parent\n</code></pre>"}, {"location": "reference/serv/plugins/middleware/", "title": "middleware", "text": ""}, {"location": "reference/serv/plugins/middleware/#serv.plugins.middleware", "title": "serv.plugins.middleware", "text": ""}, {"location": "reference/serv/plugins/middleware/#serv.plugins.middleware.ServMiddleware", "title": "ServMiddleware", "text": "<pre><code>ServMiddleware(\n    config: dict | None = None,\n    container: Container = dependency(),\n)\n</code></pre> <p>Base class for Serv middleware, providing enter, leave, and on_error hooks.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>def __init__(self, config: dict | None = None, container: Container = dependency()):\n    self._container = container\n    self._config = config\n</code></pre>"}, {"location": "reference/serv/plugins/middleware/#serv.plugins.middleware.ServMiddleware.enter", "title": "enter  <code>async</code>", "text": "<pre><code>enter()\n</code></pre> <p>Called before the request is processed further. Override to inspect/modify the request or return an early response.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def enter(self):\n    \"\"\"\n    Called before the request is processed further.\n    Override to inspect/modify the request or return an early response.\n    \"\"\"\n    pass\n</code></pre>"}, {"location": "reference/serv/plugins/middleware/#serv.plugins.middleware.ServMiddleware.leave", "title": "leave  <code>async</code>", "text": "<pre><code>leave()\n</code></pre> <p>Called after the request has been processed and a response is available. Override to inspect/modify the response. This is not called if an exception occurred during request processing.</p> Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def leave(self):\n    \"\"\"\n    Called after the request has been processed and a response is available.\n    Override to inspect/modify the response. This is not called if an exception occurred\n    during request processing.\n    \"\"\"\n    pass\n</code></pre>"}, {"location": "reference/serv/plugins/middleware/#serv.plugins.middleware.ServMiddleware.on_error", "title": "on_error  <code>async</code>", "text": "<pre><code>on_error(exc: Exception)\n</code></pre> <p>Called if an exception occurs during request processing after 'enter'. Override to handle errors and optionally return a custom error response.</p> <p>The base implementation raises the exception again.</p> <p>Parameters:</p> Name Type Description Default <code>exc</code> <code>Exception</code> <p>The exception that occurred.</p> required Source code in <code>serv/plugins/middleware.py</code> <pre><code>async def on_error(self, exc: Exception):\n    \"\"\"\n    Called if an exception occurs during request processing after 'enter'.\n    Override to handle errors and optionally return a custom error response.\n\n    The base implementation raises the exception again.\n\n    Args:\n        exc: The exception that occurred.\n    \"\"\"\n    raise exc\n</code></pre>"}, {"location": "reference/serv/plugins/plugins/", "title": "plugins", "text": ""}, {"location": "reference/serv/plugins/plugins/#serv.plugins.plugins", "title": "serv.plugins.plugins", "text": "<p>Defines a base type that can observe events happening in the Serv app. Handlers are defined as methods on the class with names following the format '[optional_]on_{event_name}'. This gives the author the ability to make readable function names like 'set_role_on_user_create' or 'create_annotations_on_form_submit'.</p>"}, {"location": "reference/serv/plugins/plugins/#serv.plugins.plugins.Plugin", "title": "Plugin", "text": "<pre><code>Plugin(\n    *,\n    plugin_spec: PluginSpec | None = None,\n    stand_alone: bool = False,\n)\n</code></pre> <p>Initialize the plugin.</p> <p>Loads plugin configuration and sets up any defined routers and routes if they are configured in the plugin.yaml file.</p> <p>Parameters:</p> Name Type Description Default <code>stand_alone</code> <code>bool</code> <p>If True, don't attempt to load plugin.yaml</p> <code>False</code> Source code in <code>serv/plugins/plugins.py</code> <pre><code>def __init__(\n    self, *, plugin_spec: \"pl.PluginSpec | None\" = None, stand_alone: bool = False\n):\n    \"\"\"Initialize the plugin.\n\n    Loads plugin configuration and sets up any defined routers and routes\n    if they are configured in the plugin.yaml file.\n\n    Args:\n        stand_alone: If True, don't attempt to load plugin.yaml\n    \"\"\"\n    self._stand_alone = stand_alone\n    if plugin_spec:\n        self.__plugin_spec__ = plugin_spec\n    else:\n        module = sys.modules[self.__module__]\n        if not hasattr(module, \"__plugin_spec__\"):\n            raise Exception(\n                f\"Plugin {self.__class__.__name__} does not exist in a plugin package. No plugin.yaml found in \"\n                f\"parent directories.\"\n            )\n        self.__plugin_spec__ = module.__plugin_spec__\n</code></pre>"}, {"location": "reference/serv/plugins/plugins/#serv.plugins.plugins.Plugin.on", "title": "on  <code>async</code>", "text": "<pre><code>on(\n    event_name: str,\n    container: Container | None = None,\n    *args: Any,\n    **kwargs: Any,\n) -&gt; None\n</code></pre> <p>Receives event notifications.</p> <p>This method will be called by the application when an event this plugin is registered for occurs. Subclasses should implement this method to handle specific events.</p> <p>Parameters:</p> Name Type Description Default <code>event_name</code> <code>str</code> <p>The name of the event that occurred.</p> required <code>**kwargs</code> <code>Any</code> <p>Arbitrary keyword arguments associated with the event.</p> <code>{}</code> Source code in <code>serv/plugins/plugins.py</code> <pre><code>async def on(\n    self,\n    event_name: str,\n    container: Container | None = None,\n    *args: Any,\n    **kwargs: Any,\n) -&gt; None:\n    \"\"\"Receives event notifications.\n\n    This method will be called by the application when an event this plugin\n    is registered for occurs. Subclasses should implement this method to handle\n    specific events.\n\n    Args:\n        event_name: The name of the event that occurred.\n        **kwargs: Arbitrary keyword arguments associated with the event.\n    \"\"\"\n    event_name = re.sub(r\"[^a-z0-9]+\", \"_\", event_name.lower())\n    for plugin_handler_name in self.__plugins__[event_name]:\n        callback = getattr(self, plugin_handler_name)\n        result = get_container(container).call(callback, *args, **kwargs)\n        if isawaitable(result):\n            await result\n</code></pre>"}, {"location": "reference/serv/plugins/router_plugin/", "title": "router_plugin", "text": ""}, {"location": "reference/serv/plugins/router_plugin/#serv.plugins.router_plugin", "title": "serv.plugins.router_plugin", "text": ""}, {"location": "reference/serv/plugins/router_plugin/#serv.plugins.router_plugin.RouterPlugin", "title": "RouterPlugin", "text": "<pre><code>RouterPlugin(\n    *, plugin_spec: PluginSpec, stand_alone: bool = False\n)\n</code></pre> <p>               Bases: <code>Plugin</code></p> Source code in <code>serv/plugins/router_plugin.py</code> <pre><code>def __init__(self, *, plugin_spec: \"PluginSpec\", stand_alone: bool = False):\n    super().__init__(plugin_spec=plugin_spec, stand_alone=stand_alone)\n    self._routers: dict[str, RouterBuilder] = dict(\n        self._setup_routers(plugin_spec.routers)\n    )\n</code></pre>"}]}
{"permissions": {"allow": ["Bash(find:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python3:*)", "Bash(ruff check:*)", "Bash(python -m pytest tests/test_route_file_upload_validation.py::test_file_upload_no_file -xvs)", "Bash(python -m pytest --tb=short)", "Bash(grep:*)", "Bash(python -m pytest tests/test_routes_feature.py::test_parameter_injection_required_missing -xvs)", "Bash(python -m pytest tests/test_routes_feature.py::test_parameter_injection_with_default_fallback -xvs)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(python:*)"]}, "enableAllProjectMcpServers": false}